# Deployment Guide for Twilio-OpenAI Integration (PM2 + Traefik)

This guide explains how to deploy the Twilio-OpenAI integration application using PM2 for process management and a standalone Traefik instance as a reverse proxy for handling HTTPS and SSL certificates.

## Overview

The application consists of two main components:
1.  **Backend Server**: A Node.js server (`index.js`) that handles Twilio calls, WebSocket connections, and OpenAI integration. Runs on `localhost:3100`.
2.  **Frontend Application**: A Next.js application (`call-center-frontend`) that provides the user interface. Runs on `localhost:3000`.

PM2 is used to keep the backend and frontend processes running reliably. Traefik acts as a reverse proxy, listening on ports 80 and 443, handling SSL certificates via Let's Encrypt, and forwarding requests to the correct backend or frontend process based on the request path.

## Prerequisites

*   Node.js and npm installed
*   PM2 installed globally (`npm install -g pm2`)
*   Traefik binary installed and available in PATH (See: [Traefik Installation](https://doc.traefik.io/traefik/getting-started/installing-traefik/))
*   Twilio account with a phone number
*   OpenAI API key
*   A domain name (e.g., `t-oai.jackwolf.dev`) with DNS A record pointing to the public IP address of this server.

## Deployment Steps

### 1. Clone Repository

```bash
git clone <repository_url>
cd <repository_directory>
```

### 2. Install Dependencies

Install dependencies for both the backend and frontend:

```bash
npm install
cd call-center-frontend
npm install
cd ..
```

### 3. Set Up Environment Variables

Configure the necessary environment variables for both applications.

**a) Backend (`.env` file in the root directory):**

Copy the example file and edit it:

```bash
cp .env.example .env
nano .env
```

Ensure the following variables are set correctly (replace placeholders):

```dotenv
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
# ... other OpenAI settings ...

# Twilio Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number
DEFAULT_PHONE_NUMBER=your_default_test_number

# Server Configuration
PORT=3100 # Port the backend listens on internally
NODE_ENV=development # Or 'production'
PUBLIC_URL=https://your-domain.com # IMPORTANT: Your public domain (e.g., https://t-oai.jackwolf.dev)
CORS_ORIGIN=https://your-domain.com # IMPORTANT: Your public domain for CORS

# ... other settings (Transcription, Google, etc.) ...
```

**b) Frontend (`call-center-frontend/.env` file):**

Create the file and add the backend URL:

```bash
nano call-center-frontend/.env
```

Add the following line, replacing the URL with your public domain:

```dotenv
NEXT_PUBLIC_BACKEND_URL=https://your-domain.com
```

### 4. Configure Traefik

Traefik needs static and dynamic configuration files. We recommend placing these outside the application directory (e.g., `/home/<USER>/github/traefik/`).

**a) Static Configuration (`/home/<USER>/github/traefik/traefik.yml`):**

```yaml
# /home/<USER>/github/traefik/traefik.yml (Static Configuration)

entryPoints:
  web:
    address: ":80"
    http:
      redirections:
        entryPoint:
          to: websecure
          scheme: https
  websecure:
    address: ":443"
    http:
      tls:
        certResolver: letsencrypt

certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL> # Replace with your email
      storage: /home/<USER>/github/traefik/letsencrypt/acme.json # Ensure this dir exists
      httpChallenge:
        entryPoint: web

providers:
  file:
    directory: /home/<USER>/github/traefik/config # Ensure this dir exists
    watch: true
```

**b) Dynamic Configuration (`/home/<USER>/github/traefik/config/dynamic.yml`):**

```yaml
# /home/<USER>/github/traefik/config/dynamic.yml (Dynamic Configuration)

http:
  routers:
    backend-api-router:
      rule: "Host(`your-domain.com`) && PathPrefix(`/api`)" # Replace your-domain.com
      entryPoints: [websecure]
      service: backend-service
      priority: 10
      tls: { certResolver: letsencrypt }

    backend-media-stream-router:
      rule: "Host(`your-domain.com`) && Path(`/media-stream`)" # Replace your-domain.com
      entryPoints: [websecure]
      service: backend-service
      priority: 10
      tls: { certResolver: letsencrypt }

    backend-callbacks-router:
      rule: "Host(`your-domain.com`) && (Path(`/incoming-call`) || Path(`/call-status`) || Path(`/recording-status`))" # Replace your-domain.com
      entryPoints: [websecure]
      service: backend-service
      priority: 10
      tls: { certResolver: letsencrypt }

    frontend-router:
      rule: "Host(`your-domain.com`)" # Replace your-domain.com
      entryPoints: [websecure]
      service: frontend-service
      priority: 1
      tls: { certResolver: letsencrypt }

  services:
    frontend-service:
      loadBalancer:
        servers: [{ url: "http://localhost:3000" }] # Points to frontend PM2 process

    backend-service:
      loadBalancer:
        servers: [{ url: "http://localhost:3100" }] # Points to backend PM2 process
```

**c) Run Script (`/home/<USER>/github/traefik/run-traefik.sh`):**

Create a script to easily start Traefik and ensure directories exist.

```bash
#!/bin/bash
TRAEFIK_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
STATIC_CONFIG="$TRAEFIK_DIR/traefik.yml"
DYNAMIC_CONFIG_DIR="$TRAEFIK_DIR/config"
LETSENCRYPT_DIR="$TRAEFIK_DIR/letsencrypt"

mkdir -p "$DYNAMIC_CONFIG_DIR"
mkdir -p "$LETSENCRYPT_DIR"

if ! command -v traefik &> /dev/null; then
    echo "Traefik command could not be found. Please install Traefik."
    exit 1
fi

echo "Starting Traefik..."
# Use sudo if Traefik needs to bind to privileged ports (80, 443)
sudo traefik --configFile="$STATIC_CONFIG"
```

Make the script executable: `chmod +x /home/<USER>/github/traefik/run-traefik.sh`

### 5. Start Applications with PM2

Navigate back to the application's root directory (`/home/<USER>/github/twilio-openai-realapi`). Start the backend and frontend processes individually using PM2:

```bash
# Start Backend
pm2 start index.js --name t-oai-backend-dev

# Start Frontend
pm2 start npm --name t-oai-frontend-dev --cwd ./call-center-frontend -- run dev
```

Check the status:

```bash
pm2 list
pm2 logs t-oai-backend-dev
pm2 logs t-oai-frontend-dev
```

### 6. Start Traefik

Navigate to the Traefik directory and run the script:

```bash
cd /home/<USER>/github/traefik
sudo ./run-traefik.sh
```

Keep this terminal open or run Traefik in the background (e.g., using `nohup` or another `pm2` process).

### 7. Verify

Access your application via `https://your-domain.com` in a web browser. It might take a minute for Traefik to obtain the initial Let's Encrypt certificate.

## Important Considerations

*   **CORS:** The backend uses the `CORS_ORIGIN` environment variable. Ensure this matches your public domain.
*   **API Routes:** The frontend communicates with the backend via routes prefixed with `/api` (e.g., `/api/make-call`). Traefik routes these to the backend service.
*   **Twilio Webhooks:** Twilio needs to reach your backend for callbacks (`/incoming-call`, `/call-status`, `/recording-status`). Ensure your `PUBLIC_URL` is correctly set to your public domain (`https://your-domain.com`) and that Traefik routes these paths to the backend service.
*   **Firewall:** Ensure your server's firewall allows incoming traffic on ports 80 (for Let's Encrypt HTTP challenge) and 443 (for HTTPS).

## Troubleshooting

*   **DNS Issues:** If you get connection errors or certificate issues, double-check your domain's A record points to the correct server IP. Use `dig A your-domain.com +short` on the server to verify propagation.
*   **PM2 Logs:** Check application errors using `pm2 logs t-oai-backend-dev` and `pm2 logs t-oai-frontend-dev`.
*   **Traefik Logs:** Check Traefik's output (in the terminal where `run-traefik.sh` is running) for errors related to configuration loading, port binding, or certificate acquisition.
*   **Port Conflicts:** Ensure no other services are using ports 80, 443, 3000, or 3100.