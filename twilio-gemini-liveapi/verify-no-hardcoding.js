#!/usr/bin/env node

// Verification Script: Check for Hardcoded Values
// This script scans the codebase to verify that hardcoded values have been removed

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Patterns to search for (potential hardcoded values)
const hardcodedPatterns = [
    // API Keys
    { pattern: /AIzaSy[A-Za-z0-9_-]{33}/g, type: 'API Key', severity: 'HIGH' },
    { pattern: /sk-[A-Za-z0-9]{48}/g, type: 'OpenAI API Key', severity: 'HIGH' },
    { pattern: /AC[a-f0-9]{32}/g, type: 'Twilio Account SID', severity: 'HIGH' },
    
    // Phone Numbers
    { pattern: /\+1845[0-9]{7}/g, type: 'Hardcoded US Phone', severity: 'MEDIUM' },
    { pattern: /\+420[0-9]{9}/g, type: 'Hardcoded CZ Phone', severity: 'MEDIUM' },
    { pattern: /\+34[0-9]{9}/g, type: 'Hardcoded ES Phone', severity: 'MEDIUM' },
    { pattern: /************/g, type: 'Hardcoded Transfer Number', severity: 'MEDIUM' },
    
    // URLs
    { pattern: /https:\/\/gemini-api\.verduona\.com/g, type: 'Hardcoded Domain', severity: 'MEDIUM' },
    { pattern: /https:\/\/www\.verduona\.com/g, type: 'Hardcoded Domain', severity: 'MEDIUM' },
    
    // Model Names (when not in configuration)
    { pattern: /'gemini-2\.5-flash-preview-native-audio-dialog'/g, type: 'Hardcoded Model', severity: 'LOW' },
    { pattern: /'gemini-2\.0-flash-live-001'/g, type: 'Hardcoded Model', severity: 'LOW' },
    
    // Voice Names (when not in configuration)
    { pattern: /'Kore'/g, type: 'Hardcoded Voice', severity: 'LOW' },
    { pattern: /'Orus'/g, type: 'Hardcoded Voice', severity: 'LOW' },
    
    // Business Constants (when not in configuration)
    { pattern: /maxVehicles\s*=\s*9/g, type: 'Hardcoded Business Rule', severity: 'LOW' },
    { pattern: /maxClaims\s*=\s*3/g, type: 'Hardcoded Business Rule', severity: 'LOW' },
    
    // Agent Names
    { pattern: /'Sarah Johnson'/g, type: 'Hardcoded Agent Name', severity: 'LOW' },
    { pattern: /'John Smith'/g, type: 'Hardcoded Agent Name', severity: 'LOW' }
];

// Files and directories to scan
const scanPaths = [
    'src',
    'index.js',
    'campaign-script-loader.js'
];

// Files to exclude from scanning
const excludePatterns = [
    /node_modules/,
    /\.git/,
    /\.env/,
    /\.env\./,
    /test/,
    /MIGRATION_GUIDE\.md/,
    /CONFIGURATION\.md/,
    /\.template$/,
    /verify-no-hardcoding\.js$/
];

class HardcodingVerifier {
    constructor() {
        this.issues = [];
        this.scannedFiles = 0;
        this.totalLines = 0;
    }

    /**
     * Scan a file for hardcoded values
     */
    scanFile(filePath) {
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            const lines = content.split('\n');
            this.totalLines += lines.length;
            this.scannedFiles++;

            hardcodedPatterns.forEach(({ pattern, type, severity }) => {
                let match;
                while ((match = pattern.exec(content)) !== null) {
                    const lineNumber = content.substring(0, match.index).split('\n').length;
                    const line = lines[lineNumber - 1];
                    
                    // Skip if it's in a comment explaining the old way
                    if (line.includes('// Before') || line.includes('* Before') || 
                        line.includes('// Old:') || line.includes('// Hardcoded')) {
                        continue;
                    }
                    
                    // Skip if it's in configuration files (these are expected)
                    if (filePath.includes('config/') && severity === 'LOW') {
                        continue;
                    }
                    
                    this.issues.push({
                        file: filePath,
                        line: lineNumber,
                        type,
                        severity,
                        match: match[0],
                        context: line.trim()
                    });
                }
                // Reset regex lastIndex
                pattern.lastIndex = 0;
            });
        } catch (error) {
            console.warn(`Warning: Could not scan file ${filePath}: ${error.message}`);
        }
    }

    /**
     * Recursively scan directory
     */
    scanDirectory(dirPath) {
        try {
            const items = fs.readdirSync(dirPath);
            
            for (const item of items) {
                const fullPath = path.join(dirPath, item);
                const relativePath = path.relative(__dirname, fullPath);
                
                // Skip excluded patterns
                if (excludePatterns.some(pattern => pattern.test(relativePath))) {
                    continue;
                }
                
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    this.scanDirectory(fullPath);
                } else if (stat.isFile() && this.shouldScanFile(fullPath)) {
                    this.scanFile(fullPath);
                }
            }
        } catch (error) {
            console.warn(`Warning: Could not scan directory ${dirPath}: ${error.message}`);
        }
    }

    /**
     * Check if file should be scanned
     */
    shouldScanFile(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        const scanExtensions = ['.js', '.mjs', '.ts', '.json', '.md'];
        return scanExtensions.includes(ext);
    }

    /**
     * Run the verification
     */
    verify() {
        console.log('🔍 Scanning for hardcoded values...\n');
        
        for (const scanPath of scanPaths) {
            const fullPath = path.join(__dirname, scanPath);
            
            if (fs.existsSync(fullPath)) {
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    this.scanDirectory(fullPath);
                } else if (stat.isFile()) {
                    this.scanFile(fullPath);
                }
            } else {
                console.warn(`Warning: Path does not exist: ${scanPath}`);
            }
        }
        
        this.generateReport();
    }

    /**
     * Generate verification report
     */
    generateReport() {
        console.log(`📊 Scan Results:`);
        console.log(`   Files scanned: ${this.scannedFiles}`);
        console.log(`   Lines scanned: ${this.totalLines.toLocaleString()}`);
        console.log(`   Issues found: ${this.issues.length}\n`);
        
        if (this.issues.length === 0) {
            console.log('✅ SUCCESS: No hardcoded values detected!');
            console.log('   All hardcoded values have been successfully replaced with configurable alternatives.\n');
            this.printConfigurationSummary();
            return;
        }
        
        // Group issues by severity
        const issuesBySeverity = {
            HIGH: this.issues.filter(i => i.severity === 'HIGH'),
            MEDIUM: this.issues.filter(i => i.severity === 'MEDIUM'),
            LOW: this.issues.filter(i => i.severity === 'LOW')
        };
        
        // Report high severity issues
        if (issuesBySeverity.HIGH.length > 0) {
            console.log('🚨 HIGH SEVERITY ISSUES (Must Fix):');
            issuesBySeverity.HIGH.forEach(issue => {
                console.log(`   ${issue.file}:${issue.line} - ${issue.type}`);
                console.log(`      Found: ${issue.match}`);
                console.log(`      Context: ${issue.context}`);
                console.log('');
            });
        }
        
        // Report medium severity issues
        if (issuesBySeverity.MEDIUM.length > 0) {
            console.log('⚠️  MEDIUM SEVERITY ISSUES (Should Fix):');
            issuesBySeverity.MEDIUM.forEach(issue => {
                console.log(`   ${issue.file}:${issue.line} - ${issue.type}`);
                console.log(`      Found: ${issue.match}`);
                console.log('');
            });
        }
        
        // Report low severity issues
        if (issuesBySeverity.LOW.length > 0) {
            console.log('ℹ️  LOW SEVERITY ISSUES (Consider Fixing):');
            issuesBySeverity.LOW.forEach(issue => {
                console.log(`   ${issue.file}:${issue.line} - ${issue.type}`);
                console.log(`      Found: ${issue.match}`);
                console.log('');
            });
        }
        
        // Summary and recommendations
        console.log('📋 RECOMMENDATIONS:');
        
        if (issuesBySeverity.HIGH.length > 0) {
            console.log('   1. Remove all HIGH severity hardcoded values immediately');
            console.log('   2. Move sensitive data to environment variables');
        }
        
        if (issuesBySeverity.MEDIUM.length > 0) {
            console.log('   3. Replace MEDIUM severity hardcoded values with configuration');
            console.log('   4. Use the configuration system for URLs and phone numbers');
        }
        
        if (issuesBySeverity.LOW.length > 0) {
            console.log('   5. Consider making LOW severity values configurable for flexibility');
        }
        
        console.log('\n📖 See MIGRATION_GUIDE.md for detailed migration instructions');
        
        // Exit with error code if high severity issues found
        if (issuesBySeverity.HIGH.length > 0) {
            process.exit(1);
        }
    }

    /**
     * Print configuration system summary
     */
    printConfigurationSummary() {
        console.log('📋 Configuration System Summary:');
        console.log('   ✅ Centralized configuration in src/config/');
        console.log('   ✅ Environment variable support');
        console.log('   ✅ Configuration validation');
        console.log('   ✅ Multi-language support');
        console.log('   ✅ Campaign-specific overrides');
        console.log('   ✅ Audio and media configuration');
        console.log('   ✅ Business logic configuration');
        console.log('   ✅ Runtime configuration updates');
        console.log('   ✅ Comprehensive test coverage');
        console.log('');
        console.log('🎉 Hardcoding removal completed successfully!');
        console.log('   Your Twilio Gemini project is now fully configurable.');
    }
}

// Run the verification
const verifier = new HardcodingVerifier();
verifier.verify();
