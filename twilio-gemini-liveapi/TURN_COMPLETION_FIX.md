# Turn Completion Fix for Audio Testing

## Problem
The outbound test flow never sends a turn-complete signal, causing the AI to wait indefinitely for the user to finish speaking. The current implementation only has a "Stop Test" button that immediately closes the session.

## Solution
We need to implement proper turn completion signaling. Here are two approaches:

### Option 1: Add a "Complete Turn" Button
Add a separate button that sends turn-complete without stopping the session:

```typescript
// In LocalAudioTester.tsx

const completeTurn = () => {
  if (sessionRef.current && isRecording) {
    try {
      // Send turn-complete message through WebSocket
      ws.send(JSON.stringify({ type: 'turn-complete' }));
      setStatus('Turn completed - waiting for AI response...');
      console.log('📤 Turn complete signal sent');
    } catch (error) {
      console.error('❌ Error sending turn complete:', error);
    }
  }
};

// Add button in UI:
<button onClick={completeTurn} disabled={!isRecording}>
  Complete Turn
</button>
```

### Option 2: Automatic Turn Completion (Voice Activity Detection)
Implement VAD to automatically detect when user stops speaking:

```typescript
// Add to audio processing
let silenceTimer: NodeJS.Timeout | null = null;
const SILENCE_THRESHOLD = 0.01;
const SILENCE_DURATION_MS = 1500; // 1.5 seconds of silence

// In audio processing callback:
if (maxLevel > SILENCE_THRESHOLD) {
  // User is speaking, clear silence timer
  if (silenceTimer) {
    clearTimeout(silenceTimer);
    silenceTimer = null;
  }
} else {
  // User is silent
  if (!silenceTimer && isRecording) {
    silenceTimer = setTimeout(() => {
      completeTurn();
      silenceTimer = null;
    }, SILENCE_DURATION_MS);
  }
}
```

### Option 3: Manual Turn Signal with Keyboard Shortcut
Allow spacebar or other key to signal turn completion:

```typescript
useEffect(() => {
  const handleKeyPress = (e: KeyboardEvent) => {
    if (e.code === 'Space' && isRecording) {
      e.preventDefault();
      completeTurn();
    }
  };
  
  window.addEventListener('keydown', handleKeyPress);
  return () => window.removeEventListener('keydown', handleKeyPress);
}, [isRecording]);
```

## Implementation Steps

1. Modify the WebSocket session wrapper to handle turn-complete
2. Update the UI to show turn status
3. Add visual feedback when waiting for AI response
4. Ensure the audio output handler is ready to receive AI response

## Backend Changes Required

The backend already supports turn-complete messages in `local-testing-handler.js`:

```javascript
case 'turn-complete':
    await handleTurnComplete(sessionId, geminiSession, isSessionActive, deps);
    break;
```

No backend changes needed - just frontend implementation.

## Testing

1. Start outbound test
2. Speak your message
3. Complete turn (button/silence/key)
4. Verify AI responds with audio
5. Continue conversation with multiple turns