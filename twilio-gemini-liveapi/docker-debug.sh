#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting application in Docker debug mode...${NC}"

# Check if .env file exists
if [ ! -f .env ]; then
  echo -e "${RED}.env file not found. Creating from template...${NC}"
  cp .env.example .env
  echo -e "${YELLOW}Please edit .env file with your credentials before continuing.${NC}"
  exit 1
fi

echo -e "${YELLOW}Building and starting Docker containers in debug mode...${NC}"
docker-compose -f docker-compose.debug.yml up --build -d

echo -e "${YELLOW}Waiting for services to start...${NC}"
sleep 5

echo -e "${YELLOW}Checking if backend is running...${NC}"
if curl -s http://localhost:3001 > /dev/null; then
  echo -e "${GRE<PERSON>}Backend is running!${NC}"
else
  echo -e "${RED}Backend failed to start. Check logs with: docker logs twilio-backend-debug${NC}"
fi

echo -e "${YELLOW}Checking if frontend is running...${NC}"
if curl -s http://localhost:3000 > /dev/null; then
  echo -e "${GREEN}Frontend is running!${NC}"
else
  echo -e "${RED}Frontend failed to start. Check logs with: docker logs twilio-frontend-debug${NC}"
fi

echo -e "${GREEN}Debug services are available at:${NC}"
echo -e "${GREEN}Backend: http://localhost:3001 (Debugger on port 9229)${NC}"
echo -e "${GREEN}Frontend: http://localhost:3000 (Debugger on port 9230)${NC}"
echo -e "${YELLOW}To debug in Chrome, open chrome://inspect in your browser${NC}"
echo -e "${YELLOW}To debug in VS Code, use the provided launch configurations${NC}"
echo -e "${YELLOW}Live code changes will be automatically detected and reloaded${NC}"

echo -e "${YELLOW}To view logs:${NC}"
echo -e "${YELLOW}docker logs -f twilio-backend-debug${NC}"
echo -e "${YELLOW}docker logs -f twilio-frontend-debug${NC}"

echo -e "${YELLOW}To stop the debug environment:${NC}"
echo -e "${YELLOW}docker-compose -f docker-compose.debug.yml down${NC}"