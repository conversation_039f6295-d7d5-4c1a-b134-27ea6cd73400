#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Stopping services on ports 3000 and 3001...${NC}"

# Find and kill process on port 3000 (frontend)
PORT_3000_PID=$(lsof -t -i:3000)
if [ -n "$PORT_3000_PID" ]; then
  echo -e "${YELLOW}Stopping frontend on port 3000 (PID: $PORT_3000_PID)${NC}"
  kill -15 $PORT_3000_PID
  sleep 2
  
  # Check if process is still running and force kill if necessary
  if ps -p $PORT_3000_PID > /dev/null; then
    echo -e "${YELLOW}Frontend process still running, force killing...${NC}"
    kill -9 $PORT_3000_PID
  fi
  
  echo -e "${GREEN}Frontend stopped${NC}"
else
  echo -e "${YELLOW}No process running on port 3000${NC}"
fi

# Find and kill process on port 3001 (backend)
PORT_3001_PID=$(lsof -t -i:3001)
if [ -n "$PORT_3001_PID" ]; then
  echo -e "${YELLOW}Stopping backend on port 3001 (PID: $PORT_3001_PID)${NC}"
  kill -15 $PORT_3001_PID
  sleep 2
  
  # Check if process is still running and force kill if necessary
  if ps -p $PORT_3001_PID > /dev/null; then
    echo -e "${YELLOW}Backend process still running, force killing...${NC}"
    kill -9 $PORT_3001_PID
  fi
  
  echo -e "${GREEN}Backend stopped${NC}"
else
  echo -e "${YELLOW}No process running on port 3001${NC}"
fi

echo -e "${GREEN}All services stopped${NC}"