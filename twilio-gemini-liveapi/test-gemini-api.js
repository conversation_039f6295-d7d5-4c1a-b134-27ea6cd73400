#!/usr/bin/env node

import pkg from '@google/genai';
const { GoogleGenAI } = pkg;
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🧪 Testing Gemini API Key and Model...\n');

const API_KEY = process.env.GEMINI_API_KEY;
console.log(`📝 API Key loaded: ${API_KEY ? 'Yes' : 'No'}`);
console.log(`📝 API Key length: ${API_KEY?.length || 0}`);
console.log(`📝 API Key preview: ${API_KEY?.substring(0, 10)}...${API_KEY?.substring(API_KEY.length - 5)}\n`);

if (!API_KEY) {
    console.error('❌ No API key found in environment!');
    process.exit(1);
}

try {
    // Initialize the Gemini client
    const genAI = new GoogleGenAI(API_KEY);
    console.log('✅ Gemini client initialized\n');

    // Test with the text model first
    console.log('🔍 Testing text generation with gemini-pro...');
    const textModel = genAI.getGenerativeModel({ model: "gemini-pro" });
    
    const prompt = "Say 'Hello, I am working!' if you can process this request.";
    const result = await textModel.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    console.log('✅ Text model response:', text);
    console.log('\n📊 API Key Status: VALID - Text generation successful\n');

    // Now test with the audio model
    console.log('🔍 Testing model availability for gemini-2.5-flash-preview-native-audio-dialog...');
    try {
        const audioModel = genAI.getGenerativeModel({ 
            model: "gemini-2.5-flash-preview-native-audio-dialog" 
        });
        console.log('✅ Audio model initialized successfully');
        
        // Try a simple text prompt with the audio model
        const audioModelResult = await audioModel.generateContent("Test");
        console.log('✅ Audio model can process text requests');
        
    } catch (audioError) {
        console.error('❌ Audio model error:', audioError.message);
        if (audioError.message.includes('API key')) {
            console.error('❌ API key does not have access to the audio model');
        } else if (audioError.message.includes('model')) {
            console.error('❌ Model not available or incorrect model name');
        }
    }

} catch (error) {
    console.error('❌ Error:', error.message);
    if (error.message.includes('API_KEY_INVALID')) {
        console.error('❌ The API key is invalid!');
    } else if (error.message.includes('quota')) {
        console.error('❌ API quota exceeded!');
    } else {
        console.error('❌ Unknown error occurred');
    }
    console.error('\nFull error:', error);
    process.exit(1);
}