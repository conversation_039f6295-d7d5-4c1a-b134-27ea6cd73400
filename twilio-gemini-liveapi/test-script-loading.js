import { getCampaignScript } from './src/config/campaign-config.js';
import { getConfigValue } from './src/config/config.js';

console.log('Testing script loading...');
console.log('Scripts path:', getConfigValue('campaigns.scriptsPath'));

try {
    const script = getCampaignScript(1, 'outbound');
    console.log('Script loaded:', !!script);
    if (script) {
        console.log('Script title:', script.title);
        console.log('Script campaign length:', script.campaign?.length || 0);
        console.log('Script has agentPersona:', !!script.agentPersona);
    } else {
        console.log('Script is null/undefined');
    }
} catch (error) {
    console.error('Error loading script:', error);
}
