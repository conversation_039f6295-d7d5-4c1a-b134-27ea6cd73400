#!/bin/bash

echo "🔍 VERIFYING TWILIO GEMINI CAMPAIGN SCRIPTS"
echo "=============================================="
echo ""

echo "📤 OUTBOUND CAMPAIGNS (1-6) - Should be 2+2+2 (EN, ES, CZ):"
echo "-----------------------------------------------------------"

for i in {1..6}; do
    echo -n "Campaign $i: "
    result=$(curl -s http://localhost:3000/get-campaign-script/$i | jq -r '.title + " | " + .language + " | " + .category' 2>/dev/null)
    if [ $? -eq 0 ] && [ "$result" != "null" ]; then
        echo "$result"
    else
        echo "❌ FAILED"
    fi
done

echo ""
echo "📥 INCOMING CAMPAIGNS (7-12) - Should be 2+2+2 (E<PERSON>, ES, CZ):"
echo "------------------------------------------------------------"

for i in {7..12}; do
    echo -n "Campaign $i: "
    result=$(curl -s http://localhost:3000/get-campaign-script/$i | jq -r '.title + " | " + .language + " | " + .category' 2>/dev/null)
    if [ $? -eq 0 ] && [ "$result" != "null" ]; then
        echo "$result"
    else
        echo "❌ FAILED"
    fi
done

echo ""
echo "✅ VERIFICATION COMPLETE"
