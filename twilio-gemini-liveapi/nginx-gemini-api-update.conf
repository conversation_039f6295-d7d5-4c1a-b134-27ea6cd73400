# Add these location blocks to the gemini-api.verduona.com server block
# BEFORE the general location / block

    # WebSocket endpoints for Twilio Media Streams - CRITICAL
    location ~ ^/(media-stream|media-stream-inbound|test-outbound|test-inbound|local-audio-session|gemini-live)$ {
        # Proxy to Gemini backend
        proxy_pass http://twilio-gemini-backend;
        
        # Standard headers
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket headers - REQUIRED
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_cache_bypass $http_upgrade;
        
        # Extended timeouts for long-lived WebSocket connections
        proxy_connect_timeout 7d;
        proxy_send_timeout 7d;
        proxy_read_timeout 7d;
        
        # Disable buffering for real-time audio data
        proxy_buffering off;
        proxy_request_buffering off;
        
        # Large buffer sizes for audio data
        proxy_buffer_size 64k;
        proxy_buffers 8 64k;
        proxy_busy_buffers_size 128k;
        
        # Log WebSocket connections
        access_log /var/log/nginx/gemini-websocket.access.log;
        error_log /var/log/nginx/gemini-websocket.error.log debug;
    }
    
    # Twilio webhook endpoints (no auth required)
    location ~ ^/(incoming-call|call-status|recording-status)$ {
        proxy_pass http://twilio-gemini-backend;
        
        # Standard proxy headers
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Bypass authentication for Twilio webhooks
        proxy_set_header X-Skip-Auth "true";
        
        # Standard timeouts for webhooks
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # Log Twilio webhooks
        access_log /var/log/nginx/twilio-webhooks.access.log;
    }
    
    # Health check endpoint (no auth required)
    location = /health {
        proxy_pass http://twilio-gemini-backend;
        proxy_set_header Host $host;
        
        # Bypass any authentication for health checks
        proxy_set_header X-Skip-Auth "true";
        
        # Short timeout for health checks
        proxy_connect_timeout 5s;
        proxy_send_timeout 5s;
        proxy_read_timeout 5s;
    }