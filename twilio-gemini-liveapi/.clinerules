# Cline Rules for Twilio Gemini Live API Project

## Project Overview
This is a Node.js voice AI call center system that integrates:
- Twilio for telephony
- Google Gemini Live API for conversational AI  
- Supabase for data storage
- Next.js frontend
- ES modules throughout

## Key Architecture Principles
- **CAMPAIGN SCRIPTS ONLY** - 100% campaign scripts, 0% system prompts (see CAMPAIGN_SCRIPT_POLICY.md)
- **Policy-based AI instructions** - All AI behavior derived from campaign scripts, not system prompts
- **4 Flows Supported**: Outbound Twilio, Outbound Testing (browser), Inbound Twilio, Inbound Testing (browser)
- **Modular architecture** with clear separation of concerns

## Essential Commands
```bash
# Development
npm run dev          # Start with auto-reload
npm run debug        # Start with Node.js inspector

# Code Quality (ALWAYS run before completing tasks)
npm run lint         # Check code style
npm run lint:fix     # Auto-fix linting issues
npm test            # Run tests (ALL 27+ tests must pass)

# Production
npm start           # Start the server
pm2 start ecosystem.config.js --only twilio-gemini-backend
pm2 logs twilio-gemini-backend

# Health & Monitoring
npm run health      # Check server health
npm run monitor     # Run dev + health monitoring
npm run audio-quality # Check audio quality

# Maintenance
npm run clean       # Clean all generated files
npm run clean:audio # Clean audio debug files
```

## Port Allocation
```
5000 - verduona-web (Main Website)
5001 - template-live-api-web-console (Live Console)
5002 - template-live-audio (Live Audio)
5003 - agent-zero-verduona (Agent Zero Docker Container)
3005 - agent-verduona-login (Agent Auth Module - PM2)
3011 - twilio-gemini-frontend (Next.js auto-assigned)
3012 - twilio-openai-frontend (Next.js auto-assigned)
3101 - twilio-gemini-backend
3102 - twilio-openai-backend
3999 - auth-service (Nginx Auth Request Handler - CRITICAL)
```

## File Structure Rules

### Core Services Location
- **Entry Point**: `index.js` - Fastify server setup with WebSocket support
- **API Layer**: `src/api/` - REST endpoints for management and testing
- **Core Services**:
  - `src/session/session-manager.js` - Central session orchestration
  - `src/gemini/client.js` - Google Gemini AI integration
  - `src/websocket/handlers.js` - Real-time audio streaming
  - `src/audio/audio-processor.js` - Audio format conversion and processing
  - `src/config/config.js` - Centralized configuration management

### Configuration
- All configuration centralized in `src/config/config.js`
- Environment variables validated on startup
- Required: `GEMINI_API_KEY`, `TWILIO_*`, `PUBLIC_URL`

### Frontend Integration
- Next.js frontend in `call-center-frontend/`
- Backend API: Port 3101
- Frontend: Port 3011

## Code Style Rules

### Audio Processing
1. **Audio Methods**: 
   - Browser: `geminiSession.sendRealtimeInput({ media: { data, mimeType } })`
   - Twilio: Same method after μ-law to PCM conversion
2. **Use `sendBrowserAudioToGemini` for browser audio**
3. **Use `sendAudioToGemini` for Twilio audio**

### Campaign Scripts
- All AI behavior comes from campaign scripts (IDs 1-12)
- Scripts 1-6: Outbound campaigns
- Scripts 7-12: Inbound campaigns
- **NO SYSTEM PROMPTS** - Send full campaign script as user message

### WebSocket Flows
- `/media-stream` - Twilio calls (inbound/outbound)
- `/local-audio-session` - Browser testing (legacy)
- `/test-outbound` - Outbound testing
- `/test-inbound` - Inbound testing

### Logging Rules
- **USE STRUCTURED LOGGER**: Always use `src/utils/logger.js` - NEVER console.log
- **Contextual logging**: Include callSid, sessionId, or relevant context
- **Emoji prefixes**: Required for all logs for easy scanning:
  - 🚀 Startup/initialization
  - 📞 Call events  
  - 🎤 Audio processing
  - 💬 AI responses
  - ❌ Errors
  - ⚠️ Warnings
  - ✅ Success
  - 🔍 Debug info
- **Performance timing**: Use `logger.timing()` for operations > 100ms
- **Structured format**: `logger.info('🎤 Audio processed', { chunks: 15, format: 'ulaw' })`
- **Error context**: Always include error object and relevant context

### Error Handling
- Always wrap async operations in try-catch
- Log errors with full context including callSid
- Implement graceful degradation
- Use recovery mechanisms for session failures

### Testing Rules
- **Comprehensive test coverage**: 27+ tests must ALL pass before completion
- **Use Node.js built-in test runner** (not Jest)
- **Test all 4 flows**: outbound/inbound × twilio/browser
- **Include LLM inference tests** with real Gemini API responses
- **Test files**:
  - `test/configuration.test.js` - 27 passing tests for config system
  - `test/backend-api.test.js` - API endpoint validation
  - `test/workflow-integration.test.js` - Complete workflow testing
- **Mock appropriately**: Use `src/utils/test-utils.js` utilities
- **Real API testing**: Include actual Gemini Live API inference tests
- **Error scenarios**: Test recovery mechanisms and error handling

## Modular Development Rules

### When modifying call handling:
1. Check `src/websocket/handlers.js` for WebSocket message handling
2. Review `src/session/session-manager.js` for session lifecycle
3. Test all 4 flows after changes

### When working with AI responses:
1. See `src/gemini/client.js` for Gemini integration
2. Configure voices in `src/gemini/voice-manager.js`
3. Adjust models in `src/gemini/model-manager.js`
4. **IMPORTANT**: Use `sendRealtimeInput` for audio, not `sendClientContent`

### When debugging audio issues:
1. Browser audio uses PCM16 @ 16kHz (no conversion needed)
2. Twilio audio uses μ-law @ 8kHz (needs conversion)
3. Check `src/audio/audio-processor.js` for conversion logic

## Code Quality Standards

### Before committing:
1. Run `npm run lint:fix`
2. Run `npm test` - **ALL 27+ tests must pass**
3. Test manually with all 4 flows
4. Check logs use proper emoji prefixes and structure

### File Modularity Rules
- **Keep files under 300 lines** - split large files into modules
- **Single responsibility**: Each file should have one clear purpose
- **Extract utilities**: Move reusable functions to `src/utils/`
- **Break up large classes**: Split into multiple smaller classes
- **Separate concerns**: Keep business logic, API routes, and utilities separate

### Import/Export Rules
- Use ES modules (`import`/`export`) throughout
- No CommonJS (`require`/`module.exports`)
- Import from `src/` for internal modules
- Group imports: external packages, internal modules, relative imports

### Variable Naming
- Use camelCase for variables and functions
- Use PascalCase for classes
- Use UPPER_CASE for constants
- Use descriptive names (no single letters except loop counters)

## Security Rules
- Validate all inputs with `SecurityUtils` class
- Sanitize phone numbers, text, and JSON
- Use rate limiting on API endpoints
- Implement CORS properly for frontend integration
- Never log sensitive data (API keys, tokens)

## Performance Rules
- Use connection pooling for external APIs
- Implement caching where appropriate
- Monitor memory usage for long-running sessions
- Clean up inactive sessions and contexts
- Use compression for API responses

## Documentation Rules
- Update this file when architecture changes
- Document new environment variables in config.js
- Add JSDoc comments for complex functions
- Update README.md for deployment changes

## Common Pitfalls to Avoid
1. **Don't use system prompts** - Only campaign scripts
2. **Don't mix audio methods** - Use correct method for source
3. **Don't skip error handling** - Always wrap risky operations
4. **Don't ignore test failures** - ALL 27+ tests must pass
5. **Don't hardcode configuration** - Use environment variables
6. **Don't forget session cleanup** - Prevent memory leaks
7. **Don't use console.log** - Use structured logger from `src/utils/logger.js`
8. **Don't create large files** - Keep under 300 lines, split into modules
9. **Don't skip emoji prefixes** - Required for all log messages
10. **Don't forget test coverage** - Include comprehensive workflow tests

## Git Workflow
- Work on feature branches
- Merge to `develop` for testing
- Only merge to `main` when fully tested
- Include tests in all PRs
- Update version numbers appropriately

## Recovery Procedures & Build/Restart Guide

### Quick Recovery After Server Crash/Restart
```bash
# Navigate to project root
cd /home/<USER>/github/verduona-full/twilio-gemini-liveapi

# Restore all PM2 processes from saved state
pm2 resurrect

# Wait for processes to initialize
sleep 10

# Restart all for fresh state
pm2 restart all

# Wait for services to stabilize
sleep 15

# Verify all services are running
pm2 status
```

### Manual Recovery (If PM2 dump is corrupted)
```bash
# Navigate to project root
cd /home/<USER>/github/verduona-full/twilio-gemini-liveapi

# Start core services
pm2 start ecosystem.config.js --only twilio-gemini-backend
pm2 start ecosystem.config.js --only twilio-gemini-frontend

# Save current state for future recovery
pm2 save
```

### Production Deployment Process (CRITICAL)
```bash
# Build applications for production
cd /home/<USER>/github/verduona-full/twilio-gemini-liveapi/call-center-frontend
pnpm build

# Start in production mode using ecosystem configs
cd /home/<USER>/github/verduona-full/twilio-gemini-liveapi
pm2 start ecosystem.config.js --only twilio-gemini-frontend

# Verify production status
pm2 list
pm2 logs twilio-gemini-frontend --lines 5

# Test direct frontend health
curl -I http://localhost:3011  # Should return 200

# ⚠️ CRITICAL: Must use `pnpm build` then `pnpm start`, never `pnpm dev` in production
```

### Health Check Commands
```bash
# Check service health
pm2 status

# Check backend API health
curl https://gemini-api.verduona.com/health

# Check frontend health
curl -I http://localhost:3011

# Check logs for errors
pm2 logs --lines 20 --err

# Check disk space and memory
df -h && free -h
```

### Emergency Recovery Commands
```bash
# Emergency restart of all services
pm2 restart twilio-gemini-backend
pm2 restart twilio-gemini-frontend

# Rebuild and restart if needed
cd /home/<USER>/github/verduona-full/twilio-gemini-liveapi/call-center-frontend
pnpm install && pnpm build
cd /home/<USER>/github/verduona-full/twilio-gemini-liveapi
pm2 restart twilio-gemini-frontend

# Check nginx configuration
sudo nginx -t
sudo systemctl reload nginx
```

## Environment Variables Required
- `GEMINI_API_KEY` - Required for AI functionality
- `TWILIO_ACCOUNT_SID` - Required for phone service
- `TWILIO_AUTH_TOKEN` - Required for phone service
- `PUBLIC_URL` - Required for Twilio callbacks
- Optional: `DEEPGRAM_API_KEY`, `OPENAI_API_KEY`, `SUPABASE_*`
