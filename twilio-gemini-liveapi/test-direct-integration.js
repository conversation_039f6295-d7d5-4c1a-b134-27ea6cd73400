#!/usr/bin/env node

import WebSocket from 'ws';
import pkg from '@google/genai';
const { GoogleGenAI, Modality } = pkg;
import dotenv from './src/utils/dotenv-stub.js';

dotenv.config();

console.log('🧪 Testing Direct Integration (Bypassing Session Manager)...');

const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const geminiClient = new GoogleGenAI({ apiKey: GEMINI_API_KEY });

let sessionStarted = false;
let aiResponded = false;
let geminiSession = null;

const ws = new WebSocket('ws://localhost:3101/local-audio-session');

ws.on('open', () => {
    console.log('✅ WebSocket connected');
    
    // Send session start
    console.log('📤 Starting session...');
    ws.send(JSON.stringify({
        type: 'start-session',
        voice: '<PERSON><PERSON>',
        model: 'gemini-2.5-flash-preview-native-audio-dialog',
        scriptType: 'incoming',
        scriptId: 'incoming-1',
        targetName: 'Test User',
        targetPhoneNumber: '+420733154483',
        isTestMode: true
    }));
});

ws.on('message', async (data) => {
    try {
        const message = JSON.parse(data);
        console.log(`📨 Received: ${message.type}`);
        
        if (message.type === 'session-started') {
            sessionStarted = true;
            console.log('✅ Session started! Now creating DIRECT Gemini session (bypassing session manager)...');
            
            // Create Gemini session DIRECTLY (bypassing our session manager)
            try {
                console.log('🤖 Creating DIRECT Gemini session...');
                
                geminiSession = await geminiClient.live.connect({
                    model: 'gemini-2.5-flash-preview-native-audio-dialog',
                    callbacks: {
                        onopen: () => {
                            console.log('✅ DIRECT Gemini session opened successfully');
                            
                            // Send initial AI instructions
                            setTimeout(() => {
                                console.log('📤 Sending initial AI instructions to DIRECT session...');
                                geminiSession.sendClientContent({
                                    turns: [{
                                        role: 'user',
                                        parts: [{
                                            text: 'You are a helpful AI assistant. Please respond with audio when users ' +
                                                'send you messages. Keep responses brief and friendly.'
                                        }]
                                    }],
                                    turnComplete: true
                                });
                                
                                // Send test message after another delay
                                setTimeout(() => {
                                    console.log('📤 Sending test message to DIRECT session...');
                                    geminiSession.sendClientContent({
                                        turns: [{
                                            role: 'user',
                                            parts: [{
                                                text: 'Hello, please say "test response" back to me.'
                                            }]
                                        }],
                                        turnComplete: true
                                    });
                                }, 1000);
                            }, 1000);
                        },
                        
                        onmessage: (message) => {
                            aiResponded = true;
                            console.log('🎉 DIRECT GEMINI RESPONSE RECEIVED!');
                            console.log('📨 Message type:', message?.type || 'unknown');
                            console.log('📨 Message keys:', Object.keys(message || {}).join(', '));
                            
                            // Check for audio response
                            const audio = message?.serverContent?.modelTurn?.parts?.[0]?.inlineData;
                            if (audio) {
                                console.log('🎵 Audio response received!');
                                console.log('   MIME type:', audio.mimeType);
                                console.log('   Data size:', audio.data?.length || 0, 'bytes');
                                
                                // Send audio back to WebSocket
                                try {
                                    ws.send(JSON.stringify({
                                        type: 'audio-response',
                                        audio: {
                                            mimeType: audio.mimeType,
                                            data: audio.data
                                        }
                                    }));
                                    console.log('🔊 Sent audio back to WebSocket');
                                } catch (audioError) {
                                    console.error('❌ Error sending audio to WebSocket:', audioError);
                                }
                            }
                            
                            // Check for text response
                            const text = message?.serverContent?.modelTurn?.parts?.[0]?.text;
                            if (text) {
                                console.log('💬 Text response:', text);
                            }
                        },
                        
                        onerror: (error) => {
                            console.error('❌ DIRECT Gemini session error:', error);
                        },
                        
                        onclose: () => {
                            console.log('🔌 DIRECT Gemini session closed');
                        }
                    },
                    config: {
                        responseModalities: [Modality.AUDIO],
                        speechConfig: {
                            voiceConfig: {
                                prebuiltVoiceConfig: {
                                    voiceName: 'Kore'
                                }
                            }
                        }
                    },
                    temperature: 1.1,
                    topP: 0.95,
                    topK: 40,
                    maxOutputTokens: 8192,
                    responseMimeType: 'text/plain'
                });
                
                console.log('✅ DIRECT Gemini session created successfully');
                
            } catch (error) {
                console.error('❌ Error creating DIRECT Gemini session:', error);
            }
        }
        
        if (message.type === 'audio-response') {
            console.log('🎵 Received audio response from WebSocket');
        }
    } catch (error) {
        console.log(`⚠️ Error parsing message: ${error.message}`);
    }
});

ws.on('close', (code, reason) => {
    console.log(`🔌 WebSocket closed - Code: ${code}, Reason: ${reason}`);
    
    console.log('\n📊 Test Results:');
    console.log(`✅ Session Started: ${sessionStarted ? 'YES' : 'NO'}`);
    console.log(`🤖 AI Responded: ${aiResponded ? 'YES' : 'NO'}`);
    
    if (sessionStarted && !aiResponded) {
        console.log('\n❌ ISSUE: Even DIRECT Gemini session does not respond');
        console.log('   This suggests the issue is NOT in our session manager');
        console.log('   The issue might be in the WebSocket environment or configuration');
    } else if (!sessionStarted) {
        console.log('\n❌ ISSUE: Session failed to start');
    } else {
        console.log('\n🎉 SUCCESS: DIRECT Gemini session is responding!');
        console.log('   This means the issue IS in our session manager implementation');
    }
    
    // Close direct Gemini session
    if (geminiSession) {
        try {
            geminiSession.close();
        } catch {
            console.log('Direct session already closed');
        }
    }
});

ws.on('error', (error) => {
    console.log(`❌ WebSocket error: ${error.message}`);
});

// Auto-close after 20 seconds
setTimeout(() => {
    if (ws.readyState === WebSocket.OPEN) {
        console.log('\n⏰ Test timeout - closing connection');
        ws.close();
    }
}, 20000);
