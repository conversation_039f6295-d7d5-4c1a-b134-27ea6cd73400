#!/bin/bash

echo "🔧 Fixing DNS resolution for gemini-api.verduona.com"
echo ""
echo "Current /etc/hosts entry:"
grep "gemini-api.verduona.com" /etc/hosts

echo ""
echo "To fix this issue, run:"
echo "sudo sed -i 's/^************* gemini-api.verduona.com/#************* gemini-api.verduona.com/' /etc/hosts"
echo ""
echo "Or manually edit /etc/hosts and comment out the line:"
echo "************* gemini-api.verduona.com oai-api.verduona.com"
echo ""
echo "After fixing, verify with:"
echo "dig gemini-api.verduona.com +short"
echo "(Should return: ***********)"