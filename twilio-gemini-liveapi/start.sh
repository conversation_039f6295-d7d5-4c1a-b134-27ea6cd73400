#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Checking for processes on ports 3000 and 3001...${NC}"

# Kill any processes running on port 3000 (frontend)
PORT_3000_PID=$(lsof -t -i:3000)
if [ -n "$PORT_3000_PID" ]; then
  echo -e "${YELLOW}Killing process on port 3000 (PID: $PORT_3000_PID)${NC}"
  kill -9 $PORT_3000_PID
else
  echo -e "${GREEN}No process running on port 3000${NC}"
fi

# Kill any processes running on port 3001 (backend)
PORT_3001_PID=$(lsof -t -i:3001)
if [ -n "$PORT_3001_PID" ]; then
  echo -e "${YELLOW}Killing process on port 3001 (PID: $PORT_3001_PID)${NC}"
  kill -9 $PORT_3001_PID
else
  echo -e "${GREEN}No process running on port 3001${NC}"
fi

echo -e "${YELLOW}Starting backend server on port 3001...${NC}"
# Start the backend server in the background
node index.js > backend.log 2>&1 &
BACKEND_PID=$!
echo -e "${GREEN}Backend started with PID: $BACKEND_PID${NC}"

# Wait for backend to start
echo -e "${YELLOW}Waiting for backend to start...${NC}"
MAX_RETRIES=10
RETRY_COUNT=0
BACKEND_READY=false

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
  if curl -s http://localhost:3001 > /dev/null; then
    BACKEND_READY=true
    echo -e "${GREEN}Backend is running!${NC}"
    break
  fi
  echo -e "${YELLOW}Waiting for backend to start (attempt $((RETRY_COUNT+1))/$MAX_RETRIES)...${NC}"
  RETRY_COUNT=$((RETRY_COUNT+1))
  sleep 2
done

if [ "$BACKEND_READY" = false ]; then
  echo -e "${RED}Failed to start backend after $MAX_RETRIES attempts${NC}"
  echo -e "${YELLOW}Check backend.log for errors${NC}"
  exit 1
fi

echo -e "${YELLOW}Starting frontend on port 3000...${NC}"
# Start the frontend in development mode (more stable for now)
cd call-center-frontend && npm run dev > ../frontend.log 2>&1 &
FRONTEND_PID=$!
echo -e "${GREEN}Frontend started with PID: $FRONTEND_PID${NC}"

# Wait for frontend to start
echo -e "${YELLOW}Waiting for frontend to start...${NC}"
MAX_RETRIES=10
RETRY_COUNT=0
FRONTEND_READY=false

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
  if curl -s http://localhost:3000 > /dev/null; then
    FRONTEND_READY=true
    echo -e "${GREEN}Frontend is running!${NC}"
    break
  fi
  echo -e "${YELLOW}Waiting for frontend to start (attempt $((RETRY_COUNT+1))/$MAX_RETRIES)...${NC}"
  RETRY_COUNT=$((RETRY_COUNT+1))
  sleep 2
done

if [ "$FRONTEND_READY" = false ]; then
  echo -e "${RED}Failed to start frontend after $MAX_RETRIES attempts${NC}"
  echo -e "${YELLOW}Check frontend.log for errors${NC}"
  exit 1
fi

echo -e "${GREEN}Both services are running:${NC}"
echo -e "${GREEN}Backend: http://localhost:3001${NC}"
echo -e "${GREEN}Frontend: http://localhost:3000${NC}"
echo -e "${YELLOW}Logs are available in backend.log and frontend.log${NC}"

# Run health check to verify services
echo -e "${YELLOW}Running health check to verify services...${NC}"
./health-check.sh