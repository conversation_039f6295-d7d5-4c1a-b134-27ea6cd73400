#!/usr/bin/env node

import WebSocket from 'ws';

console.log('🧪 Testing AI Response with Text Prompt...');

const ws = new WebSocket('ws://localhost:3101/local-audio-session');
let sessionStarted = false;
let aiResponded = false;

ws.on('open', () => {
    console.log('✅ WebSocket connected');
    
    // Send session start
    console.log('📤 Starting session...');
    ws.send(JSON.stringify({
        type: 'start-session',
        voice: 'Kore',
        model: 'gemini-2.5-flash-preview-native-audio-dialog',
        scriptType: 'incoming',
        scriptId: 'incoming-1',
        targetName: 'Test User',
        targetPhoneNumber: '+420733154483',
        isTestMode: true
    }));
});

ws.on('message', (data) => {
    try {
        const message = JSON.parse(data);
        console.log(`📨 Received: ${message.type}`);
        
        if (message.type === 'session-started') {
            sessionStarted = true;
            console.log('✅ Session started! Now sending text prompt...');
            
            // Wait a moment then send a text prompt
            setTimeout(() => {
                console.log('💬 Sending text prompt: "Hello, please respond with audio"');
                
                ws.send(JSON.stringify({
                    type: 'text-message',
                    text: 'Hello, please respond with audio. Say something back to me.'
                }));
                
                // Send turn complete after text
                setTimeout(() => {
                    console.log('📤 Sending turn complete signal...');
                    ws.send(JSON.stringify({
                        type: 'turn-complete'
                    }));
                }, 500);
                
            }, 1000);
        } else if (message.type === 'audio-response' || message.type === 'ai-audio') {
            aiResponded = true;
            console.log('🎉 AI RESPONDED WITH AUDIO!');
            console.log(`   Audio size: ${message.audioData ? message.audioData.length : 'N/A'} bytes`);
        } else if (message.type === 'ai-text' || message.text) {
            aiResponded = true;
            console.log('💬 AI RESPONDED WITH TEXT!');
            console.log(`   Text: ${message.text || message.content || 'N/A'}`);
        } else {
            console.log(`   Full message:`, message);
        }
    } catch (error) {
        console.log(`⚠️ Error parsing message: ${error.message}`);
        console.log(`   Raw data: ${data.toString().substring(0, 200)}...`);
    }
});

ws.on('close', (code, reason) => {
    console.log(`🔌 WebSocket closed - Code: ${code}, Reason: ${reason}`);
    
    console.log('\n📊 Test Results:');
    console.log(`✅ Session Started: ${sessionStarted ? 'YES' : 'NO'}`);
    console.log(`🤖 AI Responded: ${aiResponded ? 'YES' : 'NO'}`);
    
    if (sessionStarted && !aiResponded) {
        console.log('\n❌ ISSUE: Session starts but AI does not respond to text either');
        console.log('   This suggests a fundamental issue with:');
        console.log('   1. Gemini session configuration');
        console.log('   2. AI response handling');
        console.log('   3. Response modality setup');
    } else if (!sessionStarted) {
        console.log('\n❌ ISSUE: Session failed to start');
    } else {
        console.log('\n🎉 SUCCESS: AI is responding!');
    }
});

ws.on('error', (error) => {
    console.log(`❌ WebSocket error: ${error.message}`);
});

// Auto-close after 15 seconds
setTimeout(() => {
    if (ws.readyState === WebSocket.OPEN) {
        console.log('\n⏰ Test timeout - closing connection');
        ws.close();
    }
}, 15000);
