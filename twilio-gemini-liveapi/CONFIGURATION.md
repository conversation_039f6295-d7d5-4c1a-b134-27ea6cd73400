# Twilio Gemini Live API Configuration Guide

This document describes all configurable settings for the Twilio Gemini Live API project. All hardcoded values have been replaced with configurable options.

## Quick Start

1. Copy `.env.template` to `.env`
2. Fill in the required values (marked as REQUIRED)
3. Customize optional settings as needed
4. Run the application

## Configuration Categories

### Required Configuration

These settings are required for the application to function:

- `GEMINI_API_KEY`: Your Google Gemini API key
- `TWILIO_ACCOUNT_SID`: Your Twilio Account SID
- `TWILIO_AUTH_TOKEN`: Your Twilio Auth Token
- `PUBLIC_URL`: The public URL where your application is hosted

### API Keys and Authentication

Configure various API services:

- **Gemini**: `GEMINI_API_KEY`
- **OpenAI**: `OPENAI_API_KEY`, `OPENAI_API_URL`
- **Deepgram**: `DEEPGRAM_API_KEY`
- **<PERSON>rok**: `NGROK_AUTHTOKEN`

### Twilio Configuration

Phone numbers and webhook settings:

- **Phone Numbers**: Support for multiple countries (US, CZ, ES)
- **Webhooks**: Auto-generated from `PUBLIC_URL` or manually specified
- **Default Numbers**: For transfers and callbacks

### AI Model Configuration

Customize AI behavior:

- **Models**: Choose from available Gemini models
- **Voices**: Configure voice selection and mapping
- **Parameters**: Temperature, Top-P, Top-K settings
- **Language Mapping**: Different voices for different languages and call types

### Campaign Configuration

Manage campaign scripts:

- **Script Sources**: File system, database, or API
- **Caching**: Configure script caching behavior
- **Custom Scripts**: Enable/disable custom script creation
- **Validation**: Business logic validation rules

### Business Logic Constants

Configurable business rules:

- **Timeouts**: Call, intro, and response timeouts
- **Validation**: Vehicle count, claims, year ranges
- **Transfer**: Default numbers and agent names

### Security and Safety

Safety and compliance settings:

- **Vocabulary Restrictions**: Words/topics to avoid
- **Recording Confirmation**: Compliance messages
- **Content Filtering**: Safety thresholds

## Environment-Specific Configuration

### Development
```bash
NODE_ENV=development
DEBUG_LEVEL=debug
ENABLE_DETAILED_LOGGING=true
```

### Production
```bash
NODE_ENV=production
DEBUG_LEVEL=info
ENABLE_DETAILED_LOGGING=false
ENABLE_CACHING=true
```

## Advanced Configuration

### Campaign-Specific Overrides

Override settings for specific campaigns:

```bash
# Campaign 1 specific settings
TRANSFER_NUMBER_CAMPAIGN_1=555-111-1111
AGENT_NAME_CAMPAIGN_1=John Smith

# Campaign 2 specific settings
TRANSFER_NUMBER_CAMPAIGN_2=555-222-2222
AGENT_NAME_CAMPAIGN_2=Jane Doe
```

### Voice Mapping

Configure voices based on language and call type:

```bash
# English voices
VOICE_EN_INCOMING=empathetic
VOICE_EN_OUTBOUND=relaxed

# Spanish voices
VOICE_ES_INCOMING=empathetic
VOICE_ES_OUTBOUND=energetic

# Czech voices
VOICE_CZ_INCOMING=professional
VOICE_CZ_OUTBOUND=authoritative
```

### Performance Tuning

Optimize performance:

```bash
ENABLE_CACHING=true
CACHE_TIMEOUT=300
MAX_CONCURRENT_CALLS=100
SCRIPT_CACHE_TIMEOUT=300
```

## Configuration Validation

The system validates all configuration on startup:

- **Required Fields**: Ensures all required settings are present
- **Format Validation**: Validates URLs, ports, numbers
- **Range Validation**: Ensures numeric values are within valid ranges
- **Enum Validation**: Validates against allowed values

## Migration from Hardcoded Values

All previously hardcoded values have been moved to configuration:

### Before (Hardcoded)
```javascript
const defaultVoice = 'Kore';
const maxVehicles = 9;
const transferNumber = '555-123-4567';
```

### After (Configurable)
```javascript
const defaultVoice = getConfigValue('ai.gemini.defaultVoice', 'Kore');
const maxVehicles = getConfigValue('business.validation.maxVehicles', 9);
const transferNumber = getConfigValue('business.transfer.defaultTransferNumber');
```

## Configuration API

Access configuration programmatically:

```javascript
import { config, getConfigValue } from './src/config/config.js';

// Direct access
const apiKey = config.auth.gemini.apiKey;

// With fallback
const voice = getConfigValue('ai.gemini.defaultVoice', 'Kore');

// Validation
import { validateConfig } from './src/config/config.js';
validateConfig(); // Throws error if invalid
```

## Troubleshooting

### Common Issues

1. **Missing Required Configuration**
   - Error: "Required configuration missing: GEMINI_API_KEY"
   - Solution: Set the required environment variable

2. **Invalid URL Format**
   - Error: "Invalid URL format for PUBLIC_URL"
   - Solution: Ensure URL starts with http:// or https://

3. **Invalid Port**
   - Error: "Invalid port for PORT: 99999"
   - Solution: Use port between 1-65535

### Debug Configuration

Enable detailed logging to debug configuration issues:

```bash
DEBUG_LEVEL=debug
ENABLE_DETAILED_LOGGING=true
```

## Best Practices

1. **Use Environment Variables**: Never commit sensitive values to code
2. **Validate Early**: Configuration is validated on startup
3. **Document Changes**: Update this file when adding new configuration
4. **Test Thoroughly**: Test with different configuration combinations
5. **Use Defaults**: Provide sensible defaults for optional settings

## Support

For configuration issues:

1. Check this documentation
2. Verify `.env` file format
3. Check application logs for validation errors
4. Ensure all required fields are set
