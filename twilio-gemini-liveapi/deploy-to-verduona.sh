#!/bin/bash

# ===========================================
# 💎 TWILIO GEMINI LIVEAPI DEPLOYMENT SCRIPT
# ===========================================
# Deploy Twilio Gemini LiveAPI to Verduona production
# Usage: ./deploy-to-verduona.sh [--deps-only|--build-only|--backend-only|--frontend-only]

set -e

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

PROJECT_NAME="Twilio Gemini LiveAPI"
BACKEND_PORT=3101
FRONTEND_PORT=3001

echo -e "${BLUE}💎 $PROJECT_NAME Deployment${NC}"
echo "================================"

# Parse arguments
DEPS_ONLY=false
BUILD_ONLY=false
BACKEND_ONLY=false
FRONTEND_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --deps-only)
            DEPS_ONLY=true
            shift
            ;;
        --build-only)
            BUILD_ONLY=true
            shift
            ;;
        --backend-only)
            BACKEND_ONLY=true
            shift
            ;;
        --frontend-only)
            FRONTEND_ONLY=true
            shift
            ;;
        *)
            echo "Unknown option $1"
            exit 1
            ;;
    esac
done

# Validate arguments
if [ "$FRONTEND_ONLY" = true ] && [ "$BACKEND_ONLY" = true ]; then
    echo -e "${RED}❌ Cannot use --frontend-only and --backend-only together${NC}"
    exit 1
fi

# Install dependencies
if [ "$BUILD_ONLY" = false ]; then
    echo -e "${YELLOW}📦 Installing dependencies...${NC}"
    
    # Backend dependencies
    if [ "$FRONTEND_ONLY" = false ]; then
        echo "Installing backend dependencies..."
        pnpm install
    fi

    # Frontend dependencies
    if [ "$BACKEND_ONLY" = false ]; then
        echo "Installing frontend dependencies..."
        cd call-center-frontend
        pnpm install
        cd ..
    fi
    
    if [ "$DEPS_ONLY" = true ]; then
        echo -e "${GREEN}✅ Dependencies installed for $PROJECT_NAME${NC}"
        exit 0
    fi
fi

# Build projects
if [ "$DEPS_ONLY" = false ]; then
    echo -e "${YELLOW}🔧 Building projects...${NC}"
    
    # Backend doesn't typically need building (Node.js)
    if [ "$FRONTEND_ONLY" = false ]; then
        echo "Backend ready (Node.js runtime)"
    fi
    
    # Build frontend
    if [ "$BACKEND_ONLY" = false ]; then
        echo "Building frontend..."
        cd call-center-frontend
        if grep -q '"build"' package.json; then
            export NODE_ENV=production
            export NEXT_TELEMETRY_DISABLED=1
            export NEXT_PUBLIC_BACKEND_URL="https://www.verduona.com/gemini-api"
            export NEXT_PUBLIC_VERSION="gemini"
            
            pnpm run build || {
                echo "Frontend build failed, trying clean build..."
                rm -rf .next node_modules/.cache
                pnpm run build
            }
        fi
        cd ..
    fi
    
    if [ "$BUILD_ONLY" = true ]; then
        echo -e "${GREEN}✅ Build completed for $PROJECT_NAME${NC}"
        exit 0
    fi
fi

echo -e "${GREEN}✅ $PROJECT_NAME deployment preparation complete${NC}"
echo -e "Backend port: $BACKEND_PORT"
echo -e "Frontend port: $FRONTEND_PORT"
echo -e "AI Model: Gemini"
echo -e "Services will be managed by PM2 via ecosystem.config.js"
