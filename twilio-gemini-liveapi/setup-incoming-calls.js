import twilio from 'twilio';
import dotenv from './src/utils/dotenv-stub.js';
import path from 'path';

dotenv.config({ path: path.resolve(process.cwd(), '.env') });

const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const publicUrl = process.env.PUBLIC_URL;
const twilioPhoneNumber = process.env.TWILIO_PHONE_NUMBER_US || process.env.TWILIO_PHONE_NUMBER;

console.log('🔧 Setting up Twilio phone number for incoming calls...');
console.log('📞 Phone Number:', twilioPhoneNumber);
console.log('🌐 Public URL:', publicUrl);

if (!accountSid || !authToken || !publicUrl || !twilioPhoneNumber) {
    console.error('❌ Missing required environment variables:');
    console.error('   TWILIO_ACCOUNT_SID:', accountSid ? '✅' : '❌');
    console.error('   TWILIO_AUTH_TOKEN:', authToken ? '✅' : '❌');
    console.error('   PUBLIC_URL:', publicUrl ? '✅' : '❌');
    console.error('   TWILIO_PHONE_NUMBER:', twilioPhoneNumber ? '✅' : '❌');
    process.exit(1);
}

const client = twilio(accountSid, authToken);

async function setupIncomingCalls() {
    try {
        // First, list all phone numbers to find the one we want to configure
        console.log('📋 Fetching available phone numbers...');
        const phoneNumbers = await client.incomingPhoneNumbers.list();
        
        console.log('Available phone numbers:', phoneNumbers.map(p => ({
            phoneNumber: p.phoneNumber,
            friendlyName: p.friendlyName,
            sid: p.sid
        })));

        // Find the phone number we want to configure
        const targetPhoneNumber = phoneNumbers.find(p => p.phoneNumber === twilioPhoneNumber);
        
        if (!targetPhoneNumber) {
            console.error(`❌ Phone number ${twilioPhoneNumber} not found in your Twilio account.`);
            console.log('Available numbers:', phoneNumbers.map(p => p.phoneNumber));
            process.exit(1);
        }

        console.log(`✅ Found phone number: ${targetPhoneNumber.phoneNumber} (${targetPhoneNumber.friendlyName})`);

        // Configure the webhook URLs
        const voiceUrl = `${publicUrl}/incoming-call`;
        const statusCallbackUrl = `${publicUrl}/call-status`;
        
        console.log(`🔗 Configuring webhooks:`);
        console.log(`   Voice URL: ${voiceUrl}`);
        console.log(`   Status Callback: ${statusCallbackUrl}`);

        // Update the phone number configuration
        const updatedPhoneNumber = await client.incomingPhoneNumbers(targetPhoneNumber.sid)
            .update({
                voiceUrl: voiceUrl,
                voiceMethod: 'POST',
                statusCallback: statusCallbackUrl,
                statusCallbackMethod: 'POST'
            });

        console.log('✅ Phone number configured successfully!');
        console.log('📞 Phone number:', updatedPhoneNumber.phoneNumber);
        console.log('🎯 Voice URL:', updatedPhoneNumber.voiceUrl);
        console.log('📊 Status Callback:', updatedPhoneNumber.statusCallback);
        
        console.log('\n🎉 Setup complete! Your Twilio phone number will now:');
        console.log('   • Automatically answer incoming calls');
        console.log('   • Connect callers to your Gemini AI assistant');
        console.log('   • Use the configured voice and system prompt');
        console.log('   • Generate call summaries after each call');
        
        console.log('\n📞 Test it by calling:', twilioPhoneNumber);
        
    } catch (error) {
        console.error('❌ Error setting up incoming calls:', error);
        if (error.code === 20003) {
            console.error('   Authentication failed. Check your TWILIO_ACCOUNT_SID and TWILIO_AUTH_TOKEN.');
        } else if (error.code === 21421) {
            console.error('   Invalid phone number. Make sure the phone number exists in your Twilio account.');
        }
        process.exit(1);
    }
}

// Also create a function to setup for multiple countries
async function setupMultiCountryIncomingCalls() {
    const phoneNumbers = {
        'US': process.env.TWILIO_PHONE_NUMBER_US,
        'CZ': process.env.TWILIO_PHONE_NUMBER_CZ,
        'ES': process.env.TWILIO_PHONE_NUMBER_ES
    };

    console.log('\n🌍 Setting up multi-country phone numbers...');

    for (const [country, phoneNumber] of Object.entries(phoneNumbers)) {
        if (!phoneNumber || phoneNumber.includes('*********')) {
            console.log(`⏭️ Skipping ${country} - placeholder number: ${phoneNumber}`);
            continue;
        }

        try {
            console.log(`\n🔧 Configuring ${country} number: ${phoneNumber}`);
            
            const twilioPhoneNumbers = await client.incomingPhoneNumbers.list();
            const targetNumber = twilioPhoneNumbers.find(p => p.phoneNumber === phoneNumber);
            
            if (!targetNumber) {
                console.log(`⚠️ ${country} number ${phoneNumber} not found in Twilio account`);
                continue;
            }

            const voiceUrl = `${publicUrl}/incoming-call`;
            const statusCallbackUrl = `${publicUrl}/call-status`;
            
            await client.incomingPhoneNumbers(targetNumber.sid)
                .update({
                    voiceUrl: voiceUrl,
                    voiceMethod: 'POST',
                    statusCallback: statusCallbackUrl,
                    statusCallbackMethod: 'POST'
                });

            console.log(`✅ ${country} number configured successfully!`);
            
        } catch (error) {
            console.error(`❌ Error configuring ${country} number:`, error.message);
        }
    }
}

// Main execution
async function main() {
    await setupIncomingCalls();
    
    // Also setup multi-country numbers if they exist
    await setupMultiCountryIncomingCalls();
    
    console.log('\n🏁 All done! Your Gemini AI is now ready to receive incoming calls.');
}

main().catch(console.error); 