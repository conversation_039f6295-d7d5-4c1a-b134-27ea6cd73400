#!/usr/bin/env node

import WebSocket from 'ws';

const WS_URL = 'ws://localhost:3101/test-inbound';

console.log('🎵 Testing inbound audio - checking if AI responds to customer...');

const ws = new WebSocket(WS_URL);

ws.on('open', () => {
    console.log('🔌 Connected to inbound test endpoint');
    
    // Send start-session message with proper inbound customer service instructions
    const startMessage = {
        type: 'start-session',
        aiInstructions: `You are a professional customer service representative handling inbound calls. Your goal is to help customers with their inquiries.

Key behaviors:
1. Always greet the customer warmly when they speak first
2. Listen carefully to their needs
3. Provide helpful and accurate information
4. Be patient and understanding
5. Ask clarifying questions when needed

Remember: In inbound calls, the CUSTOMER speaks first, then you respond.`,
        voice: 'Kore',
        model: 'gemini-2.5-flash-preview-native-audio-dialog'
    };
    
    console.log('📤 Sending start-session message...');
    ws.send(JSON.stringify(startMessage));
});

ws.on('message', (data) => {
    try {
        const message = JSON.parse(data);
        console.log('📨 Received message:', message.type);
        
        if (message.type === 'session-started') {
            console.log('✅ Session started - simulating customer calling in...');
            
            // Simulate customer calling in and speaking first after 2 seconds
            setTimeout(() => {
                console.log('📞 Simulating customer saying: "Hello, I need help with my account"');
                
                // Generate simple test audio (simulate customer speaking)
                const testAudio = Buffer.from('Hello I need help with my account').toString('base64');
                
                ws.send(JSON.stringify({
                    type: 'audio-data',
                    audioData: testAudio
                }));
            }, 2000);
        } else if (message.type === 'audio') {
            console.log('🔊 SUCCESS: Received audio response from AI!');
            console.log(`   Audio length: ${message.audio?.length || 0} bytes`);
            console.log('🎉 Inbound testing is working - AI is responding to customer!');
            process.exit(0);
        }
    } catch (error) {
        console.error('❌ Error parsing message:', error);
    }
});

ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error);
    process.exit(1);
});

ws.on('close', () => {
    console.log('🔌 WebSocket connection closed');
    process.exit(1);
});

// Timeout after 30 seconds
setTimeout(() => {
    console.log('⏰ Test timeout - no AI response received');
    process.exit(1);
}, 30000);
