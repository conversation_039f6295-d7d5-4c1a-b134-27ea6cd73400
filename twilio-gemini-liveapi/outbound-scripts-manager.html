<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Outbound Call Scripts Manager</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .glass-effect {
            backdrop-filter: blur(16px);
            background: rgba(255, 255, 255, 0.95);
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .script-card {
            transition: all 0.3s ease;
        }
        .script-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .status-indicator {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <div x-data="incomingScriptManager()" class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="glass-effect rounded-2xl p-8 mb-8 fade-in">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h1 class="text-4xl font-bold text-gray-800 mb-2">
                        📞 Advanced Outbound Call Scripts Manager
                    </h1>
                    <p class="text-gray-600 text-lg">
                        Sophisticated campaign management for outbound calls - when you call customers
                    </p>
                </div>
                <div class="text-right">
                    <div class="flex items-center space-x-2 mb-2">
                        <div :class="isConnected ? 'bg-green-500' : 'bg-red-500'" 
                             class="w-3 h-3 rounded-full status-indicator"></div>
                        <span class="text-sm font-medium" 
                              :class="isConnected ? 'text-green-700' : 'text-red-700'"
                              x-text="isConnected ? 'Connected' : 'Disconnected'"></span>
                    </div>
                    <div class="text-sm text-gray-600">
                        Server: <span x-text="backendUrl"></span>
                    </div>
                </div>
            </div>
            
            <!-- Current Script Status -->
            <div class="bg-blue-50 border border-blue-200 rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-blue-800 mb-1">Current Active Script</h3>
                        <div class="flex items-center space-x-4">
                            <span class="text-2xl font-bold text-blue-900" x-text="currentScript?.name || 'Loading...'"></span>
                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium"
                                  x-text="currentScript?.agentPersona || 'N/A'"></span>
                        </div>
                        <p class="text-blue-700 mt-1" x-text="currentScript?.description || ''"></p>
                    </div>
                    <div class="text-right">
                        <div class="text-sm text-blue-600">Voice: <span x-text="currentScript?.voice || 'N/A'"></span></div>
                        <div class="text-sm text-blue-600">Model: <span x-text="currentScript?.model || 'N/A'"></span></div>
                        <div class="text-sm text-blue-600">Language: <span x-text="currentScript?.language || 'N/A'"></span></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scripts Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
            <template x-for="script in availableScripts" :key="script.id">
                <div class="glass-effect rounded-xl p-6 script-card"
                     :class="currentScript?.id === script.id ? 'ring-4 ring-blue-500' : ''">
                    
                    <!-- Script Header -->
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                            <h3 class="text-xl font-bold text-gray-800 mb-1" x-text="script.name"></h3>
                            <p class="text-sm text-gray-600 mb-2" x-text="script.description"></p>
                            <div class="flex items-center space-x-2">
                                <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded-md text-xs font-medium"
                                      x-text="script.agentPersona"></span>
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded-md text-xs font-medium"
                                      x-text="script.voice"></span>
                            </div>
                        </div>
                        <div class="flex flex-col items-end space-y-1">
                            <span :class="currentScript?.id === script.id ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'"
                                  class="px-2 py-1 rounded-full text-xs font-medium">
                                <span x-show="currentScript?.id === script.id">ACTIVE</span>
                                <span x-show="currentScript?.id !== script.id">INACTIVE</span>
                            </span>
                        </div>
                    </div>

                    <!-- Script Metrics -->
                    <div class="bg-gray-50 rounded-lg p-4 mb-4">
                        <div class="grid grid-cols-2 gap-4 text-center">
                            <div>
                                <div class="text-2xl font-bold text-gray-800" x-text="script.callCount || 0"></div>
                                <div class="text-xs text-gray-600">Total Calls</div>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-gray-800" 
                                     x-text="script.averageHandleTime ? Math.round(script.averageHandleTime) + 's' : 'N/A'"></div>
                                <div class="text-xs text-gray-600">Avg Handle Time</div>
                            </div>
                        </div>
                        <div class="mt-2 text-center">
                            <div class="text-xs text-gray-500">
                                Last Used: <span x-text="script.lastUsed ? formatDate(script.lastUsed) : 'Never'"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Campaign Details -->
                    <div class="bg-blue-50 rounded-lg p-3 mb-4">
                        <div class="text-sm font-medium text-blue-800 mb-1">Campaign</div>
                        <div class="text-sm text-blue-700" x-text="script.campaign || 'N/A'"></div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex space-x-2">
                        <button @click="activateScript(script.id)"
                                :disabled="currentScript?.id === script.id || isLoading"
                                class="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                            <span x-show="!isLoading || loadingScript !== script.id">
                                <span x-show="currentScript?.id === script.id">✓ Active</span>
                                <span x-show="currentScript?.id !== script.id">Activate</span>
                            </span>
                            <span x-show="isLoading && loadingScript === script.id">
                                🔄 Loading...
                            </span>
                        </button>
                        <button @click="showScriptDetails(script)" 
                                class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                            📋 Details
                        </button>
                    </div>
                </div>
            </template>
        </div>

        <!-- Advanced Features Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Script Analytics -->
            <div class="glass-effect rounded-xl p-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">📊 Script Analytics</h3>
                <div class="space-y-4">
                    <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-lg">
                        <div class="flex justify-between items-center">
                            <span>Total Outbound Calls Today</span>
                            <span class="text-2xl font-bold" x-text="analytics.totalCalls"></span>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-green-500 to-teal-600 text-white p-4 rounded-lg">
                        <div class="flex justify-between items-center">
                            <span>Average Handle Time</span>
                            <span class="text-2xl font-bold" x-text="analytics.avgHandleTime + 's'"></span>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-orange-500 to-red-600 text-white p-4 rounded-lg">
                        <div class="flex justify-between items-center">
                            <span>Active Calls Now</span>
                            <span class="text-2xl font-bold" x-text="analytics.activeCalls"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Call History -->
            <div class="glass-effect rounded-xl p-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">📈 Recent Call History</h3>
                <div class="space-y-2 max-h-64 overflow-y-auto">
                    <template x-for="call in callHistory.slice(0, 8)" :key="call.callSid">
                        <div class="bg-gray-50 p-3 rounded-lg flex justify-between items-center">
                            <div>
                                <div class="font-medium text-gray-800" x-text="getScriptName(call.scriptId)"></div>
                                <div class="text-sm text-gray-600" x-text="formatDate(call.endTime)"></div>
                            </div>
                            <div class="text-right">
                                <div class="font-medium" x-text="Math.round(call.duration) + 's'"></div>
                                <div class="text-sm" 
                                     :class="getDispositionColor(call.disposition)"
                                     x-text="call.disposition"></div>
                            </div>
                        </div>
                    </template>
                    <div x-show="callHistory.length === 0" class="text-center text-gray-500 py-4">
                        No recent calls
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Custom Script Section -->
        <div class="glass-effect rounded-xl p-6 mb-8">
            <h3 class="text-xl font-bold text-gray-800 mb-4">🛠️ Create Custom Script</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Script Name</label>
                    <input x-model="newScript.name" type="text" 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           placeholder="e.g., Custom Support Script">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Agent Name</label>
                    <input x-model="newScript.agentName" type="text" 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           placeholder="e.g., Taylor Smith">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Voice</label>
                    <select x-model="newScript.voice" 
                            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="Kore">Kore (Professional Female)</option>
                        <option value="Puck">Puck (Energetic Male)</option>
                        <option value="Charon">Charon (Warm Baritone)</option>
                        <option value="Fenrir">Fenrir (Authoritative Mid-range)</option>
                        <option value="Orus">Orus (Calm Male)</option>
                        <option value="Aoede">Aoede (Gentle Female)</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Language</label>
                    <select x-model="newScript.language" 
                            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="en">English</option>
                        <option value="cs">Czech</option>
                        <option value="es">Spanish</option>
                    </select>
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea x-model="newScript.description" rows="2"
                              class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="Brief description of the script purpose"></textarea>
                </div>
                <div class="md:col-span-2">
                    <button @click="createCustomScript()" :disabled="isLoading"
                            class="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-2 px-6 rounded-lg transition duration-200">
                        🚀 Create Custom Script
                    </button>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="glass-effect rounded-xl p-6">
            <h3 class="text-xl font-bold text-gray-800 mb-4">🔧 System Status</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="flex items-center justify-between">
                        <span class="text-blue-800 font-medium">Backend Server</span>
                        <div :class="isConnected ? 'bg-green-500' : 'bg-red-500'" 
                             class="w-3 h-3 rounded-full"></div>
                    </div>
                    <div class="text-sm text-blue-600 mt-1" x-text="isConnected ? 'Online' : 'Offline'"></div>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="flex items-center justify-between">
                        <span class="text-green-800 font-medium">Twilio Integration</span>
                        <div class="bg-green-500 w-3 h-3 rounded-full"></div>
                    </div>
                    <div class="text-sm text-green-600 mt-1">Active</div>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="flex items-center justify-between">
                        <span class="text-purple-800 font-medium">Gemini AI</span>
                        <div class="bg-green-500 w-3 h-3 rounded-full"></div>
                    </div>
                    <div class="text-sm text-purple-600 mt-1">Connected</div>
                </div>
            </div>
        </div>

        <!-- Script Details Modal -->
        <div x-show="showModal" x-transition 
             class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-xl p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold" x-text="selectedScript?.name"></h3>
                    <button @click="showModal = false" class="text-gray-500 hover:text-gray-700">
                        ✕
                    </button>
                </div>
                <div class="space-y-4">
                    <div>
                        <strong>Campaign:</strong> <span x-text="selectedScript?.campaign"></span>
                    </div>
                    <div>
                        <strong>Agent Persona:</strong> <span x-text="selectedScript?.agentPersona"></span>
                    </div>
                    <div>
                        <strong>Description:</strong> <span x-text="selectedScript?.description"></span>
                    </div>
                    <div>
                        <strong>Voice & Model:</strong> 
                        <span x-text="selectedScript?.voice + ' | ' + selectedScript?.model"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <div x-show="message" x-transition 
             class="fixed bottom-4 right-4 p-4 rounded-lg text-white font-medium"
             :class="messageType === 'success' ? 'bg-green-500' : 'bg-red-500'">
            <span x-text="message"></span>
        </div>
    </div>

    <script>
        function incomingScriptManager() {
            return {
                backendUrl: window.location.origin,
                isConnected: false,
                isLoading: false,
                loadingScript: null,
                availableScripts: [],
                currentScript: null,
                callHistory: [],
                analytics: {
                    totalCalls: 0,
                    avgHandleTime: 0,
                    activeCalls: 0
                },
                showModal: false,
                selectedScript: null,
                message: '',
                messageType: 'success',
                newScript: {
                    name: '',
                    agentName: '',
                    voice: 'Kore',
                    language: 'en',
                    description: ''
                },

                async init() {
                    await this.checkConnection();
                    if (this.isConnected) {
                        await this.loadData();
                        this.startPeriodicUpdates();
                    }
                },

                async checkConnection() {
                    try {
                        const response = await fetch(`${this.backendUrl}/health`);
                        this.isConnected = response.ok;
                    } catch (error) {
                        this.isConnected = false;
                    }
                },

                async loadData() {
                    try {
                        // Load available scripts
                        const scriptsResponse = await fetch(`${this.backendUrl}/api/incoming-scripts`);
                        if (scriptsResponse.ok) {
                            this.availableScripts = await scriptsResponse.json();
                        }

                        // Load current script
                        const currentResponse = await fetch(`${this.backendUrl}/api/incoming-scripts/current`);
                        if (currentResponse.ok) {
                            this.currentScript = await currentResponse.json();
                        }

                        // Load call history
                        const historyResponse = await fetch(`${this.backendUrl}/api/incoming-scripts/history`);
                        if (historyResponse.ok) {
                            this.callHistory = await historyResponse.json();
                        }

                        // Load analytics
                        const analyticsResponse = await fetch(`${this.backendUrl}/api/incoming-scripts/analytics`);
                        if (analyticsResponse.ok) {
                            this.analytics = await analyticsResponse.json();
                        }
                    } catch (error) {
                        console.error('Error loading data:', error);
                    }
                },

                async activateScript(scriptId) {
                    this.isLoading = true;
                    this.loadingScript = scriptId;
                    
                    try {
                        const response = await fetch(`${this.backendUrl}/api/incoming-scripts/set/${scriptId}`, {
                            method: 'POST'
                        });
                        
                        if (response.ok) {
                            this.currentScript = this.availableScripts.find(s => s.id === scriptId);
                            this.showMessage('Script activated successfully!', 'success');
                        } else {
                            throw new Error('Failed to activate script');
                        }
                    } catch (error) {
                        this.showMessage('Error activating script: ' + error.message, 'error');
                    } finally {
                        this.isLoading = false;
                        this.loadingScript = null;
                    }
                },

                async createCustomScript() {
                    if (!this.newScript.name || !this.newScript.agentName) {
                        this.showMessage('Please fill in script name and agent name', 'error');
                        return;
                    }

                    this.isLoading = true;
                    
                    try {
                        const scriptData = {
                            id: this.newScript.name.toLowerCase().replace(/\s+/g, '-'),
                            name: this.newScript.name,
                            description: this.newScript.description,
                            agentPersona: {
                                name: this.newScript.agentName,
                                tone: 'Professional, helpful'
                            },
                            voice: this.newScript.voice,
                            language: this.newScript.language
                        };

                        const response = await fetch(`${this.backendUrl}/api/incoming-scripts/create`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(scriptData)
                        });

                        if (response.ok) {
                            this.showMessage('Custom script created successfully!', 'success');
                            this.newScript = { name: '', agentName: '', voice: 'Kore', language: 'en', description: '' };
                            await this.loadData();
                        } else {
                            throw new Error('Failed to create custom script');
                        }
                    } catch (error) {
                        this.showMessage('Error creating script: ' + error.message, 'error');
                    } finally {
                        this.isLoading = false;
                    }
                },

                showScriptDetails(script) {
                    this.selectedScript = script;
                    this.showModal = true;
                },

                formatDate(dateString) {
                    return new Date(dateString).toLocaleString();
                },

                getScriptName(scriptId) {
                    const script = this.availableScripts.find(s => s.id === scriptId);
                    return script ? script.name : scriptId;
                },

                getDispositionColor(disposition) {
                    const colors = {
                        'completed': 'text-green-600',
                        'transferred': 'text-blue-600',
                        'callback': 'text-yellow-600',
                        'failed': 'text-red-600'
                    };
                    return colors[disposition.toLowerCase()] || 'text-gray-600';
                },

                showMessage(text, type = 'success') {
                    this.message = text;
                    this.messageType = type;
                    setTimeout(() => {
                        this.message = '';
                    }, 3000);
                },

                startPeriodicUpdates() {
                    setInterval(async () => {
                        if (this.isConnected) {
                            await this.loadData();
                        }
                    }, 30000); // Update every 30 seconds
                }
            }
        }
    </script>
</body>
</html> 