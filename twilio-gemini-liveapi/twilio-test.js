import twilio from 'twilio';
import dotenv from './src/utils/dotenv-stub.js';
import path from 'path';

dotenv.config({ path: path.resolve(process.cwd(), '.env') });

const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const publicUrl = process.env.PUBLIC_URL;

console.log('Testing Twilio credentials...');
console.log('Account SID:', accountSid);
console.log('Auth Token:', authToken);
console.log('Public URL:', publicUrl);

const client = twilio(accountSid, authToken, { baseUrl: '' });

// First verify we can list phone numbers
client.incomingPhoneNumbers
    .list()
    .then(async phoneNumbers => {
        console.log('Successfully authenticated with Twilio!');
        console.log('Available phone numbers:', phoneNumbers.map(p => p.phoneNumber));

        // Now try to make a call
        try {
            const call = await client.calls.create({
                url: `${publicUrl}/incoming-call`,
                to: process.env.DEFAULT_PHONE_NUMBER,
                from: process.env.TWILIO_PHONE_NUMBER,
                statusCallback: `${publicUrl}/call-status`,
                statusCallbackEvent: ['completed']
            });
      
            console.log('Successfully initiated call:', call.sid);
        } catch (error) {
            console.error('Error making call:', error);
        }
    })
    .catch(error => {
        console.error('Twilio authentication error:', error);
    });