#!/usr/bin/env node

import { GoogleGenerativeAI } from '@google/generative-ai';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🧪 Testing Gemini Text-to-Text API...\n');

const API_KEY = process.env.GEMINI_API_KEY;
console.log(`📝 API Key loaded: ${API_KEY ? 'Yes' : 'No'}`);
console.log(`📝 API Key: ${API_KEY}\n`);

if (!API_KEY) {
    console.error('❌ No API key found in environment!');
    process.exit(1);
}

async function testTextGeneration() {
    try {
        // Initialize the Gemini client
        const genAI = new GoogleGenerativeAI(API_KEY);
        console.log('✅ Gemini client initialized\n');

        // Use gemini-pro for text generation
        console.log('🔍 Getting gemini-pro model...');
        const model = genAI.getGenerativeModel({ model: "gemini-pro" });
        console.log('✅ Model loaded\n');

        // Test 1: Simple text generation
        console.log('📝 Test 1: Simple text generation');
        console.log('Prompt: "Hello, please respond with a greeting"');
        
        const result1 = await model.generateContent("Hello, please respond with a greeting");
        const response1 = await result1.response;
        const text1 = response1.text();
        
        console.log('Response:', text1);
        console.log('✅ Test 1 passed\n');

        // Test 2: More complex prompt
        console.log('📝 Test 2: Complex prompt');
        console.log('Prompt: "What is 2+2? Answer with just the number"');
        
        const result2 = await model.generateContent("What is 2+2? Answer with just the number");
        const response2 = await result2.response;
        const text2 = response2.text();
        
        console.log('Response:', text2);
        console.log('✅ Test 2 passed\n');

        console.log('🎉 All tests passed! API key is valid and working.');

    } catch (error) {
        console.error('❌ Error:', error.message);
        if (error.message.includes('API_KEY_INVALID')) {
            console.error('❌ The API key is invalid!');
        } else if (error.message.includes('quota')) {
            console.error('❌ API quota exceeded!');
        } else {
            console.error('❌ Error details:', error);
        }
        process.exit(1);
    }
}

// Run the test
testTextGeneration();