<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Incoming Call Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body class="bg-gray-100" x-data="incomingManager">
    <div class="container mx-auto p-6">
        <div class="flex items-center justify-between mb-6">
            <h1 class="text-3xl font-bold">Incoming Call Management</h1>
            
            <!-- Quick Scenario Selector -->
            <div class="flex items-center space-x-3">
                <label class="text-sm font-medium text-gray-700">Quick Select:</label>
                <select class="border border-gray-300 rounded-md px-3 py-2 bg-white text-sm"
                        x-model="quickSelectValue"
                        @change="selectScenario(quickSelectValue)">
                    <template x-for="scenario in scenarios" :key="scenario.id">
                        <option :value="scenario.id" 
                                :selected="scenario.id === currentScenario?.id"
                                x-text="`${getScenarioIcon(scenario.category)} ${scenario.name}`">
                        </option>
                    </template>
                </select>
                <button @click="loadData()" 
                        class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-md text-sm transition-colors duration-200">
                    🔄 Refresh
                </button>
            </div>
        </div>
        
        <!-- Stats -->
        <div class="grid grid-cols-4 gap-4 mb-6">
            <div class="bg-white p-4 rounded shadow">
                <h3 class="text-sm text-gray-600">Total Calls</h3>
                <p class="text-2xl font-bold" x-text="analytics.totalCalls || 0"></p>
            </div>
            <div class="bg-white p-4 rounded shadow">
                <h3 class="text-sm text-gray-600">Avg Handle Time</h3>
                <p class="text-2xl font-bold" x-text="formatTime(analytics.avgHandleTime || 0)"></p>
            </div>
            <div class="bg-white p-4 rounded shadow">
                <h3 class="text-sm text-gray-600">Active Calls</h3>
                <p class="text-2xl font-bold" x-text="analytics.activeCalls || 0"></p>
            </div>
            <div class="bg-white p-4 rounded shadow">
                <h3 class="text-sm text-gray-600">Current Scenario</h3>
                <p class="text-lg font-bold" x-text="currentScenario?.name || 'Loading...'"></p>
            </div>
        </div>

        <!-- Scenario Selection -->
        <div class="bg-white rounded shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold">📋 Select Active Scenario</h2>
                <div class="text-sm text-gray-600">
                    Next incoming call will use: <span class="font-semibold text-green-600" x-text="currentScenario?.name"></span>
                </div>
            </div>
            
            <div class="space-y-3">
                <template x-for="scenario in scenarios" :key="scenario.id">
                    <div class="relative p-4 border rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md" 
                         :class="{ 
                             'bg-green-50 border-green-400 shadow-md': scenario.id === currentScenario?.id,
                             'bg-white border-gray-200 hover:border-gray-300': scenario.id !== currentScenario?.id
                         }"
                         @click="selectScenario(scenario.id)">
                        
                        <!-- Active Indicator -->
                        <div x-show="scenario.id === currentScenario?.id" 
                             class="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                            ✓ ACTIVE
                        </div>
                        
                        <!-- Scenario Info -->
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-3">
                                    <div class="text-2xl" x-text="getScenarioIcon(scenario.category)"></div>
                                    <div>
                                        <h3 class="font-semibold text-gray-900" x-text="scenario.name"></h3>
                                        <p class="text-sm text-gray-600" x-text="scenario.description"></p>
                                    </div>
                                </div>
                                
                                <div class="mt-3 grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="text-gray-500">👤 Agent:</span>
                                        <span class="font-medium ml-1" x-text="scenario.agent"></span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">🎤 Voice:</span>
                                        <span class="font-medium ml-1" x-text="scenario.voice"></span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">📞 Calls:</span>
                                        <span class="font-medium ml-1" x-text="scenario.callCount"></span>
                                    </div>
                                    <div x-show="scenario.avgHandleTime">
                                        <span class="text-gray-500">⏱️ Avg Time:</span>
                                        <span class="font-medium ml-1" x-text="formatTime(scenario.avgHandleTime)"></span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Selection Radio -->
                            <div class="ml-4 flex items-center">
                                <div class="w-6 h-6 rounded-full border-2 flex items-center justify-center"
                                     :class="scenario.id === currentScenario?.id ? 'border-green-500 bg-green-500' : 'border-gray-300'">
                                    <div x-show="scenario.id === currentScenario?.id" class="w-3 h-3 bg-white rounded-full"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Click to select hint -->
                        <div x-show="scenario.id !== currentScenario?.id" 
                             class="mt-3 text-center text-xs text-gray-400 border-t pt-2">
                            Click to activate for incoming calls
                        </div>
                    </div>
                </template>
            </div>
            
            <!-- Current Selection Summary -->
            <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h4 class="font-semibold text-blue-900 mb-2">🎯 Currently Active for Incoming Calls</h4>
                <template x-if="currentScenario">
                    <div class="text-sm text-blue-800">
                        <div class="font-medium" x-text="currentScenario.name"></div>
                        <div class="mt-1" x-text="currentScenario.description"></div>
                        <div class="mt-2 text-xs">
                            Agent: <span x-text="currentScenario.agent"></span> | 
                            Voice: <span x-text="currentScenario.voice"></span> | 
                            Model: <span x-text="currentScenario.model"></span>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.data('incomingManager', () => ({
                scenarios: [],
                currentScenario: null,
                analytics: {},
                quickSelectValue: '',

                async init() {
                    await this.loadData();
                    setInterval(() => this.loadData(), 30000);
                },

                async loadData() {
                    try {
                        const response = await fetch('/api/incoming-scenarios');
                        if (response.ok) {
                            const data = await response.json();
                            this.scenarios = data.scenarios || [];
                            this.currentScenario = data.currentScenario;
                            this.quickSelectValue = data.currentScenario?.id || '';
                        }

                        const analyticsResponse = await fetch('/api/incoming-analytics');
                        if (analyticsResponse.ok) {
                            this.analytics = await analyticsResponse.json();
                        }
                    } catch (error) {
                        console.error('Error loading data:', error);
                    }
                },

                async selectScenario(scenarioId) {
                    try {
                        const response = await fetch('/api/incoming-scenarios/select', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ scenarioId })
                        });

                        if (response.ok) {
                            await this.loadData();
                            this.quickSelectValue = scenarioId;
                        }
                    } catch (error) {
                        console.error('Error selecting scenario:', error);
                    }
                },

                formatTime(seconds) {
                    if (!seconds) return '0s';
                    const mins = Math.floor(seconds / 60);
                    const secs = seconds % 60;
                    return mins > 0 ? `${mins}m ${secs}s` : `${secs}s`;
                },

                getScenarioIcon(category) {
                    const icons = {
                        'Customer Service': '🎧',
                        'Sales': '💼',
                        'Technical Support': '🔧',
                        'Emergency': '🚨',
                        'Custom': '⚙️'
                    };
                    return icons[category] || '📞';
                }
            }));
        });
    </script>
</body>
</html> 