/**
 * Conversation Context Manager
 * 
 * Implements patterns from the Conversation Context Cookbook:
 * - Recipe #3: Hybrid Window + Summary
 * - Recipe #6: Role Separation & Pinned Instructions
 * - Recipe #8: Token Budgeting & Cost Control
 */

export class ConversationContextManager {
    constructor(options = {}) {
        this.maxRecentTurns = options.maxRecentTurns || 15;
        this.maxRequestTokens = options.maxRequestTokens || 12000;
        this.summaryThreshold = options.summaryThreshold || 8000;
        this.sessions = new Map(); // sessionId -> context
    }

    /**
     * Initialize context for a new session
     * Recipe #6: Role Separation & Pinned Instructions
     */
    initializeSession(sessionId, systemInstructions, campaignScript) {
        const context = {
            systemInstructions,
            campaignScript,
            conversationSummary: '',
            recentTurns: [],
            totalTokens: 0,
            lastSummaryUpdate: Date.now()
        };
        
        this.sessions.set(sessionId, context);
        console.log(`📝 [${sessionId}] Context initialized with ${campaignScript?.length || 0} char campaign script`);
        return context;
    }

    /**
     * Add a new conversation turn
     * Recipe #3: Hybrid Window + Summary
     */
    addTurn(sessionId, role, content) {
        const context = this.sessions.get(sessionId);
        if (!context) {
            console.warn(`⚠️ [${sessionId}] No context found for session`);
            return null;
        }

        const turn = {
            role,
            content,
            timestamp: Date.now(),
            tokens: this.estimateTokens(content)
        };

        context.recentTurns.push(turn);
        context.totalTokens += turn.tokens;

        // Recipe #2: Summarization Buffer - trigger when needed
        if (context.totalTokens > this.summaryThreshold) {
            this.summarizeOldTurns(sessionId);
        }

        // Recipe #1: Sliding Window - keep only recent turns
        if (context.recentTurns.length > this.maxRecentTurns) {
            const removed = context.recentTurns.shift();
            context.totalTokens -= removed.tokens;
        }

        console.log(`💬 [${sessionId}] Added ${role} turn (${turn.tokens} tokens, total: ${context.totalTokens})`);
        return context;
    }

    /**
     * Get formatted messages for AI model
     * Recipe #3: Hybrid Window + Summary + Recipe #6: Role Separation
     */
    getFormattedMessages(sessionId) {
        const context = this.sessions.get(sessionId);
        if (!context) return [];

        const messages = [];

        // System instructions (pinned)
        if (context.systemInstructions) {
            messages.push({
                role: 'system',
                content: context.systemInstructions
            });
        }

        // Campaign script (pinned)
        if (context.campaignScript) {
            messages.push({
                role: 'system',
                content: `Campaign Script:\n${context.campaignScript}`
            });
        }

        // Conversation summary (if exists)
        if (context.conversationSummary) {
            messages.push({
                role: 'system',
                content: `Conversation Summary: ${context.conversationSummary}`
            });
        }

        // Recent turns (sliding window)
        context.recentTurns.forEach(turn => {
            messages.push({
                role: turn.role,
                content: turn.content
            });
        });

        return messages;
    }

    /**
     * Recipe #2: Summarization Buffer
     */
    async summarizeOldTurns(sessionId) {
        const context = this.sessions.get(sessionId);
        if (!context || context.recentTurns.length < 5) return;

        try {
            // Take first half of turns for summarization
            const turnsToSummarize = context.recentTurns.splice(0, Math.floor(context.recentTurns.length / 2));
            
            const conversationText = turnsToSummarize
                .map(turn => `${turn.role}: ${turn.content}`)
                .join('\n');

            // Simple summarization (in production, use AI model)
            const newSummary = this.createSimpleSummary(conversationText);
            
            if (context.conversationSummary) {
                context.conversationSummary += ` | ${newSummary}`;
            } else {
                context.conversationSummary = newSummary;
            }

            // Update token count
            const removedTokens = turnsToSummarize.reduce((sum, turn) => sum + turn.tokens, 0);
            context.totalTokens -= removedTokens;
            context.lastSummaryUpdate = Date.now();

            console.log(`📋 [${sessionId}] Summarized ${turnsToSummarize.length} turns, saved ${removedTokens} tokens`);
        } catch (error) {
            console.error(`❌ [${sessionId}] Error summarizing turns:`, error);
        }
    }

    /**
     * Simple summarization (placeholder for AI-based summarization)
     */
    createSimpleSummary(conversationText) {
        const lines = conversationText.split('\n').slice(0, 10);
        return `Previous conversation covered: ${lines.length} exchanges about campaign topics`;
    }

    /**
     * Recipe #8: Token Budgeting
     */
    estimateTokens(text) {
        // Simple estimation: ~4 chars per token
        return Math.ceil(text.length / 4);
    }

    /**
     * Get context statistics
     */
    getStats(sessionId) {
        const context = this.sessions.get(sessionId);
        if (!context) return null;

        return {
            totalTokens: context.totalTokens,
            recentTurns: context.recentTurns.length,
            hasSummary: !!context.conversationSummary,
            lastSummaryUpdate: context.lastSummaryUpdate
        };
    }

    /**
     * Clean up session context
     */
    clearSession(sessionId) {
        const deleted = this.sessions.delete(sessionId);
        if (deleted) {
            console.log(`🗑️ [${sessionId}] Context cleared`);
        }
        return deleted;
    }
}

export default ConversationContextManager;
