export class ScriptCache {
    constructor(timeout = 300000) {
        this.cache = new Map();
        this.timestamps = new Map();
        this.timeout = timeout;
    }

    set(key, value) {
        this.cache.set(key, value);
        this.timestamps.set(key, Date.now());
    }

    get(key) {
        const ts = this.timestamps.get(key);
        if (!ts) return null;
        if (Date.now() - ts > this.timeout) {
            this.cache.delete(key);
            this.timestamps.delete(key);
            return null;
        }
        return this.cache.get(key);
    }
}
