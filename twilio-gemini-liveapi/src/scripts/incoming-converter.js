import { loadCampaignScript } from '../../campaign-script-loader.js';

export function convertIncomingScenarioToCampaignScript(incomingScenario) {
    let campaignId = 1;
    if (incomingScenario.id && typeof incomingScenario.id === 'string') {
        const match = incomingScenario.id.match(/(\d+)/);
        if (match) {
            campaignId = parseInt(match[1]);
        }
    }
    if (campaignId < 1 || campaignId > 6) {
        campaignId = 1;
    }
    console.log(`🔄 [Gemini] Loading real incoming campaign ${campaignId} for scenario: ${incomingScenario.name}`);
    const realCampaignScript = loadCampaignScript(campaignId, 'incoming', false);
    if (realCampaignScript) {
        console.log(`✅ [Gemini] Using real incoming campaign script: ${realCampaignScript.title}`);
        return realCampaignScript;
    }
    console.warn(`⚠️ [Gemini] Failed to load real campaign ${campaignId}, using minimal fallback`);
    return {
        id: campaignId,
        type: 'incoming',
        language: 'en',
        category: 'support',
        title: `Campaign ${campaignId} (Incoming)`,
        campaign: 'Customer Support',
        agentPersona: {
            name: 'Support Agent',
            tone: '',
            humanEmulation: true
        },
        script: {
            start: [
                { type: 'statement', content: '' }
            ]
        }
    };
}
