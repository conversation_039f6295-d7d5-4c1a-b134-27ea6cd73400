import { AudioEnhancer } from '../../audio-enhancer.js';

// Enhanced Audio conversion utilities for Twilio μ-law ↔ Gemini PCM with quality improvements
export class AudioProcessor {
    // Static audio enhancer instance
    static audioEnhancer = new AudioEnhancer();

    constructor() {
        // μ-law to linear conversion table (pre-computed for performance)
        this.ULAW_TO_LINEAR = new Int16Array(256);
        this.initializeUlawTable();

        // Audio quality settings
        this.audioSettings = {
            enableDeEssing: false,
            enableNoiseReduction: false,
            enableCompression: false,
            enableAGC: false,
            compressionRatio: 2.0,
            noiseThreshold: 0.01,
            agcTargetLevel: 0.7
        };
    }

    // Initialize μ-law to linear conversion table
    initializeUlawTable() {
        for (let i = 0; i < 256; i++) {
            const ulaw = ~i;
            let t = ((ulaw & 0x0F) << 3) + 0x84;
            t <<= ((ulaw & 0x70) >> 4);
            this.ULAW_TO_LINEAR[i] = (ulaw & 0x80) ? (0x84 - t) : (t - 0x84);
        }
    }

    /**
     * Converts Twilio's G.711 μ-law audio to Int16 PCM format for Gemini compatibility.
     * Enhanced with noise reduction and quality improvements.
     * @param audioBuffer - Input audio data in μ-law format from Twilio
     * @param skipEnhancement - Skip audio enhancement for testing
     * @returns Converted audio data in Int16 PCM format
     */
    convertUlawToPCM(audioBuffer, skipEnhancement = false) {
        try {
            // Validate input
            if (!audioBuffer || audioBuffer.length === 0) {
                console.warn('❌ Invalid audio buffer: null or empty');
                return Buffer.alloc(0);
            }
            if (!Buffer.isBuffer(audioBuffer)) {
                console.error('❌ Input must be a Buffer');
                return Buffer.alloc(0);
            }
            
            // Limit buffer size to prevent memory issues
            const MAX_BUFFER_SIZE = 10 * 1024 * 1024; // 10MB max
            if (audioBuffer.length * 2 > MAX_BUFFER_SIZE) {
                console.error('❌ Audio buffer too large:', audioBuffer.length);
                return Buffer.alloc(0);
            }
            
            // Convert G.711 μ-law to 16-bit PCM
            const int16Buffer = Buffer.alloc(audioBuffer.length * 2);
            for (let i = 0; i < audioBuffer.length; i++) {
                // Ensure ulaw sample is in valid range (0-255)
                const ulawSample = Math.max(0, Math.min(255, audioBuffer[i] || 0));
                const pcmSample = this.ULAW_TO_LINEAR[ulawSample] || 0;
                int16Buffer.writeInt16LE(pcmSample, i * 2);
            }

            // Apply advanced audio enhancement unless skipped
            return skipEnhancement ? int16Buffer : this.enhanceAudioQualityAdvanced(int16Buffer);
        } catch (error) {
            console.error('❌ Error in μ-law to PCM conversion:', error);
            // Fallback to basic conversion without enhancement
            return this.basicUlawToPCM(audioBuffer);
        }
    }

    /**
     * Basic μ-law to PCM conversion without enhancements (fallback)
     */
    basicUlawToPCM(audioBuffer) {
        if (!audioBuffer || !Buffer.isBuffer(audioBuffer) || audioBuffer.length === 0) {
            return Buffer.alloc(0);
        }
        
        const int16Buffer = Buffer.alloc(audioBuffer.length * 2);
        for (let i = 0; i < audioBuffer.length; i++) {
            const ulawSample = Math.max(0, Math.min(255, audioBuffer[i] || 0));
            const pcmSample = this.ULAW_TO_LINEAR[ulawSample] || 0;
            int16Buffer.writeInt16LE(pcmSample, i * 2);
        }
        return int16Buffer;
    }

    /**
     * Advanced audio quality improvement using sophisticated algorithms
     * @param pcmBuffer - Raw PCM audio buffer
     * @returns Enhanced PCM audio buffer
     */
    enhanceAudioQualityAdvanced(pcmBuffer) {
        try {
            // Convert to float32 for processing
            const samples = new Float32Array(pcmBuffer.length / 2);
            for (let i = 0; i < samples.length; i++) {
                samples[i] = pcmBuffer.readInt16LE(i * 2) / 32768.0;
            }

            // Apply advanced audio enhancement
            const enhancementOptions = {
                noiseReduction: process.env.AUDIO_NOISE_REDUCTION !== 'false',
                compression: process.env.AUDIO_COMPRESSION !== 'false',
                agc: process.env.AUDIO_AGC !== 'false',
                voiceEnhancement: process.env.AUDIO_VOICE_ENHANCEMENT !== 'false'
            };

            const enhancedSamples = AudioProcessor.audioEnhancer.enhance(samples, enhancementOptions);

            // Convert back to int16 PCM
            const enhancedBuffer = Buffer.alloc(pcmBuffer.length);
            for (let i = 0; i < enhancedSamples.length; i++) {
                const sample = Math.max(-1, Math.min(1, enhancedSamples[i]));
                enhancedBuffer.writeInt16LE(Math.round(sample * 32767), i * 2);
            }

            return enhancedBuffer;
        } catch (error) {
            console.error('❌ Error in advanced audio enhancement:', error);
            return this.enhanceAudioQuality(pcmBuffer); // Fallback to basic enhancement
        }
    }

    /**
     * Basic audio quality improvement with noise reduction and normalization (fallback)
     * @param pcmBuffer - Raw PCM audio buffer
     * @returns Enhanced PCM audio buffer
     */
    enhanceAudioQuality(pcmBuffer) {
        try {
            // Convert to float32 for processing
            const samples = new Float32Array(pcmBuffer.length / 2);
            for (let i = 0; i < samples.length; i++) {
                samples[i] = pcmBuffer.readInt16LE(i * 2) / 32768.0;
            }

            // Apply noise gate (remove very quiet samples that are likely noise)
            const noiseThreshold = 0.01; // Adjust based on testing
            for (let i = 0; i < samples.length; i++) {
                if (Math.abs(samples[i]) < noiseThreshold) {
                    samples[i] *= 0.1; // Reduce noise rather than eliminate completely
                }
            }

            // Apply simple high-pass filter to remove low-frequency noise
            const filteredSamples = this.applyHighPassFilter(samples, 80, 8000);

            // Normalize audio levels
            const normalizedSamples = this.normalizeAudio(filteredSamples);

            // Convert back to int16 PCM
            const enhancedBuffer = Buffer.alloc(pcmBuffer.length);
            for (let i = 0; i < normalizedSamples.length; i++) {
                const sample = Math.max(-1, Math.min(1, normalizedSamples[i]));
                enhancedBuffer.writeInt16LE(Math.round(sample * 32767), i * 2);
            }

            return enhancedBuffer;
        } catch (error) {
            console.error('❌ Error in audio enhancement:', error);
            return pcmBuffer; // Return original if enhancement fails
        }
    }

    /**
     * Apply simple high-pass filter to remove low-frequency noise
     * @param samples - Float32Array of audio samples
     * @param cutoffFreq - Cutoff frequency in Hz
     * @param sampleRate - Sample rate in Hz
     * @returns Filtered audio samples
     */
    applyHighPassFilter(samples, cutoffFreq, sampleRate) {
        const rc = 1.0 / (cutoffFreq * 2 * Math.PI);
        const dt = 1.0 / sampleRate;
        const alpha = rc / (rc + dt);

        const filtered = new Float32Array(samples.length);
        filtered[0] = samples[0];

        for (let i = 1; i < samples.length; i++) {
            filtered[i] = alpha * (filtered[i-1] + samples[i] - samples[i-1]);
        }

        return filtered;
    }

    /**
     * Normalize audio levels to prevent clipping and improve consistency
     * @param samples - Float32Array of audio samples
     * @returns Normalized audio samples
     */
    normalizeAudio(samples) {
        // Find peak amplitude
        let peak = 0;
        for (let i = 0; i < samples.length; i++) {
            peak = Math.max(peak, Math.abs(samples[i]));
        }

        // Avoid division by zero and over-normalization
        if (peak < 0.001) {
            return samples;
        }

        // Apply gentle normalization (don't normalize to full scale to avoid harshness)
        const targetLevel = 0.7;
        const gain = Math.min(targetLevel / peak, 3.0); // Limit gain to prevent over-amplification

        const normalized = new Float32Array(samples.length);
        for (let i = 0; i < samples.length; i++) {
            normalized[i] = samples[i] * gain;
        }

        return normalized;
    }

    /**
     * Converts PCM audio to Float32Array format for Gemini Live API
     * Enhanced with quality improvements
     * @param pcmBuffer - PCM audio buffer
     * @returns Float32Array suitable for Gemini
     */
    pcmToFloat32Array(pcmBuffer) {
        try {
            // Use enhanced PCM buffer if available
            const enhancedBuffer = this.enhanceAudioQuality(pcmBuffer);

            const float32Array = new Float32Array(enhancedBuffer.length / 2);
            for (let i = 0; i < float32Array.length; i++) {
                // Convert int16 to float32 (-1 to 1 range)
                float32Array[i] = enhancedBuffer.readInt16LE(i * 2) / 32768.0;
            }
            return float32Array;
        } catch (error) {
            console.error('❌ Error in PCM to Float32 conversion:', error);
            // Fallback to basic conversion
            const float32Array = new Float32Array(pcmBuffer.length / 2);
            for (let i = 0; i < float32Array.length; i++) {
                float32Array[i] = pcmBuffer.readInt16LE(i * 2) / 32768.0;
            }
            return float32Array;
        }
    }

    /**
     * Enhanced resampling from 8kHz to 16kHz using cubic interpolation
     * @param data - Float32Array audio data at 8kHz
     * @returns Float32Array audio data at 16kHz
     */
    upsample8kTo16k(data) {
        try {
            // Use cubic interpolation for better quality
            return this.resampleCubic(data, 2.0);
        } catch (error) {
            console.error('❌ Error in enhanced upsampling, falling back to linear:', error);
            return this.upsample8kTo16kLinear(data);
        }
    }

    /**
     * Fallback linear interpolation resampling from 8kHz to 16kHz
     * @param data - Float32Array audio data at 8kHz
     * @returns Float32Array audio data at 16kHz
     */
    upsample8kTo16kLinear(data) {
        const outputLength = data.length * 2; // Double the sample rate
        const output = new Float32Array(outputLength);

        for (let i = 0; i < data.length - 1; i++) {
            const current = data[i];
            const next = data[i + 1];

            // Original sample
            output[i * 2] = current;
            // Interpolated sample
            output[i * 2 + 1] = (current + next) / 2;
        }

        // Handle last sample
        if (data.length > 0) {
            output[outputLength - 2] = data[data.length - 1];
            output[outputLength - 1] = data[data.length - 1];
        }

        return output;
    }

    /**
     * Cubic interpolation resampling for better audio quality
     * @param input - Input audio data
     * @param ratio - Resampling ratio (output_rate / input_rate)
     * @returns Resampled audio data
     */
    resampleCubic(input, ratio) {
        const outputLength = Math.floor(input.length * ratio);
        const output = new Float32Array(outputLength);

        for (let i = 0; i < outputLength; i++) {
            const srcIndex = i / ratio;
            const srcIndexInt = Math.floor(srcIndex);
            const fraction = srcIndex - srcIndexInt;

            // Get 4 points for cubic interpolation
            const y0 = input[Math.max(0, srcIndexInt - 1)] || 0;
            const y1 = input[srcIndexInt] || 0;
            const y2 = input[Math.min(input.length - 1, srcIndexInt + 1)] || 0;
            const y3 = input[Math.min(input.length - 1, srcIndexInt + 2)] || 0;

            // Cubic interpolation
            const a = -0.5 * y0 + 1.5 * y1 - 1.5 * y2 + 0.5 * y3;
            const b = y0 - 2.5 * y1 + 2 * y2 - 0.5 * y3;
            const c = -0.5 * y0 + 0.5 * y2;
            const d = y1;

            output[i] = a * fraction * fraction * fraction + b * fraction * fraction + c * fraction + d;
        }

        return output;
    }

    /**
     * Enhanced downsampling from 24kHz to 8kHz with anti-aliasing
     * @param data - Float32Array audio data at 24kHz
     * @returns Float32Array audio data at 8kHz
     */
    downsample24kTo8k(data) {
        try {
            // Apply anti-aliasing filter before downsampling
            const filtered = this.applyAntiAliasingFilter(data, 24000, 4000);
            return this.resampleCubic(filtered, 8000 / 24000);
        } catch (error) {
            console.error('❌ Error in enhanced downsampling, falling back to simple:', error);
            return this.downsample24kTo8kSimple(data);
        }
    }

    /**
     * Simple decimation resampling from 24kHz to 8kHz (fallback)
     * @param data - Float32Array audio data at 24kHz
     * @returns Float32Array audio data at 8kHz
     */
    downsample24kTo8kSimple(data) {
        const outputLength = Math.floor(data.length / 3); // Divide by 3 (24k/8k = 3)
        const output = new Float32Array(outputLength);

        for (let i = 0; i < outputLength; i++) {
            // Take every 3rd sample (simple decimation)
            output[i] = data[i * 3];
        }

        return output;
    }

    /**
     * Apply anti-aliasing low-pass filter before downsampling
     * @param samples - Input audio samples
     * @param sampleRate - Input sample rate
     * @param cutoffFreq - Cutoff frequency for anti-aliasing
     * @returns Filtered audio samples
     */
    applyAntiAliasingFilter(samples, sampleRate, cutoffFreq) {
        const rc = 1.0 / (cutoffFreq * 2 * Math.PI);
        const dt = 1.0 / sampleRate;
        const alpha = dt / (rc + dt);

        const filtered = new Float32Array(samples.length);
        filtered[0] = samples[0];

        for (let i = 1; i < samples.length; i++) {
            filtered[i] = filtered[i-1] + alpha * (samples[i] - filtered[i-1]);
        }

        return filtered;
    }

    /**
     * Creates audio blob for Gemini API (based on template)
     * @param data - Float32Array audio data at 8kHz
     * @returns Audio blob for Gemini at 16kHz
     */
    createGeminiAudioBlob(data) {
        // Upsample from 8kHz to 16kHz for Gemini
        const upsampled = this.upsample8kTo16k(data);

        const int16 = new Int16Array(upsampled.length);
        for (let i = 0; i < upsampled.length; i++) {
            // Convert float32 -1 to 1 to int16 -32768 to 32767
            int16[i] = upsampled[i] * 32768;
        }

        return {
            data: Buffer.from(int16.buffer).toString('base64'),
            mimeType: 'audio/pcm;rate=16000' // Gemini expects 16kHz for input
        };
    }

    /**
     * Enhanced conversion of Gemini PCM audio back to μ-law for Twilio
     * @param base64Audio - Base64 encoded PCM audio from Gemini (24kHz) OR Buffer for testing
     * @returns Base64 encoded μ-law audio for Twilio (8kHz) OR Buffer for testing
     */
    convertPCMToUlaw(base64Audio) {
        try {
            // Handle both base64 string and Buffer input for testing compatibility
            const pcmBuffer = Buffer.isBuffer(base64Audio) ? base64Audio : Buffer.from(base64Audio, 'base64');
            const returnAsBuffer = Buffer.isBuffer(base64Audio);

            // Convert PCM buffer to Float32Array for processing
            const float32Array = new Float32Array(pcmBuffer.length / 2);
            for (let i = 0; i < float32Array.length; i++) {
                float32Array[i] = pcmBuffer.readInt16LE(i * 2) / 32768.0;
            }

            // Apply audio enhancement before downsampling
            const enhanced = this.enhanceGeminiOutput(float32Array);

            // Downsample from 24kHz to 8kHz with quality preservation
            const downsampled = this.downsample24kTo8k(enhanced);

            // Apply final processing and convert to μ-law
            const processed = this.prepareForTwilio(downsampled);
            const ulawBuffer = Buffer.alloc(processed.length);

            for (let i = 0; i < processed.length; i++) {
                // Ensure proper range and convert to μ-law
                const pcmSample = Math.max(-32767, Math.min(32767, Math.round(processed[i] * 32767)));
                ulawBuffer[i] = this.linearToUlaw(pcmSample);
            }

            return returnAsBuffer ? ulawBuffer : ulawBuffer.toString('base64');
        } catch (error) {
            console.error('❌ Error converting PCM to μ-law:', error);
            return this.fallbackPCMToUlaw(base64Audio);
        }
    }

    /**
     * Enhance Gemini output audio quality using advanced processing
     * @param samples - Float32Array of audio samples from Gemini
     * @returns Enhanced audio samples
     */
    enhanceGeminiOutput(samples) {
        try {
            // Use advanced audio enhancer for Gemini output
            const enhancementOptions = {
                noiseReduction: true,
                compression: true,
                agc: true,
                voiceEnhancement: true,
                deEssing: AudioProcessor.audioEnhancer.isDeEssingEnabled() // Use configurable de-essing
            };

            const enhanced = AudioProcessor.audioEnhancer.enhance(samples, enhancementOptions);

            // Apply additional Gemini-specific processing
            const compressed = this.applyCompression(enhanced);

            // Apply de-essing only if enabled (for backward compatibility with existing method)
            const deEssed = AudioProcessor.audioEnhancer.isDeEssingEnabled()
                ? this.applyDeEssing(compressed)
                : compressed;

            // Final normalization
            return this.normalizeAudio(deEssed);
        } catch (error) {
            console.error('❌ Error in advanced Gemini output enhancement:', error);
            // Fallback to basic enhancement
            return this.enhanceGeminiOutputBasic(samples);
        }
    }

    /**
     * Basic Gemini output enhancement (fallback)
     * @param samples - Float32Array of audio samples from Gemini
     * @returns Enhanced audio samples
     */
    enhanceGeminiOutputBasic(samples) {
        try {
            // Apply gentle compression to even out levels
            const compressed = this.applyCompression(samples);

            // Apply de-essing only if enabled
            const deEssed = AudioProcessor.audioEnhancer.isDeEssingEnabled()
                ? this.applyDeEssing(compressed)
                : compressed;

            // Final normalization
            return this.normalizeAudio(deEssed);
        } catch (error) {
            console.error('❌ Error in basic Gemini output enhancement:', error);
            return samples;
        }
    }

    /**
     * Apply gentle compression to even out audio levels
     * @param samples - Input audio samples
     * @returns Compressed audio samples
     */
    applyCompression(samples) {
        const threshold = 0.6;
        const ratio = 3.0;
        const compressed = new Float32Array(samples.length);

        for (let i = 0; i < samples.length; i++) {
            const sample = samples[i];
            const absample = Math.abs(sample);

            if (absample > threshold) {
                const excess = absample - threshold;
                const compressedExcess = excess / ratio;
                const newLevel = threshold + compressedExcess;
                compressed[i] = sample >= 0 ? newLevel : -newLevel;
            } else {
                compressed[i] = sample;
            }
        }

        return compressed;
    }

    /**
     * Apply de-essing to reduce harsh sibilant sounds
     * @param samples - Input audio samples
     * @returns De-essed audio samples
     */
    applyDeEssing(samples) {
        // Simple de-essing using frequency-selective compression
        const deEssed = new Float32Array(samples.length);
        const lookAhead = 5; // samples

        for (let i = 0; i < samples.length; i++) {
            let highFreqEnergy = 0;

            // Calculate high-frequency energy in a small window
            for (let j = Math.max(0, i - lookAhead); j < Math.min(samples.length, i + lookAhead); j++) {
                if (j > 0) {
                    const diff = samples[j] - samples[j - 1];
                    highFreqEnergy += diff * diff;
                }
            }

            // Apply reduction if high-frequency energy is excessive
            const threshold = 0.1;
            if (highFreqEnergy > threshold) {
                const reduction = Math.min(0.7, threshold / highFreqEnergy);
                deEssed[i] = samples[i] * reduction;
            } else {
                deEssed[i] = samples[i];
            }
        }

        return deEssed;
    }

    /**
     * Prepare audio for Twilio transmission
     * @param samples - Input audio samples
     * @returns Processed audio samples optimized for Twilio
     */
    prepareForTwilio(samples) {
        // Apply final EQ to optimize for phone transmission
        const eqed = this.applyPhoneEQ(samples);

        // Apply gentle limiting to prevent clipping
        return this.applyLimiter(eqed);
    }

    /**
     * Apply EQ optimized for phone transmission
     * @param samples - Input audio samples
     * @returns EQ'd audio samples
     */
    applyPhoneEQ(samples) {
        // Simple EQ boost in the 1-3kHz range for better intelligibility
        const eqed = new Float32Array(samples.length);
        const boost = 1.2; // 20% boost

        // This is a simplified EQ - in production, you'd use proper filter design
        for (let i = 1; i < samples.length - 1; i++) {
            // Simple high-mid boost
            const highMid = (samples[i] - (samples[i-1] + samples[i+1]) / 2) * boost;
            eqed[i] = samples[i] + highMid * 0.3;
        }

        // Handle edges
        eqed[0] = samples[0];
        if (samples.length > 1) {eqed[samples.length - 1] = samples[samples.length - 1];}

        return eqed;
    }

    /**
     * Apply gentle limiting to prevent clipping
     * @param samples - Input audio samples
     * @returns Limited audio samples
     */
    applyLimiter(samples) {
        const limit = 0.95;
        const limited = new Float32Array(samples.length);

        for (let i = 0; i < samples.length; i++) {
            const sample = samples[i];
            if (Math.abs(sample) > limit) {
                // Soft limiting
                limited[i] = sample >= 0 ? limit : -limit;
            } else {
                limited[i] = sample;
            }
        }

        return limited;
    }

    /**
     * Fallback PCM to μ-law conversion
     * @param base64Audio - Base64 encoded PCM audio
     * @returns Base64 encoded μ-law audio
     */
    fallbackPCMToUlaw(base64Audio) {
        try {
            // Handle both base64 string and Buffer input for testing compatibility
            const pcmBuffer = Buffer.isBuffer(base64Audio) ? base64Audio : Buffer.from(base64Audio, 'base64');
            const returnAsBuffer = Buffer.isBuffer(base64Audio);
            const float32Array = new Float32Array(pcmBuffer.length / 2);

            for (let i = 0; i < float32Array.length; i++) {
                float32Array[i] = pcmBuffer.readInt16LE(i * 2) / 32768.0;
            }

            const downsampled = this.downsample24kTo8kSimple(float32Array);
            const ulawBuffer = Buffer.alloc(downsampled.length);

            for (let i = 0; i < downsampled.length; i++) {
                const pcmSample = Math.round(downsampled[i] * 32767);
                ulawBuffer[i] = this.linearToUlaw(pcmSample);
            }

            return returnAsBuffer ? ulawBuffer : ulawBuffer.toString('base64');
        } catch (error) {
            console.error('❌ Error in fallback PCM to μ-law conversion:', error);
            return base64Audio;
        }
    }

    /**
     * Converts linear PCM sample to μ-law
     * @param pcm - Linear PCM sample
     * @returns μ-law encoded sample
     */
    linearToUlaw(pcm) {
        const BIAS = 0x84;
        const CLIP = 32635;

        const sign = (pcm >> 8) & 0x80;
        if (sign !== 0) {pcm = -pcm;}
        if (pcm > CLIP) {pcm = CLIP;}

        pcm += BIAS;
        let exponent = 7;
        let expMask = 0x4000;

        for (let i = 0; i < 8; i++) {
            if ((pcm & expMask) !== 0) {break;}
            exponent--;
            expMask >>= 1;
        }

        const mantissa = (pcm >> (exponent + 3)) & 0x0F;
        const ulaw = ~(sign | (exponent << 4) | mantissa);

        return ulaw & 0xFF;
    }

    /**
     * Audio Quality Monitor - tracks and logs audio quality metrics
     */
    static audioQualityMonitor = {
        metrics: {
            totalSamples: 0,
            clippedSamples: 0,
            silentSamples: 0,
            peakLevel: 0,
            averageLevel: 0,
            dynamicRange: 0,
            lastUpdate: Date.now()
        },

        /**
         * Analyze audio quality metrics
         * @param samples - Float32Array of audio samples
         * @param label - Label for logging (e.g., 'input', 'output')
         */
        analyze(samples, label = 'unknown') {
            try {
                const metrics = this.calculateMetrics(samples);
                this.updateGlobalMetrics(metrics);

                if (process.env.AUDIO_DEBUG === 'true') {
                    console.log(`🎵 [${label}] Audio Quality Metrics:`, {
                        samples: samples.length,
                        peak: metrics.peak.toFixed(3),
                        rms: metrics.rms.toFixed(3),
                        clipping: metrics.clippingPercentage.toFixed(1) + '%',
                        silence: metrics.silencePercentage.toFixed(1) + '%',
                        dynamicRange: metrics.dynamicRange.toFixed(1) + 'dB'
                    });
                }

                // Warn about quality issues
                if (metrics.clippingPercentage > 5) {
                    console.warn(`⚠️ [${label}] High clipping detected: ${metrics.clippingPercentage.toFixed(1)}%`);
                }
                if (metrics.silencePercentage > 80) {
                    console.warn(`⚠️ [${label}] Mostly silent audio: ${metrics.silencePercentage.toFixed(1)}%`);
                }
                if (metrics.dynamicRange < 20) {
                    console.warn(`⚠️ [${label}] Low dynamic range: ${metrics.dynamicRange.toFixed(1)}dB`);
                }

                return metrics;
            } catch (error) {
                console.error(`❌ Error analyzing audio quality for ${label}:`, error);
                return null;
            }
        },

        /**
         * Calculate detailed audio metrics
         * @param samples - Float32Array of audio samples
         * @returns Object with audio quality metrics
         */
        calculateMetrics(samples) {
            let peak = 0;
            let rmsSum = 0;
            let clippedCount = 0;
            let silentCount = 0;
            let min = Infinity;
            let max = -Infinity;

            const silenceThreshold = 0.001;
            const clippingThreshold = 0.95;

            for (let i = 0; i < samples.length; i++) {
                const sample = samples[i];
                const absSample = Math.abs(sample);

                // Peak detection
                if (absSample > peak) {peak = absSample;}

                // RMS calculation
                rmsSum += sample * sample;

                // Clipping detection
                if (absSample > clippingThreshold) {clippedCount++;}

                // Silence detection
                if (absSample < silenceThreshold) {silentCount++;}

                // Min/max for dynamic range
                if (sample < min) {min = sample;}
                if (sample > max) {max = sample;}
            }

            const rms = Math.sqrt(rmsSum / samples.length);
            const dynamicRange = 20 * Math.log10((max - min) / 2);

            return {
                peak,
                rms,
                clippingPercentage: (clippedCount / samples.length) * 100,
                silencePercentage: (silentCount / samples.length) * 100,
                dynamicRange: isFinite(dynamicRange) ? dynamicRange : 0,
                sampleCount: samples.length
            };
        },

        /**
         * Update global metrics tracking
         * @param metrics - Current metrics object
         */
        updateGlobalMetrics(metrics) {
            this.metrics.totalSamples += metrics.sampleCount;
            this.metrics.clippedSamples += (metrics.clippingPercentage / 100) * metrics.sampleCount;
            this.metrics.silentSamples += (metrics.silencePercentage / 100) * metrics.sampleCount;

            if (metrics.peak > this.metrics.peakLevel) {
                this.metrics.peakLevel = metrics.peak;
            }

            // Running average of RMS level
            const alpha = 0.1; // Smoothing factor
            this.metrics.averageLevel = this.metrics.averageLevel * (1 - alpha) + metrics.rms * alpha;

            this.metrics.dynamicRange = metrics.dynamicRange;
            this.metrics.lastUpdate = Date.now();
        },

        /**
         * Get current quality summary
         * @returns Object with quality summary
         */
        getSummary() {
            const totalClippingPercentage = this.metrics.totalSamples > 0 ?
                (this.metrics.clippedSamples / this.metrics.totalSamples) * 100 : 0;
            const totalSilencePercentage = this.metrics.totalSamples > 0 ?
                (this.metrics.silentSamples / this.metrics.totalSamples) * 100 : 0;

            return {
                totalSamples: this.metrics.totalSamples,
                peakLevel: this.metrics.peakLevel.toFixed(3),
                averageLevel: this.metrics.averageLevel.toFixed(3),
                clippingPercentage: totalClippingPercentage.toFixed(2) + '%',
                silencePercentage: totalSilencePercentage.toFixed(2) + '%',
                dynamicRange: this.metrics.dynamicRange.toFixed(1) + 'dB',
                lastUpdate: new Date(this.metrics.lastUpdate).toISOString()
            };
        },

        /**
         * Reset metrics
         */
        reset() {
            this.metrics = {
                totalSamples: 0,
                clippedSamples: 0,
                silentSamples: 0,
                peakLevel: 0,
                averageLevel: 0,
                dynamicRange: 0,
                lastUpdate: Date.now()
            };
        }
    };

    /**
     * Debug helper to save audio samples to file for analysis
     * @param samples - Float32Array of audio samples
     * @param filename - Filename to save (without extension)
     * @param sampleRate - Sample rate of the audio
     */
    static async saveAudioDebug(samples, filename, sampleRate = 8000) {
        if (process.env.AUDIO_DEBUG !== 'true') {return;}

        try {
            const { default: fs } = await import('fs');
            const { default: path } = await import('path');

            // Convert to 16-bit PCM
            const pcmBuffer = Buffer.alloc(samples.length * 2);
            for (let i = 0; i < samples.length; i++) {
                const sample = Math.max(-1, Math.min(1, samples[i]));
                pcmBuffer.writeInt16LE(Math.round(sample * 32767), i * 2);
            }

            // Create debug directory if it doesn't exist
            const debugDir = path.join(process.cwd(), 'audio-debug');
            if (!fs.existsSync(debugDir)) {
                fs.mkdirSync(debugDir, { recursive: true });
            }

            // Save raw PCM file
            const filepath = path.join(debugDir, `${filename}_${Date.now()}.pcm`);
            fs.writeFileSync(filepath, pcmBuffer);

            console.log(`🎵 Debug: Saved audio to ${filepath} (${samples.length} samples, ${sampleRate}Hz)`);
        } catch (error) {
            console.error('❌ Error saving audio debug file:', error);
        }
    }

    // Update audio settings
    updateAudioSettings(newSettings) {
        this.audioSettings = { ...this.audioSettings, ...newSettings };
        console.log('🎛️ Audio settings updated:', this.audioSettings);
    }

    // Get current audio settings
    getAudioSettings() {
        return { ...this.audioSettings };
    }

    /**
     * Simple PCM to μ-law conversion for testing (no resampling)
     * @param pcmBuffer - PCM16 audio buffer at 8kHz
     * @returns μ-law encoded buffer
     */
    pcmToUlaw(pcmBuffer) {
        if (!Buffer.isBuffer(pcmBuffer)) {
            throw new Error('Input must be a Buffer');
        }

        const ulawBuffer = Buffer.alloc(Math.floor(pcmBuffer.length / 2));
        
        for (let i = 0; i < ulawBuffer.length; i++) {
            const pcmSample = pcmBuffer.readInt16LE(i * 2);
            ulawBuffer[i] = this.linearToUlaw(pcmSample);
        }

        return ulawBuffer;
    }
    /**
     * Convert WebM audio to PCM16 format for Gemini Live API
     * This function was missing and causing the "wrong Hz" issue in outbound testing
     * @param {Buffer} webmData - WebM audio data from browser
     * @param {number} targetSampleRate - Target sample rate (default: 16000)
     * @returns {Buffer} PCM16 audio buffer
     */
    static convertWebmToPCM16(webmData, targetSampleRate = 16000) {
        try {
            console.log('🎵 Converting WebM to PCM16:', {
                inputSize: webmData.length,
                targetSampleRate: targetSampleRate
            });

            // Use Web Audio API to decode WebM data
            const audioBuffer = this.decodeAudioData(webmData);
            
            console.log('🎵 Decoded WebM audio:', {
                sampleRate: audioBuffer.sampleRate,
                numberOfChannels: audioBuffer.numberOfChannels,
                length: audioBuffer.length,
                duration: audioBuffer.duration
            });

            // Convert to PCM16 at target sample rate
            const pcm16Buffer = this.convertToPCM16(audioBuffer, targetSampleRate);
            
            console.log('🎵 PCM16 conversion result:', {
                outputSize: pcm16Buffer.length,
                targetSampleRate: targetSampleRate
            });

            return pcm16Buffer;
        } catch (error) {
            console.error('❌ Error converting WebM to PCM16:', error);
            throw error;
        }
    }

    /**
     * Decode WebM audio data using Web Audio API
     * @param {Buffer} webmData - WebM audio data
     * @returns {AudioBuffer} Decoded audio buffer
     */
    static decodeAudioData(webmData) {
        // WebM container typically contains Opus-encoded audio
        // For browser audio, we expect PCM data, not WebM
        // The browser should send raw PCM audio data, not WebM-encoded data
        
        // Check if this is actually PCM data mislabeled as WebM
        // Browser MediaRecorder with mimeType 'audio/webm' still sends PCM chunks
        if (webmData.length > 0 && webmData.length % 2 === 0) {
            // Treat as raw PCM16 data at 48kHz (browser default)
            const mockAudioBuffer = {
                sampleRate: 48000, // Browser recording default
                numberOfChannels: 1,
                length: webmData.length / 2, // 16-bit samples
                duration: 0,
                getChannelData: function(_channel) {
                    // Convert PCM16 buffer to Float32Array
                    const samples = new Float32Array(this.length);
                    for (let i = 0; i < this.length; i++) {
                        // Read PCM16 sample and convert to float [-1, 1]
                        const sampleIndex = i * 2;
                        if (sampleIndex < webmData.length - 1) {
                            const sample = webmData.readInt16LE(sampleIndex) / 32768.0;
                            samples[i] = sample;
                        }
                    }
                    return samples;
                }
            };
            
            mockAudioBuffer.duration = mockAudioBuffer.length / mockAudioBuffer.sampleRate;
            return mockAudioBuffer;
        }
        
        // If not PCM, we need proper WebM decoding
        throw new Error('WebM container decoding not implemented. Expected PCM audio data.');
    }

    /**
     * Convert AudioBuffer to PCM16 at target sample rate
     * @param {AudioBuffer} audioBuffer - Source audio buffer
     * @param {number} targetSampleRate - Target sample rate
     * @returns {Buffer} PCM16 buffer
     */
    static convertToPCM16(audioBuffer, targetSampleRate) {
        try {
            // Get channel data (use first channel for mono)
            const sourceData = audioBuffer.getChannelData(0);
            const sourceSampleRate = audioBuffer.sampleRate;
            
            console.log('🎵 Converting to PCM16:', {
                sourceSampleRate: sourceSampleRate,
                targetSampleRate: targetSampleRate,
                sourceLength: sourceData.length
            });

            // Resample if needed
            let resampledData;
            if (sourceSampleRate !== targetSampleRate) {
                const resampleRatio = targetSampleRate / sourceSampleRate;
                console.log('🎵 Resampling with ratio:', resampleRatio);
                
                // Use the existing resampling function
                const processor = new AudioProcessor();
                resampledData = processor.resampleCubic(sourceData, resampleRatio);
            } else {
                resampledData = sourceData;
            }

            console.log('🎵 Resampled data length:', resampledData.length);

            // Convert to Int16 PCM
            const pcm16Buffer = Buffer.alloc(resampledData.length * 2);
            for (let i = 0; i < resampledData.length; i++) {
                // Clamp and convert float32 to int16
                const sample = Math.max(-1, Math.min(1, resampledData[i]));
                const int16Sample = Math.round(sample * 32767);
                pcm16Buffer.writeInt16LE(int16Sample, i * 2);
            }

            return pcm16Buffer;
        } catch (error) {
            console.error('❌ Error converting to PCM16:', error);
            throw error;
        }
    }
}

// Export the convertWebmToPCM16 function globally for use in WebSocket handlers
export function convertWebmToPCM16(webmData, targetSampleRate = 16000) {
    return AudioProcessor.convertWebmToPCM16(webmData, targetSampleRate);
}
