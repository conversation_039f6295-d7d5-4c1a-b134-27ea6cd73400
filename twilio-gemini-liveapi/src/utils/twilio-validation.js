import crypto from 'crypto';
import { config, getConfigValue } from '../config/config.js';

/**
 * Validate Twilio webhook signatures to ensure requests are from Twilio
 */
export class TwilioWebhookValidator {
    constructor() {
        this.authToken = getConfigValue('twilio.authToken');
        this.skipValidation = getConfigValue('twilio.skipWebhookValidation', false);
    }

    /**
     * Validate a Twilio webhook request
     * @param {string} signature - The X-Twilio-Signature header value
     * @param {string} url - The full URL of the webhook endpoint
     * @param {object} params - The request body parameters
     * @returns {boolean} - Whether the signature is valid
     */
    validateRequest(signature, url, params) {
        // Skip validation in development if configured
        if (this.skipValidation || process.env.NODE_ENV === 'development') {
            console.warn('⚠️ Twilio webhook signature validation is disabled');
            return true;
        }

        if (!signature || !url || !this.authToken) {
            console.error('❌ Missing required parameters for Twilio signature validation');
            return false;
        }

        try {
            // Sort the POST parameters alphabetically by key
            const sortedParams = Object.keys(params || {})
                .sort()
                .reduce((acc, key) => {
                    acc[key] = params[key];
                    return acc;
                }, {});

            // Build the validation string
            let validationString = url;
            for (const [key, value] of Object.entries(sortedParams)) {
                validationString += key + (value || '');
            }

            // Calculate the expected signature
            const expectedSignature = crypto
                .createHmac('sha1', this.authToken)
                .update(validationString)
                .digest('base64');

            // Compare signatures
            const isValid = signature === expectedSignature;
            
            if (!isValid) {
                console.error('❌ Twilio webhook signature validation failed');
                console.error('Expected:', expectedSignature);
                console.error('Received:', signature);
            }

            return isValid;
        } catch (error) {
            console.error('❌ Error validating Twilio webhook signature:', error);
            return false;
        }
    }

    /**
     * Express/Fastify middleware for validating Twilio webhooks
     */
    middleware() {
        return async (request, reply) => {
            // Skip validation for non-Twilio endpoints
            const twilioEndpoints = ['/incoming-call', '/call-status', '/voice-status'];
            if (!twilioEndpoints.includes(request.url)) {
                return;
            }

            const signature = request.headers['x-twilio-signature'];
            const fullUrl = `${request.protocol}://${request.headers.host}${request.url}`;
            
            if (!this.validateRequest(signature, fullUrl, request.body)) {
                reply.code(403).send({ error: 'Invalid Twilio signature' });
                return;
            }
        };
    }
}

// Export singleton instance
export const twilioValidator = new TwilioWebhookValidator();

/**
 * Validate a Twilio webhook request
 * @param {object} request - The HTTP request object
 * @returns {boolean} - Whether the request is valid
 */
export function validateTwilioWebhook(request) {
    const signature = request.headers['x-twilio-signature'];
    const fullUrl = `${request.protocol}://${request.headers.host}${request.url}`;
    
    return twilioValidator.validateRequest(signature, fullUrl, request.body);
}