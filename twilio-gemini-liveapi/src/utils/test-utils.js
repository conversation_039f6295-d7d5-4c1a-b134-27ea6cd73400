/**
 * Test Utilities for Twilio Gemini Live API
 * 
 * Provides comprehensive testing infrastructure for:
 * - All 4 flows: outbound/inbound × twilio/browser
 * - LLM integration testing with mocks
 * - Audio processing validation
 * - Session management testing
 * - Error scenario simulation
 */

import { EventEmitter } from 'events';
import { performance } from 'perf_hooks';
import { logger } from './logger.js';

/**
 * Mock Gemini API responses for deterministic testing
 */
export class MockGeminiClient {
    constructor(options = {}) {
        this.responses = options.responses || [];
        this.responseIndex = 0;
        this.connected = false;
        this.eventEmitter = new EventEmitter();
        this.sessionId = `mock-session-${Date.now()}`;
    }

    async connect() {
        this.connected = true;
        this.eventEmitter.emit('connected');
        return this.sessionId;
    }

    async disconnect() {
        this.connected = false;
        this.eventEmitter.emit('disconnected');
    }

    sendRealtimeInput(input) {
        if (!this.connected) {
            throw new Error('Not connected to Gemini');
        }

        // Simulate processing delay
        setTimeout(() => {
            const response = this.responses[this.responseIndex % this.responses.length];
            this.responseIndex++;
            
            this.eventEmitter.emit('response', {
                type: 'audio',
                data: response.audioData || new ArrayBuffer(1024),
                mimeType: 'audio/pcm16'
            });
        }, 100);
    }

    on(event, callback) {
        this.eventEmitter.on(event, callback);
    }

    off(event, callback) {
        this.eventEmitter.off(event, callback);
    }
}

/**
 * Mock Twilio webhooks and API calls
 */
export class MockTwilioClient {
    constructor(options = {}) {
        this.calls = new Map();
        this.webhookCallbacks = new Map();
        this.simulateErrors = options.simulateErrors || false;
    }

    async makeCall(to, from, url) {
        if (this.simulateErrors) {
            throw new Error('Twilio API error');
        }

        const callSid = `CA${Math.random().toString(36).substr(2, 32)}`;
        const call = {
            sid: callSid,
            to,
            from,
            url,
            status: 'initiated',
            startTime: new Date(),
            duration: null
        };

        this.calls.set(callSid, call);

        // Simulate call progression
        setTimeout(() => {
            call.status = 'ringing';
            this.triggerWebhook(callSid, 'ringing');
        }, 100);

        setTimeout(() => {
            call.status = 'in-progress';
            this.triggerWebhook(callSid, 'answered');
        }, 500);

        return call;
    }

    async endCall(callSid) {
        const call = this.calls.get(callSid);
        if (call) {
            call.status = 'completed';
            call.endTime = new Date();
            call.duration = Math.round((call.endTime - call.startTime) / 1000);
            this.triggerWebhook(callSid, 'completed');
        }
    }

    triggerWebhook(callSid, status) {
        const callback = this.webhookCallbacks.get(callSid);
        if (callback) {
            callback({
                CallSid: callSid,
                CallStatus: status,
                From: this.calls.get(callSid)?.from,
                To: this.calls.get(callSid)?.to
            });
        }
    }

    onWebhook(callSid, callback) {
        this.webhookCallbacks.set(callSid, callback);
    }

    getCall(callSid) {
        return this.calls.get(callSid);
    }
}

/**
 * Audio test data generator
 */
export class AudioTestData {
    static generatePCM16(durationMs = 1000, frequency = 440) {
        const sampleRate = 16000;
        const samples = Math.floor(sampleRate * durationMs / 1000);
        const buffer = new ArrayBuffer(samples * 2);
        const view = new Int16Array(buffer);

        for (let i = 0; i < samples; i++) {
            const t = i / sampleRate;
            const amplitude = Math.sin(2 * Math.PI * frequency * t) * 0.5;
            view[i] = Math.floor(amplitude * 32767);
        }

        return buffer;
    }

    static generateMuLaw(durationMs = 1000) {
        const sampleRate = 8000;
        const samples = Math.floor(sampleRate * durationMs / 1000);
        const buffer = new ArrayBuffer(samples);
        const view = new Uint8Array(buffer);

        // Generate mu-law encoded silence with some variation
        for (let i = 0; i < samples; i++) {
            view[i] = 0xFF; // mu-law silence
        }

        return buffer;
    }

    static generateBase64Audio(format = 'pcm16', durationMs = 1000) {
        let buffer;
        if (format === 'pcm16') {
            buffer = this.generatePCM16(durationMs);
        } else if (format === 'mulaw') {
            buffer = this.generateMuLaw(durationMs);
        } else {
            throw new Error(`Unsupported format: ${format}`);
        }

        return Buffer.from(buffer).toString('base64');
    }
}

/**
 * WebSocket test utilities
 */
export class MockWebSocket extends EventEmitter {
    constructor(url) {
        super();
        this.url = url;
        this.readyState = 0; // CONNECTING
        this.messages = [];
        
        // Simulate connection
        setTimeout(() => {
            this.readyState = 1; // OPEN
            this.emit('open');
        }, 10);
    }

    send(data) {
        if (this.readyState !== 1) {
            throw new Error('WebSocket is not open');
        }

        let parsedData;
        try {
            parsedData = JSON.parse(data);
        } catch (e) {
            parsedData = data;
        }

        this.messages.push({
            timestamp: Date.now(),
            data: parsedData
        });

        this.emit('message', { data });
    }

    close() {
        this.readyState = 3; // CLOSED
        this.emit('close');
    }

    getMessages() {
        return this.messages;
    }

    getLastMessage() {
        return this.messages[this.messages.length - 1];
    }
}

/**
 * Test session factory
 */
export class TestSessionFactory {
    static createTwilioSession(callSid = null) {
        return {
            id: callSid || `CA${Math.random().toString(36).substr(2, 32)}`,
            type: 'twilio',
            status: 'active',
            startTime: new Date(),
            callSid: callSid || `CA${Math.random().toString(36).substr(2, 32)}`,
            from: '+1234567890',
            to: '+1987654321',
            direction: 'outbound',
            campaignId: 1,
            geminiSession: null,
            websocket: null,
            audioMetrics: {
                packetsReceived: 0,
                packetsProcessed: 0,
                audioQuality: 'good'
            }
        };
    }

    static createBrowserSession(sessionId = null) {
        return {
            id: sessionId || `browser-${Math.random().toString(36).substr(2, 16)}`,
            type: 'browser',
            status: 'active',
            startTime: new Date(),
            direction: 'outbound',
            campaignId: 1,
            geminiSession: null,
            websocket: null,
            audioMetrics: {
                samplesReceived: 0,
                samplesProcessed: 0,
                audioQuality: 'excellent'
            }
        };
    }

    static createInboundSession(type = 'twilio') {
        const session = type === 'twilio' 
            ? this.createTwilioSession()
            : this.createBrowserSession();
        
        session.direction = 'inbound';
        session.campaignId = 7; // Inbound campaigns start at 7
        return session;
    }
}

/**
 * Performance measurement utilities
 */
export class TestPerformanceMonitor {
    constructor() {
        this.measurements = new Map();
    }

    start(operation) {
        this.measurements.set(operation, {
            startTime: performance.now(),
            endTime: null,
            duration: null
        });
    }

    end(operation) {
        const measurement = this.measurements.get(operation);
        if (measurement) {
            measurement.endTime = performance.now();
            measurement.duration = measurement.endTime - measurement.startTime;
        }
        return measurement?.duration || null;
    }

    getDuration(operation) {
        return this.measurements.get(operation)?.duration || null;
    }

    getAllMeasurements() {
        return Object.fromEntries(this.measurements);
    }

    reset() {
        this.measurements.clear();
    }
}

/**
 * Error simulation utilities
 */
export class ErrorSimulator {
    static networkError() {
        const error = new Error('Network error');
        error.code = 'ECONNRESET';
        return error;
    }

    static geminiApiError() {
        const error = new Error('Gemini API rate limit exceeded');
        error.status = 429;
        return error;
    }

    static twilioWebhookError() {
        const error = new Error('Twilio webhook validation failed');
        error.status = 403;
        return error;
    }

    static audioCorruptionError() {
        const error = new Error('Audio data corrupted');
        error.code = 'AUDIO_CORRUPT';
        return error;
    }

    static sessionTimeoutError() {
        const error = new Error('Session timeout');
        error.code = 'SESSION_TIMEOUT';
        return error;
    }
}

/**
 * Test data factories
 */
export class TestDataFactory {
    static createCampaignScript(id = 1, type = 'outbound') {
        return {
            id,
            type,
            campaign: `Test Campaign ${id}`,
            agentPersona: 'Professional AI assistant',
            script: `This is a test campaign script for ${type} calls.`,
            transferData: {
                transferNumber: '+1555000' + String(id).padStart(4, '0'),
                agentName: `Agent ${id}`
            }
        };
    }

    static createCallResult(callSid, status = 'completed') {
        return {
            callSid,
            status,
            startTime: new Date(Date.now() - 60000),
            endTime: status === 'completed' ? new Date() : null,
            duration: status === 'completed' ? 60 : null,
            transcript: 'Test conversation transcript',
            outcome: status === 'completed' ? 'successful' : 'in_progress',
            campaignId: 1
        };
    }

    static createAudioQualityMetrics() {
        return {
            sampleRate: 16000,
            bitDepth: 16,
            channels: 1,
            latency: Math.random() * 100 + 50, // 50-150ms
            jitter: Math.random() * 10, // 0-10ms
            packetLoss: Math.random() * 0.01, // 0-1%
            snr: Math.random() * 20 + 30 // 30-50dB
        };
    }
}

/**
 * Test assertion helpers
 */
export class TestAssertions {
    static assertSessionActive(session) {
        if (!session || session.status !== 'active') {
            throw new Error(`Session is not active: ${session?.status || 'null'}`);
        }
    }

    static assertAudioDataValid(audioData, format = 'pcm16') {
        if (!audioData || audioData.byteLength === 0) {
            throw new Error('Audio data is empty or null');
        }

        if (format === 'pcm16' && audioData.byteLength % 2 !== 0) {
            throw new Error('PCM16 audio data must have even byte length');
        }
    }

    static assertWebSocketMessage(message, expectedType) {
        if (!message || !message.data) {
            throw new Error('WebSocket message is empty');
        }

        let parsed;
        try {
            parsed = JSON.parse(message.data);
        } catch (e) {
            throw new Error('WebSocket message is not valid JSON');
        }

        if (parsed.event !== expectedType) {
            throw new Error(`Expected ${expectedType}, got ${parsed.event}`);
        }

        return parsed;
    }

    static assertPerformanceWithinBounds(duration, maxMs) {
        if (duration > maxMs) {
            throw new Error(`Performance too slow: ${duration}ms > ${maxMs}ms`);
        }
    }

    static assertCallSidFormat(callSid) {
        if (!callSid || !callSid.match(/^CA[a-f0-9]{32}$/)) {
            throw new Error(`Invalid call SID format: ${callSid}`);
        }
    }
}

/**
 * Integration test helpers
 */
export class IntegrationTestHelpers {
    static async waitForCondition(condition, timeoutMs = 5000, intervalMs = 100) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeoutMs) {
            if (await condition()) {
                return true;
            }
            await new Promise(resolve => setTimeout(resolve, intervalMs));
        }
        
        throw new Error(`Condition not met within ${timeoutMs}ms`);
    }

    static async simulateCall(mockTwilio, mockGemini, campaignId = 1) {
        const callResult = await mockTwilio.makeCall(
            '+1987654321',
            '+1234567890',
            'http://localhost:3101/media-stream'
        );

        // Wait for call to be answered
        await this.waitForCondition(() => 
            mockTwilio.getCall(callResult.sid)?.status === 'in-progress'
        );

        // Simulate some conversation
        await mockGemini.connect();
        mockGemini.sendRealtimeInput({
            media: {
                data: AudioTestData.generateBase64Audio('pcm16', 2000),
                mimeType: 'audio/pcm16'
            }
        });

        return callResult;
    }

    static createTestServer(port = 0) {
        // This would create a test instance of the Fastify server
        // Implementation would depend on the actual server setup
        return {
            start: async () => ({ port }),
            stop: async () => {},
            getUrl: () => `http://localhost:${port}`
        };
    }
}
