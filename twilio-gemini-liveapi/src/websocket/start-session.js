import { Modality } from '../gemini/client.js';
import { endSession } from './session-utils.js';
import { globalHeartbeatManager } from './heartbeat-manager.js';

export async function handleStartSession(
    sessionId,
    data,
    deps,
    connection,
    ws,
    flowType,
    isIncomingCall,
    getSessionConfig,
    activeConnections,
    healthMonitor,
    lifecycleManager,
    sessionManager
) {
    try {
        let sessionConfig = getSessionConfig();

        if (data.aiInstructions) sessionConfig.aiInstructions = data.aiInstructions;
        if (data.voice) sessionConfig.voice = deps.voiceManager.getValidGeminiVoice(data.voice);
        if (data.model) sessionConfig.model = deps.modelManager.getValidGeminiModel(data.model);
        if (data.scriptId) {
            const testConfig = deps.scriptManager.getScriptConfig(data.scriptId, isIncomingCall);
            if (testConfig) {
                sessionConfig = {
                    ...testConfig,
                    aiInstructions: `[TESTING MODE] ${testConfig.aiInstructions}`,
                    isTestMode: true
                };
            }
        }

        const connectionData = createConnectionData(sessionId, connection, sessionConfig, flowType, isIncomingCall);
        activeConnections.set(sessionId, connectionData);

        healthMonitor.trackConnection(sessionId, 'connected', {
            flowType,
            isTestMode: true,
            scriptId: sessionConfig.scriptId
        });

        const { geminiSession, isSessionActive } = await createGeminiSession(
            sessionId,
            sessionConfig,
            deps,
            connectionData,
            ws,
            flowType
        );

        connectionData.geminiSession = geminiSession;

        if (geminiSession) {
            await initTranscription(sessionId, deps, connectionData);
            if (ws.readyState === 1) {
                ws.send(
                    JSON.stringify({
                        type: 'session-started',
                        sessionId,
                        flowType,
                        scriptId: sessionConfig.scriptId,
                        config: {
                            voice: sessionConfig.voice,
                            model: sessionConfig.model,
                            isIncomingCall,
                            transcriptionEnabled: !!connectionData.deepgramConnection
                        }
                    })
                );
            }
        }

        return { geminiSession, isSessionActive };
    } catch (error) {
        if (ws.readyState === 1) {
            ws.send(
                JSON.stringify({ type: 'session-error', error: `Session start failed: ${error.message}` })
            );
        }
        return { geminiSession: null, isSessionActive: false };
    }
}

function createConnectionData(sessionId, connection, sessionConfig, flowType, isIncomingCall) {
    return {
        localWs: connection.socket || connection,
        sessionId,
        isSessionActive: false,
        summaryRequested: false,
        summaryReceived: false,
        summaryText: '',
        conversationLog: [],
        fullTranscript: [],
        speechTranscript: [],
        isIncomingCall,
        sessionType: 'local_test',
        flowType,
        sessionStartTime: Date.now(),
        lastActivity: Date.now(),
        targetName: sessionConfig.targetName || 'Test Contact',
        targetPhoneNumber: sessionConfig.targetPhoneNumber || '+1234567890',
        originalAIInstructions: sessionConfig.aiInstructions,
        scriptId: sessionConfig.scriptId,
        isTestMode: true,
        lastAIResponse: Date.now(),
        responseTimeouts: 0,
        connectionQuality: 'good',
        lastContextSave: Date.now(),
        contextSaveInterval: null
    };
}

async function createGeminiSession(sessionId, sessionConfig, deps, connectionData, ws, flowType) {
    const correctModel = sessionConfig.model || deps.GEMINI_DEFAULT_MODEL;
    const correctVoice = sessionConfig.voice || deps.GEMINI_DEFAULT_VOICE;

    let geminiSession = null;
    let isSessionActive = false;

    try {
        geminiSession = await deps.sessionManager.geminiClient.live.connect({
            model: correctModel,
            callbacks: {
                onopen: () => {
                    isSessionActive = true;
                },
                onmessage: (message) => handleGeminiMessage(sessionId, message, deps, connectionData),
                onerror: () => {
                    isSessionActive = false;
                },
                onclose: () => {
                    isSessionActive = false;
                }
            },
            config: {
                responseModalities: [Modality.AUDIO],
                speechConfig: {
                    voiceConfig: {
                        prebuiltVoiceConfig: {
                            voiceName: correctVoice
                        }
                    }
                }
            },
            temperature: 1.1,
            topP: 0.95,
            topK: 40,
            maxOutputTokens: 8192
        });

        globalHeartbeatManager.startHeartbeat(sessionId, ws, 30000, 10000, () => {
            endSession(sessionId, { activeConnections: deps.activeConnections, lifecycleManager: deps.lifecycleManager }, 'heartbeat_timeout');
        });

        if (sessionConfig.aiInstructions && geminiSession) {
            await geminiSession.sendClientContent({
                turns: [{ role: 'user', parts: [{ text: sessionConfig.aiInstructions }] }],
                turnComplete: true
            });

            if (flowType === 'outbound_test') {
                setTimeout(async () => {
                    try {
                        await geminiSession.sendClientContent({
                            turns: [{ role: 'user', parts: [{ text: 'Hello, call answered. Start speaking now.' }] }],
                            turnComplete: true
                        });
                    } catch {}
                }, 1000);
            }
        }
    } catch {
        geminiSession = null;
    }

    return { geminiSession, isSessionActive };
}

function handleGeminiMessage(sessionId, message, deps, connectionData) {
    console.log(`🔍 [${sessionId}] handleGeminiMessage called - message keys:`, Object.keys(message || {}));

    if (message.setupComplete || message.goAway) {
        console.log(`🔍 [${sessionId}] Skipping setupComplete or goAway message`);
        return;
    }

    const audio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;
    const text = message.serverContent?.modelTurn?.parts?.[0]?.text;

    console.log(`🔍 [${sessionId}] Audio check: audio exists: ${!!audio}, data length: ${audio?.data?.length || 0}`);

    // ENHANCED AUDIO HANDLING - Match working local testing handler
    if (audio && audio.data && audio.data.length > 0) {
        // Get fresh connection data to ensure we have the latest WebSocket state
        const freshConnectionData = deps.activeConnections?.get(sessionId) || connectionData;
        const localWs = freshConnectionData?.localWs;

        console.log(`🔍 [${sessionId}] Audio forwarding check: audio=${!!audio}, data=${audio.data?.length || 0}, localWs=${!!localWs}, readyState=${localWs?.readyState}`);

        if (localWs && localWs.readyState === 1) { // WebSocket.OPEN
            console.log(`🔊 [${sessionId}] Sending audio response to client, size: ${audio.data?.length || 0}, mimeType: ${audio.mimeType}`);

            try {
                // Send audio back to client with metadata for proper playback
                localWs.send(JSON.stringify({
                    type: 'audio',
                    audio: audio.data,
                    mimeType: audio.mimeType // Include mime type for sample rate info
                }));
                console.log(`✅ [${sessionId}] Audio sent successfully to WebSocket`);
            } catch (sendError) {
                console.error(`❌ [${sessionId}] Error sending audio to WebSocket:`, sendError);
                console.error(`🔍 [${sessionId}] WebSocket state after error: ${localWs.readyState}`);
            }
        } else {
            console.warn(`⚠️ [${sessionId}] Cannot send audio: localWs=${!!localWs}, readyState=${localWs?.readyState}`);
        }
    }

    if (text) {
        console.log(`💬 [${sessionId}] Text response received: ${text.substring(0, 100)}...`);
        connectionData.lastAIResponse = Date.now();
        connectionData.responseTimeouts = 0;
        connectionData.connectionQuality = 'good';
    }
}

async function initTranscription(sessionId, deps, connectionData) {
    try {
        const dgConnection = await deps.transcriptionManager.initializeLocalTranscription(sessionId, connectionData);
        if (dgConnection) {
            connectionData.deepgramConnection = dgConnection;
        }
    } catch {}
}
