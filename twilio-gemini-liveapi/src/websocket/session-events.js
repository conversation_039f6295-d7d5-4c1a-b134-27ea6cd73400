import { endSession } from './session-utils.js';

export async function handleEndSession(sessionId, deps, activeConnections, lifecycleManager) {
    const connectionData = activeConnections.get(sessionId);
    if (connectionData) {
        if (connectionData.deepgramConnection) {
            deps.transcriptionManager.closeTranscription(sessionId);
        }
        if (lifecycleManager) {
            await lifecycleManager.requestSessionEnd(sessionId, connectionData, 'user_end_testing');
        } else {
            await endSession(sessionId, { ...deps, transcriptionManager: deps.transcriptionManager }, 'user_requested');
        }
    } else {
        await endSession(sessionId, { ...deps, transcriptionManager: deps.transcriptionManager }, 'user_requested');
    }
}

export async function handleRequestSummary(sessionId, _deps, activeConnections, summaryManager, contextManager) {
    const summaryConnectionData = activeConnections.get(sessionId);
    if (summaryConnectionData && summaryManager) {
        await summaryManager.requestSummary(sessionId, summaryConnectionData, contextManager);
    }
}
