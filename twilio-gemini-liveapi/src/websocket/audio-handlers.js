export async function handleAudioData(
    sessionId,
    data,
    geminiSession,
    isSessionActive,
    deps,
    activeConnections,
    lifecycleManager,
    recoveryManager,
    flowType
) {
    if (geminiSession && isSessionActive && (data.audioData || data.audio)) {
        try {
            lifecycleManager.updateActivity(sessionId);
            const base64Audio = data.audioData || data.audio;
            await deps.sessionManager.sendBrowserAudioToGemini(sessionId, geminiSession, base64Audio);
            const connectionData = activeConnections.get(sessionId);
            if (connectionData && connectionData.deepgramConnection && deps.transcriptionManager) {
                try {
                    const audioBuffer = Buffer.from(base64Audio, 'base64');
                    deps.transcriptionManager.sendAudioToTranscription(sessionId, audioBuffer);
                } catch {}
            }
        } catch (error) {
            const connectionData = activeConnections.get(sessionId);
            if (connectionData && recoveryManager && recoveryManager.needsRecovery(sessionId, activeConnections)) {
                await recoveryManager.recoverSession(sessionId, 'audio_processing_error', activeConnections);
            }
        }
    }
}

export async function handleTextMessage(sessionId, data, geminiSession, isSessionActive, deps) {
    if (geminiSession && isSessionActive && data.text) {
        try {
            await deps.sessionManager.sendTextToGemini(sessionId, geminiSession, data.text);
        } catch {}
    }
}

export async function handleTurnComplete(sessionId, geminiSession, isSessionActive, deps) {
    if (geminiSession && isSessionActive) {
        try {
            await deps.sessionManager.sendTurnComplete(sessionId, geminiSession);
        } catch {}
    }
}
