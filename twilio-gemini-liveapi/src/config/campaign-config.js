// Campaign Configuration System
// Replaces hardcoded campaign scripts with configurable system

import { readFileSync, existsSync } from 'fs';
import path from 'path';
import { config, getConfigValue } from './config.js';
import { configLogger as logger } from '../utils/logger.js';

/**
 * Campaign Configuration Manager
 * Handles loading and managing campaign scripts from various sources
 */
export class CampaignConfigManager {
    constructor() {
        this.scriptsPath = getConfigValue('campaigns.scriptsPath');
        this.totalCampaigns = getConfigValue('campaigns.totalCampaigns', 6);
        this.defaultCampaignId = getConfigValue('campaigns.defaultCampaignId', 1);
        this.enableCustomScripts = getConfigValue('campaigns.enableCustomScripts', false);
        this.scriptCache = new Map();
        this.cacheTimeout = getConfigValue('campaigns.scriptCacheTimeout', 300) * 1000; // Convert to ms
    }

    /**
     * Load campaign script from file system
     */
    loadCampaignFromFile(campaignId, type = 'outbound') {
        try {
            let fileName;
            if (type === 'outbound') {
                fileName = `campaign${campaignId}.json`;
            } else {
                fileName = `incoming-campaign${campaignId}.json`;
            }

            // Validate and sanitize the file name to prevent path traversal
            if (!fileName.match(/^[a-zA-Z0-9-]+\.json$/)) {
                logger.error(`Invalid campaign file name format: ${fileName}`);
                return null;
            }
            
            // Ensure the file name doesn't contain path traversal sequences
            const sanitizedFileName = path.basename(fileName);
            if (sanitizedFileName !== fileName) {
                logger.error(`Potential path traversal attempt detected: ${fileName}`);
                return null;
            }
            
            const filePath = path.join(this.scriptsPath, sanitizedFileName);
            
            if (!existsSync(filePath)) {
                logger.warn(`Campaign file not found: ${filePath}`);
                return null;
            }

            const readStartTime = Date.now();
            const fileContent = readFileSync(filePath, 'utf8');
            const readTime = Date.now() - readStartTime;
            
            const parseStartTime = Date.now();
            const campaignScript = JSON.parse(fileContent);
            const parseTime = Date.now() - parseStartTime;
            
            logger.info(`⏱️ Campaign ${campaignId} (${type}) load times: read=${readTime}ms, parse=${parseTime}ms`);
            
            // Apply configuration overrides
            this.applyCampaignConfigOverrides(campaignScript);
            
            return campaignScript;
        } catch (error) {
            logger.error(`Error loading campaign ${campaignId} (${type})`, { error });
            return null;
        }
    }

    /**
     * Apply configuration overrides to campaign script
     */
    applyCampaignConfigOverrides(campaignScript) {
        // Override transfer data with configuration
        if (campaignScript.transferData) {
            campaignScript.transferData.transferNumber = 
                process.env[`TRANSFER_NUMBER_CAMPAIGN_${campaignScript.id}`] || 
                getConfigValue('business.transfer.defaultTransferNumber');
            
            campaignScript.transferData.agentName = 
                process.env[`AGENT_NAME_CAMPAIGN_${campaignScript.id}`] || 
                getConfigValue('business.transfer.defaultAgentName');
        }

        // Override validation rules with configuration
        if (campaignScript.customerData?.optionalFieldsPreTransfer) {
            campaignScript.customerData.optionalFieldsPreTransfer.forEach(field => {
                if (field.field === 'vehicleCount' && field.disqualificationRule) {
                    const maxVehicles = getConfigValue('business.validation.maxVehicles', 9);
                    field.disqualificationRule.condition = `value <= 0 || value >= ${maxVehicles + 1}`;
                }
                if (field.field === 'claimsCount3yrs' && field.disqualificationRule) {
                    const maxClaims = getConfigValue('business.validation.maxClaims', 3);
                    field.disqualificationRule.condition = `value >= ${maxClaims}`;
                }
                if (field.field === 'vehicleYear1' && field.invalidResponseHandlers?.length > 0) {
                    const minYear = getConfigValue('business.validation.minVehicleYear', 1900);
                    const maxYear = getConfigValue('business.validation.maxVehicleYear', 2027);
                    field.invalidResponseHandlers[0].condition = 
                        `response.type != 'integer' || response.value < ${minYear} || response.value > ${maxYear}`;
                }
            });
        }

        // Override vocabulary restrictions
        if (campaignScript.agentPersona) {
            campaignScript.agentPersona.vocabularyRestrictions = 
                getConfigValue('security.vocabularyRestrictions', []);
            
            campaignScript.agentPersona.recordedMessageConfirmation = 
                getConfigValue('security.recordingConfirmationMessage');
        }

        return campaignScript;
    }

    /**
     * Get campaign script with caching
     */
    getCampaignScript(campaignId, type = 'outbound', useCache = true) {
        const cacheKey = `${type}_${campaignId}`;
        
        // Check cache first
        if (useCache && this.scriptCache.has(cacheKey)) {
            const cached = this.scriptCache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.script;
            }
            // Cache expired, remove it
            this.scriptCache.delete(cacheKey);
        }

        // Load from source
        let script = null;
        
        // Try loading from file system first
        script = this.loadCampaignFromFile(campaignId, type);
        
        // If custom scripts are enabled, try loading from database/API
        if (!script && this.enableCustomScripts) {
            script = this.loadCampaignFromDatabase(campaignId, type);
        }
        
        // Fallback to default template
        if (!script) {
            script = this.createDefaultCampaignTemplate(campaignId, type);
        }

        // Cache the result
        if (useCache && script) {
            this.scriptCache.set(cacheKey, {
                script: script,
                timestamp: Date.now()
            });
        }

        return script;
    }

    /**
     * Load campaign from database (placeholder for future implementation)
     */
    loadCampaignFromDatabase(campaignId, type) {
        // TODO: Implement database loading
        logger.info(`Database loading not implemented for campaign ${campaignId} (${type})`);
        return null;
    }

    /**
     * Create default campaign template
     */
    createDefaultCampaignTemplate(campaignId, type) {
        const language = getConfigValue('localization.defaultLanguage', 'en');
        const defaultVoice = getConfigValue(`voices.defaultVoiceMapping.${language}.${type}`, 'Kore');
        
        return {
            id: campaignId,
            type: type,
            language: language,
            category: 'general',
            title: `Campaign ${campaignId} (${type.charAt(0).toUpperCase() + type.slice(1)})`,
            campaign: `Default ${type} campaign`,
            agentPersona: {
                name: 'Agent',
                tone: 'Professional, helpful',
                humanEmulation: true,
                voice: defaultVoice,
                vocabularyRestrictions: getConfigValue('security.vocabularyRestrictions', []),
                recordedMessageConfirmation: getConfigValue('security.recordingConfirmationMessage')
            },
            customerData: {
                optionalFieldsPreTransfer: []
            },
            transferData: {
                transferNumber: getConfigValue('business.transfer.defaultTransferNumber'),
                agentName: getConfigValue('business.transfer.defaultAgentName'),
                warmTransferIntroductionAgent: 'Transferring customer. Ready?',
                warmTransferIntroductionCustomer: 'I will now transfer you to an agent. Please hold.',
                specialistNotAvailableMessage: 'All agents are busy. We will call you back.'
            },
            script: {
                start: [
                    { 
                        type: 'statement', 
                        content: 'Hello, how can I help you today?' 
                    }
                ]
            }
        };
    }

    /**
     * Get all available campaigns
     */
    getAllCampaigns(type = 'outbound') {
        const campaigns = [];
        for (let i = 1; i <= this.totalCampaigns; i++) {
            const campaign = this.getCampaignScript(i, type);
            if (campaign) {
                campaigns.push(campaign);
            }
        }
        return campaigns;
    }

    /**
     * Validate campaign script structure
     */
    validateCampaignScript(script) {
        const required = ['id', 'type', 'title', 'agentPersona', 'script'];
        for (const field of required) {
            if (!script[field]) {
                throw new Error(`Missing required field in campaign script: ${field}`);
            }
        }
        
        if (!script.agentPersona.name) {
            throw new Error('Missing agent persona name');
        }
        
        if (!script.script.start || !Array.isArray(script.script.start)) {
            throw new Error('Invalid script structure: missing or invalid start section');
        }
        
        return true;
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.scriptCache.clear();
    }

    /**
     * Get cache statistics
     */
    getCacheStats() {
        return {
            size: this.scriptCache.size,
            keys: Array.from(this.scriptCache.keys())
        };
    }
}

// Export singleton instance
export const campaignConfigManager = new CampaignConfigManager();

// Export utility functions
export function getCampaignScript(campaignId, type = 'outbound') {
    return campaignConfigManager.getCampaignScript(campaignId, type);
}

export function getAllCampaigns(type = 'outbound') {
    return campaignConfigManager.getAllCampaigns(type);
}

export function validateCampaignScript(script) {
    return campaignConfigManager.validateCampaignScript(script);
}
