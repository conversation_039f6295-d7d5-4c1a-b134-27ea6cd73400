// Audio and Media Configuration System
// Replaces hardcoded audio formats, sample rates, and media URLs

import { config, getConfigValue } from './config.js';
import path from 'path';
import { configLogger as logger } from '../utils/logger.js';

/**
 * Audio Configuration Manager
 * Handles audio formats, sample rates, file paths, and media URLs
 */
export class AudioConfigManager {
    constructor() {
        // Audio format settings
        this.inputFormat = getConfigValue('audio.inputFormat', 'g711_ulaw');
        this.outputFormat = getConfigValue('audio.outputFormat', 'g711_ulaw');
        this.sampleRate = getConfigValue('audio.sampleRate', 16000);
        this.twilioSampleRate = getConfigValue('audio.twilioSampleRate', 8000);
        this.geminiOutputRate = getConfigValue('ai.gemini.outputRate', 24000);
        
        // Media URLs and paths
        this.greetingAudioUrl = getConfigValue('audio.greetingAudioUrl');
        this.publicUrl = getConfigValue('server.publicUrl');
        
        // Audio file configurations
        this.audioFiles = this.initializeAudioFiles();
        
        // Supported formats
        this.supportedFormats = this.initializeSupportedFormats();
    }

    /**
     * Initialize audio file configurations
     */
    initializeAudioFiles() {
        const baseUrl = this.publicUrl;
        
        return {
            greeting: {
                default: this.greetingAudioUrl || `${baseUrl}/intro.mp3`,
                en: process.env.GREETING_AUDIO_URL_EN || `${baseUrl}/intro-en.mp3`,
                es: process.env.GREETING_AUDIO_URL_ES || `${baseUrl}/intro-es.mp3`,
                cz: process.env.GREETING_AUDIO_URL_CZ || `${baseUrl}/intro-cz.mp3`
            },
            hold: {
                default: process.env.HOLD_MUSIC_URL || `${baseUrl}/hold-music.mp3`,
                en: process.env.HOLD_MUSIC_URL_EN || `${baseUrl}/hold-music-en.mp3`,
                es: process.env.HOLD_MUSIC_URL_ES || `${baseUrl}/hold-music-es.mp3`,
                cz: process.env.HOLD_MUSIC_URL_CZ || `${baseUrl}/hold-music-cz.mp3`
            },
            transfer: {
                default: process.env.TRANSFER_AUDIO_URL || `${baseUrl}/transfer.mp3`,
                en: process.env.TRANSFER_AUDIO_URL_EN || `${baseUrl}/transfer-en.mp3`,
                es: process.env.TRANSFER_AUDIO_URL_ES || `${baseUrl}/transfer-es.mp3`,
                cz: process.env.TRANSFER_AUDIO_URL_CZ || `${baseUrl}/transfer-cz.mp3`
            },
            unavailable: {
                default: process.env.UNAVAILABLE_AUDIO_URL || `${baseUrl}/unavailable.mp3`,
                en: process.env.UNAVAILABLE_AUDIO_URL_EN || `${baseUrl}/unavailable-en.mp3`,
                es: process.env.UNAVAILABLE_AUDIO_URL_ES || `${baseUrl}/unavailable-es.mp3`,
                cz: process.env.UNAVAILABLE_AUDIO_URL_CZ || `${baseUrl}/unavailable-cz.mp3`
            }
        };
    }

    /**
     * Initialize supported audio formats
     */
    initializeSupportedFormats() {
        return {
            input: {
                'g711_ulaw': {
                    name: 'G.711 μ-law',
                    description: 'Standard telephony format',
                    sampleRate: 8000,
                    bitRate: 64000,
                    channels: 1
                },
                'g711_alaw': {
                    name: 'G.711 A-law',
                    description: 'European telephony format',
                    sampleRate: 8000,
                    bitRate: 64000,
                    channels: 1
                },
                'pcm': {
                    name: 'PCM',
                    description: 'Uncompressed audio',
                    sampleRate: [8000, 16000, 24000, 48000],
                    bitRate: 'variable',
                    channels: [1, 2]
                }
            },
            output: {
                'g711_ulaw': {
                    name: 'G.711 μ-law',
                    description: 'Standard telephony format',
                    sampleRate: 8000,
                    bitRate: 64000,
                    channels: 1
                },
                'g711_alaw': {
                    name: 'G.711 A-law',
                    description: 'European telephony format',
                    sampleRate: 8000,
                    bitRate: 64000,
                    channels: 1
                },
                'mp3': {
                    name: 'MP3',
                    description: 'Compressed audio format',
                    sampleRate: [8000, 16000, 22050, 44100, 48000],
                    bitRate: [64000, 128000, 192000, 256000, 320000],
                    channels: [1, 2]
                }
            }
        };
    }

    /**
     * Get audio file URL for specific type and language
     */
    getAudioFileUrl(type, language = 'default') {
        const audioType = this.audioFiles[type];
        if (!audioType) {
            logger.warn(`Unknown audio type: ${type}`);
            return null;
        }
        
        return audioType[language] || audioType.default;
    }

    /**
     * Get greeting audio URL for language
     */
    getGreetingAudioUrl(language = 'default') {
        return this.getAudioFileUrl('greeting', language);
    }

    /**
     * Get hold music URL for language
     */
    getHoldMusicUrl(language = 'default') {
        return this.getAudioFileUrl('hold', language);
    }

    /**
     * Get transfer audio URL for language
     */
    getTransferAudioUrl(language = 'default') {
        return this.getAudioFileUrl('transfer', language);
    }

    /**
     * Get unavailable message audio URL for language
     */
    getUnavailableAudioUrl(language = 'default') {
        return this.getAudioFileUrl('unavailable', language);
    }

    /**
     * Get audio format configuration
     */
    getAudioFormatConfig() {
        return {
            input: {
                format: this.inputFormat,
                sampleRate: this.sampleRate,
                channels: 1,
                encoding: this.getFormatEncoding(this.inputFormat)
            },
            output: {
                format: this.outputFormat,
                sampleRate: this.twilioSampleRate,
                channels: 1,
                encoding: this.getFormatEncoding(this.outputFormat)
            },
            gemini: {
                outputRate: this.geminiOutputRate,
                inputFormat: 'audio/pcm',
                outputFormat: 'audio/pcm'
            }
        };
    }

    /**
     * Get encoding for audio format
     */
    getFormatEncoding(format) {
        const encodings = {
            'g711_ulaw': 'MULAW',
            'g711_alaw': 'ALAW',
            'pcm': 'LINEAR16',
            'mp3': 'MP3'
        };
        
        return encodings[format] || 'LINEAR16';
    }

    /**
     * Validate audio format
     */
    isFormatSupported(format, type = 'input') {
        return this.supportedFormats[type] && this.supportedFormats[type][format];
    }

    /**
     * Get optimal sample rate for format
     */
    getOptimalSampleRate(format, type = 'input') {
        const formatConfig = this.supportedFormats[type]?.[format];
        if (!formatConfig) return this.sampleRate;
        
        if (Array.isArray(formatConfig.sampleRate)) {
            // Return the highest supported rate that's <= our target
            const targetRate = type === 'input' ? this.sampleRate : this.twilioSampleRate;
            return formatConfig.sampleRate
                .filter(rate => rate <= targetRate)
                .sort((a, b) => b - a)[0] || formatConfig.sampleRate[0];
        }
        
        return formatConfig.sampleRate;
    }

    /**
     * Get audio processing configuration
     */
    getAudioProcessingConfig() {
        return {
            enableNoiseReduction: process.env.ENABLE_NOISE_REDUCTION === 'true',
            enableEchoCancellation: process.env.ENABLE_ECHO_CANCELLATION === 'true',
            enableAutoGainControl: process.env.ENABLE_AUTO_GAIN_CONTROL === 'true',
            volumeNormalization: process.env.ENABLE_VOLUME_NORMALIZATION === 'true',
            silenceDetection: {
                enabled: process.env.ENABLE_SILENCE_DETECTION === 'true',
                threshold: parseFloat(process.env.SILENCE_THRESHOLD) || -50,
                duration: parseInt(process.env.SILENCE_DURATION) || 2000
            }
        };
    }

    /**
     * Get media file path for local files
     */
    getLocalMediaPath(filename) {
        const mediaDir = process.env.MEDIA_DIRECTORY || path.join(process.cwd(), 'public');
        return path.join(mediaDir, filename);
    }

    /**
     * Update audio configuration at runtime
     */
    updateAudioConfig(updates) {
        if (updates.inputFormat && this.isFormatSupported(updates.inputFormat, 'input')) {
            this.inputFormat = updates.inputFormat;
        }
        
        if (updates.outputFormat && this.isFormatSupported(updates.outputFormat, 'output')) {
            this.outputFormat = updates.outputFormat;
        }
        
        if (updates.sampleRate && updates.sampleRate > 0) {
            this.sampleRate = updates.sampleRate;
        }
        
        if (updates.twilioSampleRate && updates.twilioSampleRate > 0) {
            this.twilioSampleRate = updates.twilioSampleRate;
        }
        
        return this.getAudioFormatConfig();
    }

    /**
     * Get configuration summary
     */
    getConfigSummary() {
        return {
            inputFormat: this.inputFormat,
            outputFormat: this.outputFormat,
            sampleRate: this.sampleRate,
            twilioSampleRate: this.twilioSampleRate,
            geminiOutputRate: this.geminiOutputRate,
            supportedInputFormats: Object.keys(this.supportedFormats.input),
            supportedOutputFormats: Object.keys(this.supportedFormats.output),
            audioFiles: Object.keys(this.audioFiles)
        };
    }
}

// Export singleton instance
export const audioConfigManager = new AudioConfigManager();

// Export utility functions
export function getAudioFileUrl(type, language = 'default') {
    return audioConfigManager.getAudioFileUrl(type, language);
}

export function getGreetingAudioUrl(language = 'default') {
    return audioConfigManager.getGreetingAudioUrl(language);
}

export function getAudioFormatConfig() {
    return audioConfigManager.getAudioFormatConfig();
}

export function isFormatSupported(format, type = 'input') {
    return audioConfigManager.isFormatSupported(format, type);
}
