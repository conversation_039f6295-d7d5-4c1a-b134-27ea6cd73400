// Business Logic Configuration System
// Replaces hardcoded timeouts, thresholds, validation rules, and business constants

import { config, getConfigValue } from './config.js';

/**
 * Business Configuration Manager
 * Handles business logic constants, validation rules, and operational parameters
 */
export class BusinessConfigManager {
    constructor() {
        // Call timeout configurations
        this.callTimeouts = {
            default: getConfigValue('business.callTimeouts.default', 30),
            intro: getConfigValue('business.callTimeouts.intro', 4),
            response: getConfigValue('business.callTimeouts.response', 10),
            transfer: getConfigValue('business.transfer.transferTimeout', 30),
            recording: parseInt(process.env.RECORDING_TIMEOUT) || 300,
            silence: parseInt(process.env.SILENCE_TIMEOUT) || 5
        };

        // Validation rules
        this.validationRules = {
            vehicles: {
                min: 1,
                max: getConfigValue('business.validation.maxVehicles', 9),
                defaultDisqualificationMessage: 'Invalid number of vehicles'
            },
            claims: {
                min: 0,
                max: getConfigValue('business.validation.maxClaims', 3),
                defaultDisqualificationMessage: 'Too many claims in the last 3 years'
            },
            vehicleYear: {
                min: getConfigValue('business.validation.minVehicleYear', 1900),
                max: getConfigValue('business.validation.maxVehicleYear', 2027),
                defaultErrorMessage: 'Please provide a valid 4-digit year'
            },
            phoneNumber: {
                minLength: parseInt(process.env.PHONE_MIN_LENGTH) || 10,
                maxLength: parseInt(process.env.PHONE_MAX_LENGTH) || 15,
                allowInternational: process.env.ALLOW_INTERNATIONAL_NUMBERS === 'true'
            },
            name: {
                minLength: parseInt(process.env.NAME_MIN_LENGTH) || 2,
                maxLength: parseInt(process.env.NAME_MAX_LENGTH) || 50,
                allowSpecialChars: process.env.ALLOW_SPECIAL_CHARS_IN_NAME === 'true'
            }
        };

        // Transfer configurations
        this.transferConfig = {
            defaultNumber: getConfigValue('business.transfer.defaultTransferNumber', '************'),
            defaultAgentName: getConfigValue('business.transfer.defaultAgentName', 'Sarah Johnson'),
            timeout: getConfigValue('business.transfer.transferTimeout', 30),
            maxAttempts: parseInt(process.env.TRANSFER_MAX_ATTEMPTS) || 3,
            retryDelay: parseInt(process.env.TRANSFER_RETRY_DELAY) || 5,
            warmTransferEnabled: process.env.ENABLE_WARM_TRANSFER === 'true',
            coldTransferEnabled: process.env.ENABLE_COLD_TRANSFER !== 'false'
        };

        // Campaign-specific configurations
        this.campaignConfig = {
            maxCampaigns: getConfigValue('campaigns.totalCampaigns', 6),
            defaultCampaignId: getConfigValue('campaigns.defaultCampaignId', 1),
            enableCustomScripts: getConfigValue('campaigns.enableCustomScripts', false),
            scriptCacheTimeout: getConfigValue('campaigns.scriptCacheTimeout', 300),
            maxScriptLength: parseInt(process.env.MAX_SCRIPT_LENGTH) || 10000,
            enableScriptValidation: process.env.ENABLE_SCRIPT_VALIDATION !== 'false'
        };

        // Performance and limits
        this.performanceConfig = {
            maxConcurrentCalls: getConfigValue('performance.maxConcurrentCalls', 100),
            callQueueSize: parseInt(process.env.CALL_QUEUE_SIZE) || 50,
            maxCallDuration: parseInt(process.env.MAX_CALL_DURATION) || 1800, // 30 minutes
            enableCallRecording: process.env.ENABLE_CALL_RECORDING !== 'false',
            enableCallTranscription: getConfigValue('transcription.enabled', true),
            enableMetrics: getConfigValue('performance.enableMetrics', true)
        };

        // Retry and error handling
        this.retryConfig = {
            maxRetries: parseInt(process.env.MAX_RETRIES) || 3,
            retryDelay: parseInt(process.env.RETRY_DELAY) || 1000,
            backoffMultiplier: parseFloat(process.env.BACKOFF_MULTIPLIER) || 2,
            maxRetryDelay: parseInt(process.env.MAX_RETRY_DELAY) || 30000,
            enableExponentialBackoff: process.env.ENABLE_EXPONENTIAL_BACKOFF !== 'false'
        };

        // Security and compliance
        this.securityConfig = {
            enableRecordingConfirmation: getConfigValue('security.enableRecordingConfirmation', true),
            recordingConfirmationMessage: getConfigValue('security.recordingConfirmationMessage'),
            vocabularyRestrictions: getConfigValue('security.vocabularyRestrictions', []),
            enableContentFiltering: process.env.ENABLE_CONTENT_FILTERING === 'true',
            maxCallsPerNumber: parseInt(process.env.MAX_CALLS_PER_NUMBER) || 10,
            callRateLimitWindow: parseInt(process.env.CALL_RATE_LIMIT_WINDOW) || 3600 // 1 hour
        };
    }

    /**
     * Get call timeout for specific type
     */
    getCallTimeout(type = 'default') {
        return this.callTimeouts[type] || this.callTimeouts.default;
    }

    /**
     * Validate vehicle count
     */
    validateVehicleCount(count) {
        const rules = this.validationRules.vehicles;
        const numCount = parseInt(count);
        
        if (isNaN(numCount) || numCount < rules.min || numCount > rules.max) {
            return {
                valid: false,
                error: `Vehicle count must be between ${rules.min} and ${rules.max}`,
                disqualify: numCount > rules.max
            };
        }
        
        return { valid: true };
    }

    /**
     * Validate claims count
     */
    validateClaimsCount(count) {
        const rules = this.validationRules.claims;
        const numCount = parseInt(count);
        
        if (isNaN(numCount) || numCount < rules.min) {
            return {
                valid: false,
                error: `Claims count must be ${rules.min} or greater`
            };
        }
        
        if (numCount > rules.max) {
            return {
                valid: false,
                error: rules.defaultDisqualificationMessage,
                disqualify: true
            };
        }
        
        return { valid: true };
    }

    /**
     * Validate vehicle year
     */
    validateVehicleYear(year) {
        const rules = this.validationRules.vehicleYear;
        const numYear = parseInt(year);
        
        if (isNaN(numYear) || numYear < rules.min || numYear > rules.max) {
            return {
                valid: false,
                error: `${rules.defaultErrorMessage} (${rules.min}-${rules.max})`
            };
        }
        
        return { valid: true };
    }

    /**
     * Validate phone number
     */
    validatePhoneNumber(phoneNumber) {
        const rules = this.validationRules.phoneNumber;
        
        if (!phoneNumber || typeof phoneNumber !== 'string') {
            return { valid: false, error: 'Phone number is required' };
        }
        
        // Remove non-digit characters for length check
        const digitsOnly = phoneNumber.replace(/\D/g, '');
        
        if (digitsOnly.length < rules.minLength || digitsOnly.length > rules.maxLength) {
            return {
                valid: false,
                error: `Phone number must be ${rules.minLength}-${rules.maxLength} digits`
            };
        }
        
        // Check international format if not allowed
        if (!rules.allowInternational && phoneNumber.startsWith('+')) {
            return { valid: false, error: 'International numbers not allowed' };
        }
        
        return { valid: true };
    }

    /**
     * Validate name
     */
    validateName(name) {
        const rules = this.validationRules.name;
        
        if (!name || typeof name !== 'string') {
            return { valid: false, error: 'Name is required' };
        }
        
        const trimmedName = name.trim();
        
        if (trimmedName.length < rules.minLength || trimmedName.length > rules.maxLength) {
            return {
                valid: false,
                error: `Name must be ${rules.minLength}-${rules.maxLength} characters`
            };
        }
        
        // Check for special characters if not allowed
        if (!rules.allowSpecialChars && /[^a-zA-Z\s]/.test(trimmedName)) {
            return { valid: false, error: 'Name can only contain letters and spaces' };
        }
        
        return { valid: true };
    }

    /**
     * Get transfer configuration
     */
    getTransferConfig(campaignId = null) {
        const config = { ...this.transferConfig };
        
        // Override with campaign-specific settings if available
        if (campaignId) {
            const campaignTransferNumber = process.env[`TRANSFER_NUMBER_CAMPAIGN_${campaignId}`];
            const campaignAgentName = process.env[`AGENT_NAME_CAMPAIGN_${campaignId}`];
            
            if (campaignTransferNumber) config.defaultNumber = campaignTransferNumber;
            if (campaignAgentName) config.defaultAgentName = campaignAgentName;
        }
        
        return config;
    }

    /**
     * Check if call should be rate limited
     */
    shouldRateLimit(phoneNumber, callHistory = []) {
        const config = this.securityConfig;
        const windowStart = Date.now() - (config.callRateLimitWindow * 1000);
        
        const recentCalls = callHistory.filter(call => 
            call.phoneNumber === phoneNumber && 
            call.timestamp > windowStart
        );
        
        return recentCalls.length >= config.maxCallsPerNumber;
    }

    /**
     * Get retry configuration for operation
     */
    getRetryConfig(operation = 'default') {
        const config = { ...this.retryConfig };
        
        // Operation-specific overrides
        const operationOverrides = {
            'api_call': {
                maxRetries: parseInt(process.env.API_CALL_MAX_RETRIES) || config.maxRetries,
                retryDelay: parseInt(process.env.API_CALL_RETRY_DELAY) || config.retryDelay
            },
            'database': {
                maxRetries: parseInt(process.env.DB_MAX_RETRIES) || config.maxRetries,
                retryDelay: parseInt(process.env.DB_RETRY_DELAY) || config.retryDelay
            },
            'transfer': {
                maxRetries: this.transferConfig.maxAttempts,
                retryDelay: this.transferConfig.retryDelay * 1000
            }
        };
        
        return { ...config, ...operationOverrides[operation] };
    }

    /**
     * Get performance limits
     */
    getPerformanceLimits() {
        return { ...this.performanceConfig };
    }

    /**
     * Update business configuration at runtime
     */
    updateBusinessConfig(section, updates) {
        const sections = {
            'timeouts': 'callTimeouts',
            'validation': 'validationRules',
            'transfer': 'transferConfig',
            'campaign': 'campaignConfig',
            'performance': 'performanceConfig',
            'retry': 'retryConfig',
            'security': 'securityConfig'
        };
        
        const configSection = sections[section];
        if (configSection && this[configSection]) {
            this[configSection] = { ...this[configSection], ...updates };
            return true;
        }
        
        return false;
    }

    /**
     * Get configuration summary
     */
    getConfigSummary() {
        return {
            callTimeouts: Object.keys(this.callTimeouts),
            validationRules: Object.keys(this.validationRules),
            transferConfig: {
                defaultNumber: this.transferConfig.defaultNumber,
                defaultAgentName: this.transferConfig.defaultAgentName,
                warmTransferEnabled: this.transferConfig.warmTransferEnabled
            },
            performanceLimits: {
                maxConcurrentCalls: this.performanceConfig.maxConcurrentCalls,
                maxCallDuration: this.performanceConfig.maxCallDuration
            },
            securityFeatures: {
                recordingConfirmation: this.securityConfig.enableRecordingConfirmation,
                contentFiltering: this.securityConfig.enableContentFiltering,
                rateLimiting: this.securityConfig.maxCallsPerNumber > 0
            }
        };
    }
}

// Export singleton instance
export const businessConfigManager = new BusinessConfigManager();

// Export utility functions
export function getCallTimeout(type = 'default') {
    return businessConfigManager.getCallTimeout(type);
}

export function validateVehicleCount(count) {
    return businessConfigManager.validateVehicleCount(count);
}

export function validateClaimsCount(count) {
    return businessConfigManager.validateClaimsCount(count);
}

export function validateVehicleYear(year) {
    return businessConfigManager.validateVehicleYear(year);
}

export function getTransferConfig(campaignId = null) {
    return businessConfigManager.getTransferConfig(campaignId);
}

export function shouldRateLimit(phoneNumber, callHistory = []) {
    return businessConfigManager.shouldRateLimit(phoneNumber, callHistory);
}
