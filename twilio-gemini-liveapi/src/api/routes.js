// Main API routes for the Twilio Gemini service
import { SecurityUtils } from '../middleware/security-utils.js';
import { AudioProcessor } from '../audio/audio-processor.js';
import { TranscriptionManager } from '../audio/transcription-manager.js';
import { loadCampaignScript } from '../../campaign-script-loader.js';
import twilio from 'twilio';
import { config } from '../config/config.js';
import { validateTwilioWebhook } from '../utils/twilio-validation.js';

export function registerApiRoutes(fastify, dependencies) {
    console.log('🚀 Starting API route registration...');
    console.log('🔍 [DEBUG] Dependencies received:', Object.keys(dependencies || {}));
    const {
        sessionManager,
        contextManager,
        activeConnections,
        healthMonitor,
        recoveryManager,
        lifecycleManager,
        summaryManager,
        scriptManager,
        voiceManager,
        modelManager,
        GEMINI_DEFAULT_VOICE,
        GEMINI_DEFAULT_MODEL,
        SUMMARY_GENERATION_PROMPT
    } = dependencies;
    console.log('✅ Dependencies extracted successfully');
    console.log('🔍 [DEBUG] scriptManager extracted:', !!scriptManager, typeof scriptManager);

    // Configuration values
    const TWILIO_ACCOUNT_SID = config.auth.twilio.accountSid;
    const TWILIO_AUTH_TOKEN = config.auth.twilio.authToken;
    const PUBLIC_URL = config.server.publicUrl;

    // Global configuration for next call - no hardcoded instructions
    let nextCallConfig = {
        aiInstructions: '', // Campaign script should provide all instructions
        voice: config.ai.gemini.defaultVoice,
        model: config.ai.gemini.defaultModel,
        targetName: null,
        targetPhoneNumber: null
    };

    // Root route - API information
    fastify.get('/', async (_request, _reply) => {
        return {
            service: 'Twilio Gemini Live API',
            status: 'running',
            version: '2.0.0',
            activeConnections: activeConnections.size,
            endpoints: {
                health: '/health',
                websocket: '/media-stream',
                localAudio: '/local-audio-session',
                configure: '/configure-call',
                endSession: '/end-session/:callSid',
                voices: '/available-voices',
                models: '/available-models',
                setVoice: '/set-voice',
                setModel: '/set-model',
                audioSettings: '/audio-settings',
                connectionMetrics: '/api/connection-metrics',
                providerHealth: '/api/provider-health',
                incomingScenarios: '/api/incoming-scenarios',
                configureIncomingScenario: '/api/configure-incoming-scenario'
            }
        };
    });

    // Enhanced health check route
    fastify.get('/health', async (_request, _reply) => {
        const contextStats = contextManager.getContextStats();
        const transcriptionManager = new TranscriptionManager();
        let transcriptionHealth;
        try {
            transcriptionHealth = await transcriptionManager.healthCheck();
        } catch (error) {
            console.error('Failed to get transcription health:', error);
            transcriptionHealth = { status: 'error', error: error.message };
        }

        return {
            status: 'ok',
            timestamp: new Date().toISOString(),
            activeConnections: activeConnections.size,
            contextStats,
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            transcription: transcriptionHealth,
            healthScore: healthMonitor ? healthMonitor.getHealthScore() : 100,
            nextCallConfig: {
                hasInstructions: !!nextCallConfig.aiInstructions,
                voice: nextCallConfig.voice,
                model: nextCallConfig.model,
                targetName: nextCallConfig.targetName
            }
        };
    });

    // INBOUND CALL WEBHOOK - Handle incoming calls from Twilio
    fastify.post('/incoming-call', async (request, reply) => {
        try {
            // Validate Twilio webhook signature
            if (!validateTwilioWebhook(request)) {
                console.error('❌ Invalid Twilio webhook signature');
                reply.status(403);
                return { error: 'Forbidden' };
            }
            
            console.log('📞 [INBOUND] Incoming call webhook received');
            console.log('📋 [INBOUND] Call data:', request.body);

            const { CallSid, From, To, CallStatus, Direction } = request.body;

            if (!CallSid) {
                reply.status(400);
                return { error: 'Missing CallSid' };
            }

            // Determine if this is an outbound or inbound call
            const isOutboundCall = Direction && Direction.startsWith('outbound');
            console.log(`📞 [${isOutboundCall ? 'OUTBOUND' : 'INBOUND'}] Call direction: ${Direction}`);

            // Get appropriate script configuration
            let scriptConfig;
            try {
                if (isOutboundCall) {
                    // For outbound calls, use the nextCallConfig that was set
                    scriptConfig = {
                        ...nextCallConfig,
                        scriptType: 'outbound'
                    };
                    console.log(`✅ [OUTBOUND] Using nextCallConfig for outbound call`);
                } else {
                    // Handle inbound calls
                    const currentScript = scriptManager.getCurrentIncomingScript();
                    if (currentScript) {
                        scriptConfig = scriptManager.getScriptConfig(currentScript.id, true);
                    } else {
                        // Fallback to default inbound config - no hardcoded instructions
                        scriptConfig = {
                            aiInstructions: '', // Campaign script should provide all instructions
                            voice: process.env.GEMINI_DEFAULT_VOICE || 'Kore',
                            model: process.env.GEMINI_DEFAULT_MODEL || 'gemini-2.5-flash-preview-native-audio-dialog',
                            scriptType: 'inbound',
                            scriptId: 'customer-service'
                        };
                    }
                }
            } catch (error) {
                console.warn('⚠️ [INBOUND] Error getting script config:', error);
                scriptConfig = {
                    aiInstructions: '', // Campaign script should provide all instructions
                    voice: 'Kore',
                    model: 'gemini-2.0-flash-exp',
                    scriptType: 'inbound',
                    scriptId: 'customer-service'
                };
            }

            // Store call configuration for WebSocket handler
            const callConfig = {
                ...scriptConfig,
                callSid: CallSid,
                from: From,
                to: To,
                callStatus: CallStatus,
                isIncomingCall: !isOutboundCall,
                timestamp: new Date().toISOString()
            };

            // Store in global state for WebSocket handler to pick up
            nextCallConfig = callConfig;
            if (fastify.setNextCallConfig) {
                fastify.setNextCallConfig(callConfig);
            }

            console.log(`✅ [${isOutboundCall ? 'OUTBOUND' : 'INBOUND'}] Call ${CallSid} configured with script: ${scriptConfig.scriptId}`);
            console.log(`📞 [${CallSid}] Call Details:`, {
                from: From,
                to: To,
                direction: Direction,
                status: CallStatus,
                scriptType: scriptConfig.scriptType,
                hasInstructions: !!scriptConfig.aiInstructions,
                voice: scriptConfig.voice,
                model: scriptConfig.model
            });

            // Return TwiML to connect to WebSocket
            const streamUrl = isOutboundCall ? '/media-stream' : '/media-stream-inbound';
            
            // Construct WebSocket URL based on PUBLIC_URL
            let wsUrl;
            if (PUBLIC_URL) {
                // Convert HTTP(S) to WS(S) protocol
                const publicUrlBase = PUBLIC_URL.replace(/^https?:/, (match) => {
                    return match === 'https:' ? 'wss:' : 'ws:';
                });
                wsUrl = `${publicUrlBase}${streamUrl}`;
            } else {
                // Fallback to request headers if PUBLIC_URL not set
                const protocol = request.protocol === 'https' ? 'wss' : 'ws';
                wsUrl = `${protocol}://${request.headers.host}${streamUrl}`;
            }
            
            console.log(`🔗 [${isOutboundCall ? 'OUTBOUND' : 'INBOUND'}] WebSocket URL: ${wsUrl}`);
            console.log(`🔗 [${CallSid}] PUBLIC_URL: ${PUBLIC_URL || 'NOT SET'}`);
            
            // Validate WebSocket URL format
            if (!wsUrl.startsWith('ws://') && !wsUrl.startsWith('wss://')) {
                console.error(`❌ [${CallSid}] Invalid WebSocket URL format: ${wsUrl}`);
                throw new Error('Invalid WebSocket URL format');
            }
            
            // Escape URL to prevent XML injection
            const escapedWsUrl = wsUrl.replace(/&/g, '&amp;')
                                     .replace(/</g, '&lt;')
                                     .replace(/>/g, '&gt;')
                                     .replace(/"/g, '&quot;')
                                     .replace(/'/g, '&apos;');
            
            const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Connect>
        <Stream url="${escapedWsUrl}" />
    </Connect>
    <Pause length="3600" />
</Response>`;

            console.log(`📄 [${isOutboundCall ? 'OUTBOUND' : 'INBOUND'}] Returning TwiML:`, twiml);
            reply.type('text/xml');
            return twiml;

        } catch (error) {
            console.error('❌ [INBOUND] Error handling incoming call:', error);
            reply.status(500);

            // Return error TwiML
            const errorTwiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="alice">Sorry, we're experiencing technical difficulties. Please try again later.</Say>
    <Hangup />
</Response>`;

            reply.type('text/xml');
            return errorTwiml;
        }
    });

    // CALL STATUS WEBHOOK - Handle call status updates from Twilio
    fastify.post('/call-status', async (request, reply) => {
        try {
            // Validate Twilio webhook signature
            if (!validateTwilioWebhook(request)) {
                console.error('❌ Invalid Twilio webhook signature');
                reply.status(403);
                return { error: 'Forbidden' };
            }
            
            // Validate request body exists
            if (!request.body) {
                reply.status(400);
                return { error: 'Request body is required' };
            }
            
            const { CallSid, CallStatus, Duration, From, To } = request.body;

            if (!CallSid) {
                reply.status(400);
                return { error: 'Missing CallSid' };
            }

            console.log(`📊 [${CallSid}] Call status update: ${CallStatus}`);

            // Log detailed call information
            if (Duration) {
                console.log(`⏱️ [${CallSid}] Call duration: ${Duration} seconds`);
            }

            // Handle call status logging
            logCallStatus(CallSid, CallStatus, Duration, From, To);
            
            // Handle session cleanup for completed/failed calls
            if (['completed', 'failed', 'canceled', 'no-answer', 'busy'].includes(CallStatus)) {
                cleanupSession(CallSid, contextManager);
            }

            // Store call status for analytics
            updateCallStatusInContext(CallSid, CallStatus, Duration, contextManager);

            return { success: true, callSid: CallSid, status: CallStatus };

        } catch (error) {
            console.error('❌ Error handling call status webhook:', error);
            reply.status(500);
            return { error: 'Internal server error', message: error.message };
        }
    });

    // Helper functions for call status handling
    function logCallStatus(callSid, status, duration, from, to) {
        switch (status) {
        case 'initiated':
            console.log(`🚀 [${callSid}] Call initiated from ${from} to ${to}`);
            break;
        case 'ringing':
            console.log(`📞 [${callSid}] Call ringing`);
            break;
        case 'answered':
            console.log(`✅ [${callSid}] Call answered`);
            break;
        case 'completed':
            console.log(`🏁 [${callSid}] Call completed (Duration: ${duration}s)`);
            break;
        case 'failed':
        case 'canceled':
        case 'no-answer':
        case 'busy':
            console.log(`❌ [${callSid}] Call ${status}`);
            break;
        default:
            console.log(`ℹ️ [${callSid}] Unknown call status: ${status}`);
        }
    }

    function cleanupSession(callSid, contextMgr) {
        if (contextMgr) {
            contextMgr.clearSessionContext(callSid);
        }
    }

    function updateCallStatusInContext(callSid, status, duration, contextMgr) {
        try {
            if (contextMgr) {
                const context = contextMgr.getSessionContext(callSid) || {};
                context.callStatus = status;
                context.duration = duration;
                context.lastStatusUpdate = new Date().toISOString();
                contextMgr.saveSessionContext(callSid, context);
            }
        } catch (error) {
            console.warn(`⚠️ [${callSid}] Error updating call status in context:`, error);
        }
    }

    // Configure next outbound call
    fastify.post('/configure-call', async (request, _reply) => {
        try {
            // Validate request body exists
            if (!request.body) {
                return {
                    success: false,
                    error: 'Request body is required'
                };
            }
            
            const { aiInstructions, voice, model, targetName, targetPhoneNumber } = request.body;
            
            nextCallConfig = {
                aiInstructions: aiInstructions || nextCallConfig.aiInstructions,
                voice: voice || GEMINI_DEFAULT_VOICE,
                model: model || GEMINI_DEFAULT_MODEL,
                targetName: targetName || null,
                targetPhoneNumber: targetPhoneNumber || null
            };

            console.log(`⚙️ Call configured for: ${nextCallConfig.targetName || 'Unknown'}`);
            
            return { 
                success: true, 
                config: nextCallConfig 
            };
        } catch (error) {
            console.error('❌ Error configuring call:', error);
            return { 
                success: false, 
                error: error.message 
            };
        }
    });

    // Get current call configuration
    fastify.get('/get-call-config', async (_request, _reply) => {
        return {
            success: true,
            config: nextCallConfig
        };
    });

    // Manually end a session
    fastify.post('/end-session/:callSid', async (request, _reply) => {
        try {
            const { callSid } = request.params;
            
            const connectionData = activeConnections.get(callSid);
            if (connectionData) {
                // Generate summary if conversation exists
                if (connectionData.conversationLog.length > 0) {
                    await sessionManager.generateSummary(callSid, connectionData, SUMMARY_GENERATION_PROMPT);
                    // End session after summary timeout
                    setTimeout(() => {
                        endSessionHelper(callSid, { sessionManager, contextManager, activeConnections });
                    }, 30000);
                } else {
                    endSessionHelper(callSid, { sessionManager, contextManager, activeConnections });
                }
                
                return { success: true, message: 'Session ending initiated' };
            } else {
                return { success: false, message: 'Session not found' };
            }
        } catch (error) {
            console.error('❌ Error ending session:', error);
            return { success: false, error: error.message };
        }
    });

    // Get available Gemini voices
    fastify.get('/available-voices', async (_request, _reply) => {
        try {
            const voiceConfig = voiceManager.getVoiceConfig();
            return {
                success: true,
                voices: voiceConfig.availableVoices,
                currentDefault: voiceConfig.defaultVoice,
                voiceMapping: voiceConfig.voiceMapping,
                voiceSelectionEnabled: voiceConfig.voiceSelectionEnabled,
                totalVoices: voiceConfig.totalVoices
            };
        } catch (error) {
            console.error('❌ Error getting available voices:', error);
            return {
                success: false,
                error: 'Failed to get available voices',
                message: error.message
            };
        }
    });

    // Voice Configuration Endpoint
    fastify.get('/api/voice-config', {
        schema: {
            description: 'Get voice configuration including available voices and current settings',
            tags: ['configuration'],
            response: {
                200: {
                    type: 'object',
                    properties: {
                        defaultVoice: { type: 'string' },
                        availableVoices: { type: 'object' },
                        voiceMapping: { type: 'object' },
                        voiceSelectionEnabled: { type: 'boolean' },
                        totalVoices: { type: 'number' },
                        voiceDescriptions: { type: 'object' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const voiceConfig = voiceManager.getVoiceConfig();
            const envVoices = config?.ai?.gemini?.voices || {};
            
            // Parse voice descriptions from env
            const voiceDescriptions = {};
            if (envVoices && typeof envVoices === 'object') {
                Object.keys(envVoices).forEach(voiceName => {
                    const description = envVoices[voiceName];
                    voiceDescriptions[voiceName] = description;
                });
            }
            
            return reply.code(200).send({
                ...voiceConfig,
                voiceDescriptions
            });
        } catch (error) {
            console.error('❌ Error getting voice config:', error);
            return reply.code(500).send({ 
                error: 'Failed to get voice configuration',
                details: error.message 
            });
        }
    });

    // Get available Gemini models
    fastify.get('/available-models', async (_request, _reply) => {
        try {
            const modelConfig = modelManager.getModelConfig();
            const availableModels = modelConfig.availableModels || {
                'gemini-2.5-flash-preview-native-audio-dialog': { name: 'Gemini 2.5 Flash Preview Native Audio Dialog', audioSupport: true },
                'gemini-2.0-flash-exp': { name: 'Gemini 2.0 Flash Experimental', audioSupport: true }
            };
            
            return {
                success: true,
                availableModels: availableModels,
                defaultModel: modelConfig.defaultModel,
                currentModel: modelConfig.currentModel,
                modelSelectionEnabled: modelConfig.modelSelectionEnabled,
                totalModels: modelConfig.totalModels || Object.keys(availableModels).length,
                configurationSource: modelConfig.configurationSource
            };
        } catch (error) {
            console.error('❌ Error getting available models:', error);
            return {
                success: false,
                error: 'Failed to get available models',
                message: error.message
            };
        }
    });

    // Set default voice for next calls
    fastify.post('/set-voice', async (request, _reply) => {
        try {
            const { voice } = request.body;

            if (!voice) {
                return {
                    success: false,
                    error: 'Voice parameter is required'
                };
            }

            const validation = voiceManager.validateVoice(voice);
            if (!validation.isValid) {
                return {
                    success: false,
                    error: validation.error,
                    suggestion: validation.suggestion,
                    availableVoices: validation.availableVoices,
                    voiceMapping: validation.voiceMapping
                };
            }

            const validVoice = validation.voice;
            nextCallConfig.voice = validVoice;

            const voiceInfo = voiceManager.getVoiceInfo(validVoice);
            return {
                success: true,
                voice: validVoice,
                voiceInfo,
                mapped: validation.mapped || false,
                originalVoice: validation.originalVoice,
                message: `Voice set to: ${validVoice} (${voiceInfo?.characteristics || 'unknown'})`
            };
        } catch (error) {
            console.error('❌ Error setting voice:', error);
            return {
                success: false,
                error: 'Internal server error',
                message: error.message
            };
        }
    });

    // Set default model for next calls
    fastify.post('/set-model', async (request, _reply) => {
        try {
            const { model } = request.body;

            if (!model) {
                return {
                    success: false,
                    error: 'Model parameter is required'
                };
            }

            const validation = modelManager.validateModel(model);
            if (!validation.isValid) {
                return {
                    success: false,
                    error: validation.error,
                    suggestion: validation.suggestion,
                    availableModels: validation.availableModels
                };
            }

            const validModel = validation.model;
            nextCallConfig.model = validModel;

            const modelInfo = modelManager.getModelInfo(validModel);
            return {
                success: true,
                model: validModel,
                modelInfo,
                message: `Model set to: ${validModel} (${modelInfo?.name || 'unknown'})`
            };
        } catch (error) {
            console.error('❌ Error setting model:', error);
            return {
                success: false,
                error: 'Internal server error',
                message: error.message
            };
        }
    });

    // Get/Set audio settings
    fastify.get('/audio-settings', async (_request, _reply) => {
        // Validate sessionManager and audioProcessor exist
        if (!sessionManager || !sessionManager.audioProcessor) {
            return {
                success: false,
                error: 'Audio processor not available'
            };
        }
        
        const audioSettings = sessionManager.audioProcessor.getAudioSettings();
        return {
            success: true,
            settings: audioSettings
        };
    });

    fastify.post('/audio-settings', async (request, _reply) => {
        try {
            // Validate request body and sessionManager
            if (!request.body) {
                return {
                    success: false,
                    error: 'Request body is required'
                };
            }
            
            if (!sessionManager || !sessionManager.audioProcessor) {
                return {
                    success: false,
                    error: 'Audio processor not available'
                };
            }
            
            const newSettings = request.body;
            sessionManager.audioProcessor.updateAudioSettings(newSettings);
            
            return {
                success: true,
                settings: sessionManager.audioProcessor.getAudioSettings(),
                message: 'Audio settings updated'
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    });

    // Get connection metrics
    fastify.get('/api/connection-metrics', async (_request, _reply) => {
        const metrics = {
            activeConnections: activeConnections.size,
            connectionDetails: [],
            contextStats: contextManager.getContextStats()
        };

        // Get details for each active connection
        for (const [sessionId, connectionData] of activeConnections.entries()) {
            const sessionMetrics = sessionManager.getSessionMetrics(sessionId);
            const recoveryStatus = contextManager.getRecoveryStatus(sessionId);
            
            metrics.connectionDetails.push({
                sessionId,
                sessionType: connectionData.sessionType || 'unknown',
                isActive: connectionData.isSessionActive,
                startTime: sessionMetrics?.startTime,
                lastActivity: sessionMetrics?.lastActivity,
                messagesReceived: sessionMetrics?.messagesReceived || 0,
                messagesSent: sessionMetrics?.messagesSent || 0,
                recoveryCount: sessionMetrics?.recoveryCount || 0,
                recoveryStatus
            });
        }

        return {
            success: true,
            metrics
        };
    });

    // Provider health check
    fastify.get('/api/provider-health', async (_request, _reply) => {
        // This would check Gemini API health, Twilio connectivity, etc.
        return {
            success: true,
            providers: {
                gemini: {
                    status: 'healthy',
                    lastCheck: new Date().toISOString()
                },
                twilio: {
                    status: 'healthy',
                    lastCheck: new Date().toISOString()
                }
            }
        };
    });

    // Manual recovery trigger
    fastify.post('/api/trigger-recovery/:callSid', async (request, _reply) => {
        try {
            const { callSid } = request.params;
            const connectionData = activeConnections.get(callSid);
            
            if (!connectionData) {
                return { success: false, message: 'Session not found' };
            }

            // Mark as interrupted and trigger recovery
            contextManager.markSessionInterrupted(callSid, 'manual_recovery');
            
            // Trigger recovery
            setTimeout(() => {
                sessionManager.recoverSession(callSid, 'manual_recovery');
            }, 1000);

            return {
                success: true,
                message: 'Recovery triggered',
                callSid
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    });

    // Audio settings endpoints
    fastify.post('/api/audio-settings', async (request, reply) => {
        try {
            const validatedSettings = SecurityUtils.validateAudioSettings(request.body);

            if (!validatedSettings) {
                reply.status(400);
                return { success: false, error: 'Invalid audio settings' };
            }

            // Update settings in AudioProcessor
            const audioProcessor = new AudioProcessor();
            audioProcessor.updateAudioSettings(validatedSettings);

            return {
                success: true,
                settings: audioProcessor.getAudioSettings(),
                message: 'Audio settings updated successfully',
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error updating audio settings:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    fastify.get('/api/audio-settings', async (request, reply) => {
        try {
            const audioProcessor = new AudioProcessor();
            return {
                success: true,
                settings: audioProcessor.getAudioSettings(),
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting audio settings:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    // Audio quality metrics endpoint
    fastify.get('/api/audio-quality', async (request, reply) => {
        try {
            let qualityMetrics;
            try {
                qualityMetrics = AudioProcessor.audioQualityMonitor?.getSummary();
            } catch (e) {
                qualityMetrics = null;
            }
            
            const audioQuality = qualityMetrics || {
                sampleRate: 16000,
                bitDepth: 16,
                channels: 1,
                format: 'PCM',
                averageLatency: 50,
                packetsProcessed: 0,
                errors: 0
            };
            
            return {
                success: true,
                audioQuality: audioQuality,
                debugMode: process.env.AUDIO_DEBUG === 'true',
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting audio quality metrics:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    // Reset audio quality metrics endpoint
    fastify.post('/api/audio-quality/reset', async (request, reply) => {
        try {
            // Check if audioQualityMonitor exists before calling reset
            if (AudioProcessor.audioQualityMonitor && typeof AudioProcessor.audioQualityMonitor.reset === 'function') {
                AudioProcessor.audioQualityMonitor.reset();
            }
            return {
                success: true,
                message: 'Audio quality metrics reset successfully',
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error resetting audio quality metrics:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    // Campaign script endpoints (UI compatibility)

    // Get campaign script by ID (for UI compatibility)
    fastify.get('/get-campaign-script/:id', async (request, reply) => {
        try {
            const { id } = request.params;

            if (!SecurityUtils.validateScriptId(id)) {
                reply.status(400);
                return { error: 'Invalid script ID' };
            }

            // Handle unified ID system: 1-6 = outbound, 7-12 = incoming
            const numericId = parseInt(id);
            
            // Check if parseInt returned a valid number
            if (isNaN(numericId)) {
                return {
                    success: false,
                    error: 'Invalid campaign ID format. Must be a number.'
                };
            }
            
            let campaignScript = null;

            if (numericId >= 7 && numericId <= 12) {
                // IDs 7-12 are incoming campaigns (map to incoming-campaign1.json to incoming-campaign6.json)
                const incomingCampaignId = numericId - 6; // 7->1, 8->2, ..., 12->6
                campaignScript = loadCampaignScript(incomingCampaignId, 'incoming', false);
                console.log(`✅ Loading incoming campaign ${incomingCampaignId} for ID ${id}`);
            } else if (numericId >= 1 && numericId <= 6) {
                // IDs 1-6 are outbound campaigns (map to campaign1.json to campaign6.json)
                campaignScript = loadCampaignScript(numericId, 'outbound', false);
                console.log(`✅ Loading outbound campaign ${numericId} for ID ${id}`);
            }

            if (campaignScript) {
                // Return the campaign script directly in the format expected by the frontend
                return campaignScript;
            } else {
                reply.status(404);
                return { error: `Campaign script ${id} not found` };
            }
        } catch (error) {
            console.error('❌ Error getting campaign script:', error);
            reply.status(500);
            return { error: error.message };
        }
    });

    // Get incoming campaign script (alternative route for UI compatibility)
    fastify.get('/incoming-campaign/:id', async (request, reply) => {
        try {
            const id = request.params.id.replace('.json', ''); // Remove .json if present
            console.log('📥 [DEBUG] Incoming campaign script request for ID:', id);

            if (!id) {
                console.log('❌ [DEBUG] No ID provided');
                reply.status(400);
                return { success: false, error: 'Script ID is required' };
            }

            if (!SecurityUtils.validateScriptId(id)) {
                console.log('❌ [DEBUG] Invalid script ID:', id);
                reply.status(400);
                return { success: false, error: 'Invalid script ID' };
            }

            const script = scriptManager.getScriptConfig(id, true);
            console.log('📊 [DEBUG] Script found:', !!script);

            if (script) {
                // Return the script as-is without any conversion
                return script;
            } else {
                console.log('❌ [DEBUG] Script not found for ID:', id);
                reply.status(404);
                return { success: false, error: 'Incoming script not found' };
            }
        } catch (error) {
            console.error('❌ Error getting incoming campaign script:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    // Get incoming campaign script (for UI compatibility)
    fastify.get('/incoming-campaign\\::id.json', async (request, reply) => {
        try {
            const id = request.params.id;
            console.log('📥 [DEBUG] Incoming campaign script request for ID:', id);

            if (!id) {
                console.log('❌ [DEBUG] No ID provided');
                reply.status(400);
                return { success: false, error: 'Script ID is required' };
            }

            if (!SecurityUtils.validateScriptId(id)) {
                console.log('❌ [DEBUG] Invalid script ID:', id);
                reply.status(400);
                return { success: false, error: 'Invalid script ID' };
            }

            const script = scriptManager.getScriptConfig(id, true);
            console.log('📊 [DEBUG] Script found:', !!script);

            if (script) {
                // Convert to the format expected by the frontend - no hardcoded instructions
                const campaignData = {
                    campaign: script.scriptId || id,
                    agentPersona: {
                        name: "AI Assistant",
                        tone: "" // Campaign script should define tone
                    },
                    script: {
                        instructions: script.aiInstructions || "" // Campaign script should provide all instructions
                    },
                    customerData: {
                        handling: "" // Campaign script should define handling
                    },
                    transferData: {
                        protocols: "" // Campaign script should define protocols
                    }
                };

                return campaignData;
            } else {
                console.log('❌ [DEBUG] Script not found for ID:', id);
                reply.status(404);
                return { success: false, error: 'Incoming script not found' };
            }
        } catch (error) {
            console.error('❌ Error getting incoming campaign script:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    // Script management endpoints
    fastify.get('/api/scripts/incoming', async (request, reply) => {
        try {
            const scripts = scriptManager.getIncomingScripts();
            return {
                success: true,
                scripts,
                current: scriptManager.getCurrentIncomingScript(),
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting incoming scripts:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    fastify.get('/api/scripts/outbound', async (request, reply) => {
        try {
            const scripts = scriptManager.getOutboundScripts();
            return {
                success: true,
                scripts,
                current: scriptManager.getCurrentOutboundScript(),
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting outbound scripts:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    fastify.post('/api/scripts/incoming/:scriptId/activate', async (request, reply) => {
        try {
            const { scriptId } = request.params;

            if (!SecurityUtils.validateScriptId(scriptId)) {
                reply.status(400);
                return { success: false, error: 'Invalid script ID' };
            }

            const success = scriptManager.setIncomingScript(scriptId);

            if (success) {
                return {
                    success: true,
                    message: `Incoming script '${scriptId}' activated`,
                    scriptId,
                    timestamp: new Date().toISOString()
                };
            } else {
                reply.status(404);
                return { success: false, error: 'Script not found' };
            }
        } catch (error) {
            console.error('❌ Error activating incoming script:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    fastify.post('/api/scripts/outbound/:scriptId/activate', async (request, reply) => {
        try {
            const { scriptId } = request.params;

            if (!SecurityUtils.validateScriptId(scriptId)) {
                reply.status(400);
                return { success: false, error: 'Invalid script ID' };
            }

            const success = scriptManager.setOutboundScript(scriptId);

            if (success) {
                return {
                    success: true,
                    message: `Outbound script '${scriptId}' activated`,
                    scriptId,
                    timestamp: new Date().toISOString()
                };
            } else {
                reply.status(404);
                return { success: false, error: 'Script not found' };
            }
        } catch (error) {
            console.error('❌ Error activating outbound script:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    // Session lifecycle management endpoints
    fastify.get('/api/sessions/active', async (request, reply) => {
        try {
            const activeSessions = lifecycleManager ? lifecycleManager.getActiveSessions() : [];
            return {
                success: true,
                sessions: activeSessions,
                count: activeSessions.length,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting active sessions:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    fastify.get('/api/sessions/:sessionId/status', async (request, reply) => {
        try {
            const { sessionId } = request.params;

            if (!SecurityUtils.sanitizeCallSid(sessionId) && !sessionId.startsWith('local-')) {
                reply.status(400);
                return { success: false, error: 'Invalid session ID format' };
            }

            const sessionStatus = lifecycleManager ? lifecycleManager.getSessionStatus(sessionId) : { exists: false };
            const recoveryStatus = recoveryManager ? recoveryManager.getRecoveryStatus(sessionId) : null;
            const summaryStatus = summaryManager ? summaryManager.getSummaryStatus(sessionId) : null;

            return {
                success: true,
                sessionId,
                session: sessionStatus,
                recovery: recoveryStatus,
                summary: summaryStatus,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting session status:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    fastify.post('/api/sessions/:sessionId/end', async (request, reply) => {
        try {
            const { sessionId } = request.params;
            const { reason = 'api_request' } = request.body;

            if (!SecurityUtils.sanitizeCallSid(sessionId) && !sessionId.startsWith('local-')) {
                reply.status(400);
                return { success: false, error: 'Invalid session ID format' };
            }

            const connectionData = activeConnections.get(sessionId);
            if (!connectionData) {
                reply.status(404);
                return { success: false, error: 'Session not found' };
            }

            if (lifecycleManager) {
                const success = await lifecycleManager.requestSessionEnd(sessionId, connectionData, reason);

                if (success) {
                    activeConnections.delete(sessionId);
                    return {
                        success: true,
                        message: 'Session end requested successfully',
                        sessionId,
                        reason,
                        timestamp: new Date().toISOString()
                    };
                } else {
                    reply.status(500);
                    return { success: false, error: 'Failed to end session' };
                }
            } else {
                reply.status(500);
                return { success: false, error: 'Lifecycle manager not available' };
            }
        } catch (error) {
            console.error('❌ Error ending session:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    fastify.post('/api/sessions/:sessionId/summary', async (request, reply) => {
        try {
            const { sessionId } = request.params;

            if (!SecurityUtils.sanitizeCallSid(sessionId) && !sessionId.startsWith('local-')) {
                reply.status(400);
                return { success: false, error: 'Invalid session ID format' };
            }

            const connectionData = activeConnections.get(sessionId);
            if (!connectionData) {
                reply.status(404);
                return { success: false, error: 'Session not found' };
            }

            if (summaryManager) {
                const success = await summaryManager.requestSummary(sessionId, connectionData, contextManager);

                return {
                    success,
                    message: success ? 'Summary requested successfully' : 'Failed to request summary',
                    sessionId,
                    timestamp: new Date().toISOString()
                };
            } else {
                reply.status(500);
                return { success: false, error: 'Summary manager not available' };
            }
        } catch (error) {
            console.error('❌ Error requesting summary:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    // Export nextCallConfig for use by WebSocket handlers
    fastify.decorate('getNextCallConfig', () => nextCallConfig);
    fastify.decorate('setNextCallConfig', (config) => {
        nextCallConfig = { ...nextCallConfig, ...config };
    });

    // Call Management Endpoints (UI compatibility)

    // Update session configuration
    fastify.post('/update-session-config', async (request, reply) => {
        try {
            const { voice, model, aiInstructions, targetName, targetPhoneNumber } = request.body;

            // Update next call configuration
            if (voice) {
                const validation = voiceManager.validateVoice(voice);
                if (validation.isValid) {
                    nextCallConfig.voice = validation.voice;
                }
            }

            if (model) {
                const validation = modelManager.validateModel(model);
                if (validation.isValid) {
                    nextCallConfig.model = validation.model;
                }
            }

            if (aiInstructions) {
                nextCallConfig.aiInstructions = aiInstructions;
            }

            if (targetName) {
                nextCallConfig.targetName = targetName;
            }

            if (targetPhoneNumber) {
                nextCallConfig.targetPhoneNumber = targetPhoneNumber;
            }

            return {
                status: 'success',
                success: true,
                config: nextCallConfig,
                message: 'Session configuration updated',
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error updating session config:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    // Make outbound call - Real Twilio integration
    fastify.post('/make-call', {
        schema: {
            body: {
                type: 'object',
                required: ['to', 'from'],
                properties: {
                    to: { type: 'string' },
                    from: { type: 'string' },
                    task: { type: 'string' },
                    voice: { type: 'string' },
                    model: { type: 'string' },
                    targetName: { type: 'string' },
                    targetPhoneNumber: { type: 'string' },
                    outputLanguage: { type: 'string' }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const { to, from, task, voice, model, targetName, targetPhoneNumber, outputLanguage } = request.body;

            // Input validation and sanitization
            const sanitizedTo = SecurityUtils.validatePhoneNumber(to);
            const sanitizedFrom = SecurityUtils.validatePhoneNumber(from);

            if (!sanitizedTo) {
                reply.status(400);
                return { error: 'Invalid "to" phone number format. Use international format (+**********).' };
            }

            if (!sanitizedFrom) {
                reply.status(400);
                return { error: 'Invalid "from" phone number format. Use international format (+**********).' };
            }

            const client = twilio(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN);

            // Get the current outbound script configuration
            let callConfig = { ...nextCallConfig };
            
            // Try to get the active outbound script
            try {
                const currentScript = scriptManager.getCurrentOutboundScript();
                if (currentScript) {
                    const scriptConfig = scriptManager.getScriptConfig(currentScript.id, false);
                    if (scriptConfig) {
                        callConfig = {
                            ...scriptConfig,
                            // Allow overrides from request
                            voice: voice || scriptConfig.voice,
                            model: model || scriptConfig.model,
                            targetName: targetName || scriptConfig.targetName,
                            targetPhoneNumber: targetPhoneNumber || scriptConfig.targetPhoneNumber
                        };
                        console.log(`📋 Using outbound script ${currentScript.id} for call`);
                    }
                }
            } catch (error) {
                console.warn('⚠️ Error getting outbound script:', error);
            }
            
            // If task is provided, use it as a simple override (not recommended - use campaign scripts)
            if (task) {
                console.log("⚠️ Using task parameter as AI instructions (deprecated - use campaign scripts)");
                callConfig.aiInstructions = task;
            }
            
            // Update nextCallConfig with the final configuration
            nextCallConfig = callConfig;

            const call = await client.calls.create({
                to: sanitizedTo,
                from: sanitizedFrom,
                url: `${PUBLIC_URL}/incoming-call`,
                statusCallback: `${PUBLIC_URL}/call-status`,
                statusCallbackEvent: ['initiated', 'ringing', 'answered', 'completed', 'failed', 'canceled', 'no-answer', 'busy'],
                statusCallbackMethod: 'POST',
                record: true,
                recordingStatusCallback: `${PUBLIC_URL}/recording-status`,
                recordingStatusCallbackMethod: 'POST',
                recordingStatusCallbackEvent: ['completed']
            });

            console.log(`📞 [${call.sid}] Outbound call initiated to ${to}`);
            return { callSid: call.sid };

        } catch (error) {
            console.error('❌ Error making call:', error);
            reply.status(500);
            return { error: 'Failed to initiate call', details: error.message };
        }
    });

    // Get call results
    fastify.get('/call-results/:callSid', async (request, reply) => {
        try {
            const { callSid } = request.params;

            if (!SecurityUtils.sanitizeCallSid(callSid)) {
                reply.status(404);
                return { success: false, error: 'Invalid call SID format' };
            }

            // Check if session exists in context manager
            const sessionContext = contextManager.getSessionContext(callSid);
            const sessionStatus = lifecycleManager ? lifecycleManager.getSessionStatus(callSid) : null;

            if (sessionContext || sessionStatus) {
                return {
                    success: true,
                    callSid,
                    status: sessionStatus?.status || 'unknown',
                    context: sessionContext,
                    timestamp: new Date().toISOString()
                };
            } else {
                reply.status(404);
                return {
                    success: false,
                    error: 'Call results not found',
                    callSid
                };
            }
        } catch (error) {
            console.error('❌ Error getting call results:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    // === ADDITIONAL SCRIPT MANAGEMENT ENDPOINTS ===

    // Get all available outbound call scripts
    fastify.get('/api/outbound-scripts', async (request, reply) => {
        try {
            const scripts = scriptManager.getOutboundScripts();
            const currentScript = scriptManager.getCurrentOutboundScript();
            reply.send({
                success: true,
                scripts: scripts,
                currentScript: {
                    id: currentScript?.id || 'default',
                    name: currentScript?.name || 'Default Script',
                    description: currentScript?.description || 'Default outbound script'
                }
            });
        } catch (error) {
            console.error('Error listing outbound scripts:', error);
            reply.status(500).send({ success: false, error: error.message });
        }
    });

    // Backward compatibility endpoint (returns outbound scripts)
    fastify.get('/api/incoming-scripts', async (request, reply) => {
        try {
            const scripts = scriptManager.getOutboundScripts();
            const currentScript = scriptManager.getCurrentOutboundScript();
            reply.send({
                success: true,
                scripts: scripts,
                currentScript: {
                    id: currentScript?.id || 'default',
                    name: currentScript?.name || 'Default Script',
                    description: currentScript?.description || 'Default outbound script'
                }
            });
        } catch (error) {
            console.error('Error listing incoming scripts:', error);
            reply.status(500).send({ success: false, error: error.message });
        }
    });

    // Get current active incoming call script
    fastify.get('/api/incoming-scripts/current', async (request, reply) => {
        try {
            const currentScript = scriptManager.getCurrentIncomingScript();
            reply.send({
                success: true,
                currentScript: currentScript || { id: 'customer-service', name: 'Customer Service', description: 'Default customer service script' }
            });
        } catch (error) {
            console.error('Error getting current incoming script:', error);
            reply.status(500).send({ success: false, error: error.message });
        }
    });

    // Set active incoming call script
    fastify.post('/api/incoming-scripts/set/:scriptId', async (request, reply) => {
        try {
            const { scriptId } = request.params;

            if (!SecurityUtils.validateScriptId(scriptId)) {
                reply.status(400);
                return { success: false, error: 'Invalid script ID' };
            }

            const success = scriptManager.setIncomingScript(scriptId);

            if (success) {
                reply.send({
                    success: true,
                    message: `Incoming script set to: ${scriptId}`,
                    scriptId,
                    timestamp: new Date().toISOString()
                });
            } else {
                reply.status(404).send({
                    success: false,
                    error: 'Script not found'
                });
            }
        } catch (error) {
            console.error('Error setting incoming script:', error);
            reply.status(500).send({ success: false, error: error.message });
        }
    });

    console.log('🔍 [DEBUG] About to register incoming scenarios route...');
    // Get all available incoming scenarios (NEW SYSTEM)
    console.log('🔧 Registering /api/incoming-scenarios route...');
    console.log('🔍 [DEBUG] scriptManager available:', !!scriptManager);
    console.log('🔍 [DEBUG] scriptManager.getIncomingScripts available:', typeof scriptManager?.getIncomingScripts);

    fastify.get('/api/incoming-scenarios', async (request, reply) => {
        try {
            console.log('📥 [DEBUG] /api/incoming-scenarios called');
            console.log('🔍 [DEBUG] scriptManager in route:', !!scriptManager);
            const scenarios = scriptManager.getIncomingScripts();
            console.log('📊 [DEBUG] Found scenarios:', scenarios?.length || 0);
            reply.send({
                success: true,
                scenarios: scenarios.map(script => ({
                    id: script.id,
                    name: script.name,
                    description: script.description,
                    category: script.category || 'support'
                })),
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('❌ Error getting incoming scenarios:', error);
            reply.status(500).send({ success: false, error: error.message });
        }
    });
    console.log('✅ /api/incoming-scenarios route registered successfully');

    // Select active incoming scenario (NEW SYSTEM)
    console.log('🔧 Registering /api/incoming-scenarios/select route...');
    fastify.post('/api/incoming-scenarios/select', async (request, reply) => {
        try {
            const { scenarioId } = request.body;

            if (!scenarioId) {
                reply.status(400);
                return { success: false, error: 'Scenario ID is required' };
            }

            if (!SecurityUtils.validateScriptId(scenarioId)) {
                reply.status(400);
                return { success: false, error: 'Invalid scenario ID' };
            }

            const success = scriptManager.setIncomingScript(scenarioId);

            if (success) {
                reply.send({
                    success: true,
                    message: `Incoming scenario selected: ${scenarioId}`,
                    scenarioId,
                    timestamp: new Date().toISOString()
                });
            } else {
                reply.status(404).send({
                    success: false,
                    error: 'Scenario not found'
                });
            }
        } catch (error) {
            console.error('Error selecting incoming scenario:', error);
            reply.status(500).send({ success: false, error: error.message });
        }
    });
    console.log('✅ /api/incoming-scenarios/select route registered successfully');

    // Configure incoming scenario (UI compatibility)
    console.log('🔧 Registering /api/configure-incoming-scenario route...');
    try {
        fastify.post('/api/configure-incoming-scenario', async (request, reply) => {
            try {
                console.log('📥 [DEBUG] configure-incoming-scenario called with body:', request.body);
                const { scenarioId, name, language, fromNumber, voice, model, country, script, isActive } = request.body;

                // Support both new scenarioId format and legacy format
                const targetScenarioId = scenarioId || name;

                if (!targetScenarioId) {
                    console.log('❌ [DEBUG] Missing scenarioId/name in request');
                    reply.status(400);
                    return { success: false, error: 'Scenario ID or name is required' };
                }

                console.log('🔍 [DEBUG] Using scenarioId:', targetScenarioId);

                if (!SecurityUtils.validateScriptId(targetScenarioId)) {
                    console.log('❌ [DEBUG] Invalid scenarioId:', targetScenarioId);
                    reply.status(400);
                    return { success: false, error: 'Invalid scenario ID' };
                }

                console.log('🔧 [DEBUG] Calling scriptManager.setIncomingScript...');
                const success = scriptManager.setIncomingScript(targetScenarioId);
                console.log('📊 [DEBUG] setIncomingScript result:', success);

                if (success) {
                    const result = {
                        success: true,
                        scenarioId: targetScenarioId,
                        message: `Incoming scenario '${targetScenarioId}' configured`,
                        timestamp: new Date().toISOString()
                    };
                    console.log('✅ [DEBUG] Returning success result:', result);
                    return result;
                } else {
                    console.log('❌ [DEBUG] setIncomingScript failed');
                    reply.status(404);
                    return { success: false, error: 'Scenario not found' };
                }
            } catch (error) {
                console.error('❌ Error configuring incoming scenario:', error);
                reply.status(500);
                return { success: false, error: error.message };
            }
        });
        console.log('✅ /api/configure-incoming-scenario route registered successfully');
    } catch (error) {
        console.error('❌ Error registering /api/configure-incoming-scenario route:', error);
    }

    // Analytics and System Metrics Endpoints

    // Get system analytics
    fastify.get('/api/analytics/system', async (request, reply) => {
        try {
            const analytics = {
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                activeConnections: activeConnections.size,
                sessionStats: {
                    total: contextManager.getContextStats().totalSessions || 0,
                    active: lifecycleManager ? lifecycleManager.getActiveSessions().length : 0,
                    recovered: recoveryManager ? recoveryManager.getRecoveryStats().totalRecovered : 0
                },
                audioStats: {
                    // Get audio processing stats from AudioProcessor
                    qualityMonitor: AudioProcessor.audioQualityMonitor ? AudioProcessor.audioQualityMonitor.getSummary() : null
                },
                voiceModelStats: {
                    availableVoices: Object.keys(voiceManager.getAvailableVoices()).length,
                    currentVoice: voiceManager.getDefaultVoice(),
                    availableModels: Object.keys(modelManager.getAvailableModels()).length,
                    currentModel: modelManager.getCurrentModel()
                },
                timestamp: new Date().toISOString()
            };

            return {
                success: true,
                analytics,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting system analytics:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    // Get audio processing metrics
    fastify.get('/api/analytics/audio', async (request, reply) => {
        try {
            const audioAnalytics = {
                qualityMonitor: AudioProcessor.audioQualityMonitor ? AudioProcessor.audioQualityMonitor.getSummary() : null,
                enhancerStats: AudioProcessor.audioEnhancer ? AudioProcessor.audioEnhancer.getProcessingStats() : null,
                transcriptionHealth: null
            };

            // Get transcription health if available
            try {
                const transcriptionManager = new TranscriptionManager();
                audioAnalytics.transcriptionHealth = await transcriptionManager.healthCheck();
            } catch (error) {
                console.warn('⚠️ Could not get transcription health:', error.message);
            }

            return {
                success: true,
                audioAnalytics,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting audio analytics:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    // Get session analytics
    fastify.get('/api/analytics/sessions', async (request, reply) => {
        try {
            const sessionAnalytics = {
                contextStats: contextManager.getContextStats(),
                lifecycleStats: lifecycleManager ? lifecycleManager.getSessionStats() : null,
                recoveryStats: recoveryManager ? recoveryManager.getRecoveryStats() : null,
                healthScore: healthMonitor ? healthMonitor.getHealthScore() : 100,
                activeConnections: Array.from(activeConnections.keys())
            };

            return {
                success: true,
                sessionAnalytics,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting session analytics:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    // Reset audio processing statistics
    fastify.post('/api/analytics/audio/reset', async (request, reply) => {
        try {
            // Reset audio quality monitor
            if (AudioProcessor.audioQualityMonitor && AudioProcessor.audioQualityMonitor.reset) {
                AudioProcessor.audioQualityMonitor.reset();
            }

            // Reset audio enhancer stats
            if (AudioProcessor.audioEnhancer && AudioProcessor.audioEnhancer.resetProcessingStats) {
                AudioProcessor.audioEnhancer.resetProcessingStats();
            }

            return {
                success: true,
                message: 'Audio processing statistics reset',
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error resetting audio statistics:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    // Get comprehensive system metrics (for monitoring/alerting)
    fastify.get('/api/metrics', async (request, reply) => {
        try {
            const metrics = {
                system: {
                    uptime: process.uptime(),
                    memory: process.memoryUsage(),
                    cpu: process.cpuUsage(),
                    version: process.version,
                    platform: process.platform
                },
                application: {
                    activeConnections: activeConnections.size,
                    healthScore: healthMonitor ? healthMonitor.getHealthScore() : 100,
                    contextStats: contextManager.getContextStats(),
                    nextCallConfig: {
                        voice: nextCallConfig.voice,
                        model: nextCallConfig.model,
                        hasInstructions: !!nextCallConfig.aiInstructions,
                        hasTarget: !!nextCallConfig.targetPhoneNumber
                    }
                },
                services: {
                    sessionManager: sessionManager ? 'available' : 'unavailable',
                    contextManager: contextManager ? 'available' : 'unavailable',
                    healthMonitor: healthMonitor ? 'available' : 'unavailable',
                    recoveryManager: recoveryManager ? 'available' : 'unavailable',
                    lifecycleManager: lifecycleManager ? 'available' : 'unavailable'
                },
                timestamp: new Date().toISOString()
            };

            return metrics;
        } catch (error) {
            console.error('❌ Error getting system metrics:', error);
            reply.status(500);
            return { error: 'Failed to get system metrics', message: error.message };
        }
    });

    // Test route to verify route registration is working
    console.log('🔧 Registering test route...');
    try {
        fastify.get('/api/test', async (request, reply) => {
            console.log('📥 [DEBUG] Test route called');
            return { success: true, message: 'Test route working', timestamp: new Date().toISOString() };
        });
        console.log('✅ Test route registered successfully');
    } catch (error) {
        console.error('❌ Error registering test route:', error);
    }

    console.log('🔍 [DEBUG] Reached end of route registration function');
    // Missing routes for test compatibility
    
    // Incoming scripts endpoints
    fastify.get('/incoming-scripts', async (request, reply) => {
        try {
            const scripts = scriptManager.getOutboundScripts(); // Returns outbound scripts for compatibility
            return scripts.map(script => ({
                id: script.id,
                name: script.name,
                description: script.description,
                systemPrompt: script.aiInstructions,
                campaignScript: script.script
            }));
        } catch (error) {
            console.error('❌ Error getting incoming scripts:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    fastify.get('/incoming-scripts/current', async (request, reply) => {
        try {
            const current = scriptManager.getCurrentOutboundScript(); // Returns outbound script for compatibility
            return {
                id: current?.id || 'default',
                name: current?.name || 'Default Script',
                systemPrompt: current?.aiInstructions || '',
                campaignScript: current?.script || ''
            };
        } catch (error) {
            console.error('❌ Error getting current incoming script:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    // Analytics endpoint
    fastify.get('/analytics', async (request, reply) => {
        try {
            return {
                success: true,
                totalOutboundCalls: 0,
                totalInboundCalls: 0,
                callHistory: [],
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting analytics:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });

    console.log('✅ All API routes registered successfully!');
}

// Helper function to end sessions
function endSessionHelper(sessionId, deps) {
    const { sessionManager, contextManager, activeConnections } = deps;

    try {
        console.log(`🔚 [${sessionId}] Ending session via API`);

        const connectionData = activeConnections.get(sessionId);
        if (connectionData) {
            // Close Gemini session
            if (connectionData.geminiSession) {
                try {
                    connectionData.geminiSession.close();
                } catch (error) {
                    console.warn(`⚠️ [${sessionId}] Error closing Gemini session:`, error);
                }
            }

            // Log summary if available
            if (connectionData.summaryText) {
                console.log(`📋 [${sessionId}] Final Summary: ${connectionData.summaryText}`);
            }

            // Remove from active connections
            activeConnections.delete(sessionId);
        }

        // Clean up context and session manager
        contextManager.clearSessionContext(sessionId);
        sessionManager.cleanupSession(sessionId);

        console.log(`✅ [${sessionId}] Session ended via API`);

    } catch (error) {
        console.error(`❌ [${sessionId}] Error ending session via API:`, error);
    }
}
