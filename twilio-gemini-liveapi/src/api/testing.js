// Testing routes for inbound/outbound call testing
export function registerTestingRoutes(fastify, dependencies) {
    const { sessionManager, contextManager, activeConnections } = dependencies;

    // Test incoming call scenarios
    fastify.post('/api/incoming-test-call', async (request, reply) => {
        try {
            const { 
                scenario, 
                aiInstructions, 
                voice, 
                model, 
                testDuration = 60000 // 1 minute default
            } = request.body;

            const testCallSid = `test-incoming-${Date.now()}`;
            console.log(`🧪 [${testCallSid}] Starting incoming test call`);

            // Configure test call - no hardcoded instructions
            const testConfig = {
                aiInstructions: aiInstructions || scenario || '', // Campaign script should provide all instructions
                voice: voice || dependencies.GEMINI_DEFAULT_VOICE,
                model: model || dependencies.GEMINI_DEFAULT_MODEL,
                isIncomingCall: true,
                isTestCall: true,
                testDuration
            };

            // Store test configuration
            fastify.setNextCallConfig(testConfig);

            // Set timeout to end test
            setTimeout(() => {
                const connectionData = activeConnections.get(testCallSid);
                if (connectionData) {
                    console.log(`⏰ [${testCallSid}] Test call timeout reached`);
                    endTestCall(testCallSid, { sessionManager, contextManager, activeConnections });
                }
            }, testDuration);

            return {
                success: true,
                testCallSid,
                message: 'Incoming test call configured',
                config: testConfig,
                duration: testDuration
            };

        } catch (error) {
            console.error('❌ Error setting up incoming test call:', error);
            return {
                success: false,
                error: error.message
            };
        }
    });

    // Test outbound call scenarios
    fastify.post('/api/outbound-test-call', async (request, reply) => {
        try {
            const { 
                script, 
                aiInstructions, 
                voice, 
                model,
                targetName,
                testDuration = 60000 // 1 minute default
            } = request.body;

            const testCallSid = `test-outbound-${Date.now()}`;
            console.log(`🧪 [${testCallSid}] Starting outbound test call`);

            // Configure test call
            const testConfig = {
                aiInstructions: aiInstructions || script || 'You are making an outbound sales call.',
                voice: voice || dependencies.GEMINI_DEFAULT_VOICE,
                model: model || dependencies.GEMINI_DEFAULT_MODEL,
                targetName: targetName || 'Test Customer',
                isIncomingCall: false,
                isTestCall: true,
                testDuration
            };

            // Store test configuration
            fastify.setNextCallConfig(testConfig);

            // Set timeout to end test
            setTimeout(() => {
                const connectionData = activeConnections.get(testCallSid);
                if (connectionData) {
                    console.log(`⏰ [${testCallSid}] Test call timeout reached`);
                    endTestCall(testCallSid, { sessionManager, contextManager, activeConnections });
                }
            }, testDuration);

            return {
                success: true,
                testCallSid,
                message: 'Outbound test call configured',
                config: testConfig,
                duration: testDuration
            };

        } catch (error) {
            console.error('❌ Error setting up outbound test call:', error);
            return {
                success: false,
                error: error.message
            };
        }
    });

    // Start local audio test session
    fastify.post('/api/start-local-test', async (request, reply) => {
        try {
            const { 
                aiInstructions, 
                voice, 
                model,
                testType = 'general'
            } = request.body;

            const testSessionId = `local-test-${Date.now()}`;
            console.log(`🧪 [${testSessionId}] Starting local audio test`);

            const testConfig = {
                aiInstructions: aiInstructions || 'You are an AI assistant for local testing. Respond naturally to test the audio quality.',
                voice: voice || dependencies.GEMINI_DEFAULT_VOICE,
                model: model || dependencies.GEMINI_DEFAULT_MODEL,
                testType,
                isLocalTest: true
            };

            return {
                success: true,
                testSessionId,
                message: 'Local test session ready',
                config: testConfig,
                websocketUrl: `/local-audio-session`
            };

        } catch (error) {
            console.error('❌ Error setting up local test:', error);
            return {
                success: false,
                error: error.message
            };
        }
    });

    // End test call manually
    fastify.post('/api/end-test/:testId', async (request, reply) => {
        try {
            const { testId } = request.params;
            
            const connectionData = activeConnections.get(testId);
            if (!connectionData) {
                return {
                    success: false,
                    error: 'Test session not found'
                };
            }

            console.log(`🛑 [${testId}] Manually ending test call`);
            
            // Generate summary if conversation exists
            if (connectionData.conversationLog && connectionData.conversationLog.length > 0) {
                await sessionManager.generateSummary(testId, connectionData, dependencies.SUMMARY_GENERATION_PROMPT);
                
                // End after summary timeout
                setTimeout(() => {
                    endTestCall(testId, { sessionManager, contextManager, activeConnections });
                }, 30000);
            } else {
                endTestCall(testId, { sessionManager, contextManager, activeConnections });
            }

            return {
                success: true,
                message: 'Test call ending initiated',
                testId
            };

        } catch (error) {
            console.error('❌ Error ending test call:', error);
            return {
                success: false,
                error: error.message
            };
        }
    });

    // Get test results
    fastify.get('/api/test-results/:testId', async (request, _reply) => {
        try {
            const { testId } = request.params;
            
            try {
                const results = await getTestResultsData(testId, { sessionManager, contextManager, activeConnections });
                
                return {
                    success: true,
                    results
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }

        } catch (error) {
            console.error('❌ Error getting test results:', error);
            return {
                success: false,
                error: error.message
            };
        }
    });

    // List active tests
    fastify.get('/api/active-tests', async (request, reply) => {
        try {
            const activeTests = [];

            for (const [sessionId, connectionData] of activeConnections.entries()) {
                if (sessionId.startsWith('test-') || sessionId.startsWith('local-test-')) {
                    const sessionMetrics = sessionManager.getSessionMetrics(sessionId);
                    
                    activeTests.push({
                        testId: sessionId,
                        testType: connectionData.testType || 'unknown',
                        status: connectionData.isSessionActive ? 'active' : 'inactive',
                        startTime: sessionMetrics?.startTime,
                        duration: sessionMetrics ? Date.now() - sessionMetrics.startTime : null,
                        messagesExchanged: (sessionMetrics?.messagesReceived || 0) + (sessionMetrics?.messagesSent || 0)
                    });
                }
            }

            return {
                success: true,
                activeTests,
                totalActive: activeTests.length
            };

        } catch (error) {
            console.error('❌ Error listing active tests:', error);
            return {
                success: false,
                error: error.message
            };
        }
    });

    // Audio quality test
    fastify.post('/api/audio-quality-test', async (request, reply) => {
        try {
            const { duration = 30000 } = request.body; // 30 seconds default
            
            const testId = `audio-quality-${Date.now()}`;
            console.log(`🔊 [${testId}] Starting audio quality test`);

            // This would typically involve sending test tones and measuring response
            setTimeout(() => {
                console.log(`✅ [${testId}] Audio quality test completed`);
            }, duration);

            return {
                success: true,
                testId,
                message: 'Audio quality test started',
                duration,
                expectedResults: [
                    'Latency measurement',
                    'Audio clarity assessment',
                    'Noise level detection',
                    'Echo cancellation test'
                ]
            };

        } catch (error) {
            console.error('❌ Error starting audio quality test:', error);
            return {
                success: false,
                error: error.message
            };
        }
    });
}

// Helper function to end test calls
function endTestCall(testId, deps) {
    const { sessionManager, contextManager, activeConnections } = deps;
    
    try {
        console.log(`🔚 [${testId}] Ending test call`);
        
        const connectionData = activeConnections.get(testId);
        if (connectionData) {
            // Close Gemini session
            if (connectionData.geminiSession) {
                try {
                    connectionData.geminiSession.close();
                } catch (error) {
                    console.warn(`⚠️ [${testId}] Error closing test Gemini session:`, error);
                }
            }

            // Close WebSocket connections
            if (connectionData.twilioWs && connectionData.twilioWs.readyState === 1) {
                try {
                    connectionData.twilioWs.close();
                } catch (error) {
                    console.warn(`⚠️ [${testId}] Error closing test WebSocket:`, error);
                }
            }

            if (connectionData.localWs && connectionData.localWs.readyState === 1) {
                try {
                    connectionData.localWs.close();
                } catch (error) {
                    console.warn(`⚠️ [${testId}] Error closing local test WebSocket:`, error);
                }
            }

            // Log test summary
            if (connectionData.summaryText) {
                console.log(`📋 [${testId}] Test Summary: ${connectionData.summaryText}`);
            }

            // Remove from active connections
            activeConnections.delete(testId);
        }

        // Clean up context and session manager
        contextManager.clearSessionContext(testId);
        sessionManager.cleanupSession(testId);
        
        console.log(`✅ [${testId}] Test call ended and cleaned up`);

    } catch (error) {
        console.error(`❌ [${testId}] Error ending test call:`, error);
    }
}

// Helper function to get test results data
async function getTestResultsData(testId, deps) {
    const { sessionManager, contextManager, activeConnections } = deps;
    
    const connectionData = activeConnections.get(testId);
    const context = contextManager.getSessionContext(testId);
    const sessionMetrics = sessionManager.getSessionMetrics(testId);

    if (!connectionData && !context) {
        throw new Error('Test session not found');
    }

    return {
        testId,
        status: connectionData ? 'active' : 'completed',
        testType: getTestType(connectionData, context),
        ...getTimingData(sessionMetrics, context),
        ...getConversationData(connectionData, context),
        metrics: getMetricsData(sessionMetrics),
        audioQuality: getAudioQualityData()
    };
}

// Extract test type from available data
function getTestType(connectionData, context) {
    return connectionData?.testType || 
           context?.sessionConfig?.testType || 
           'unknown';
}

// Extract timing information
function getTimingData(sessionMetrics, context) {
    return {
        startTime: sessionMetrics?.startTime || context?.timestamp,
        duration: sessionMetrics ? Date.now() - sessionMetrics.startTime : null
    };
}

// Extract conversation data
function getConversationData(connectionData, context) {
    return {
        summary: connectionData?.summaryText || 
                 context?.conversationState?.summaryText || 
                 '',
        conversationLog: connectionData?.conversationLog || 
                         context?.conversationState?.conversationLog || 
                         []
    };
}

// Extract metrics data
function getMetricsData(sessionMetrics) {
    return {
        messagesReceived: sessionMetrics?.messagesReceived || 0,
        messagesSent: sessionMetrics?.messagesSent || 0,
        recoveryCount: sessionMetrics?.recoveryCount || 0,
        lastActivity: sessionMetrics?.lastActivity
    };
}

// Generate audio quality data (currently simulated)
function getAudioQualityData() {
    return {
        avgLatency: Math.random() * 200 + 100, // Simulated
        audioDropouts: Math.floor(Math.random() * 3),
        qualityScore: Math.random() * 0.3 + 0.7 // 0.7-1.0
    };
}
