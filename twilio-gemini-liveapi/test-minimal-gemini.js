#!/usr/bin/env node

import pkg from '@google/genai';
const { GoogleGenAI, Modality } = pkg;
import dotenv from './src/utils/dotenv-stub.js';

dotenv.config();

console.log('🧪 Testing Minimal Gemini Live API...');

const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

if (!GEMINI_API_KEY) {
    console.error('❌ GEMINI_API_KEY not found in environment');
    process.exit(1);
}

console.log('✅ API Key found, initializing Gemini client...');

const geminiClient = new GoogleGenAI({
    apiKey: GEMINI_API_KEY
});

let messageReceived = false;

async function testGeminiSession() {
    try {
        console.log('🤖 Creating Gemini Live session...');
        
        const session = await geminiClient.live.connect({
            model: 'gemini-2.5-flash-preview-native-audio-dialog',
            callbacks: {
                onopen: () => {
                    console.log('✅ Gemini session opened successfully');
                    
                    // Send a simple text message after a delay
                    setTimeout(() => {
                        console.log('📤 Sending simple text message...');
                        session.sendClientContent({
                            turns: [{
                                role: 'user',
                                parts: [{
                                    text: 'Hello, please say "test response" back to me.'
                                }]
                            }],
                            turnComplete: true
                        });
                    }, 1000);
                },
                
                onmessage: (message) => {
                    messageReceived = true;
                    console.log('🎉 GEMINI RESPONSE RECEIVED!');
                    console.log('📨 Message type:', message?.type || 'unknown');
                    console.log('📨 Message keys:', Object.keys(message || {}).join(', '));
                    console.log('📨 Full message:', JSON.stringify(message, null, 2));
                    
                    // Check for audio response
                    const audio = message?.serverContent?.modelTurn?.parts?.[0]?.inlineData;
                    if (audio) {
                        console.log('🎵 Audio response received!');
                        console.log('   MIME type:', audio.mimeType);
                        console.log('   Data size:', audio.data?.length || 0, 'bytes');
                    }
                    
                    // Check for text response
                    const text = message?.serverContent?.modelTurn?.parts?.[0]?.text;
                    if (text) {
                        console.log('💬 Text response:', text);
                    }
                },
                
                onerror: (error) => {
                    console.error('❌ Gemini session error:', error);
                },
                
                onclose: () => {
                    console.log('🔌 Gemini session closed');
                }
            },
            config: {
                responseModalities: [Modality.AUDIO],
                speechConfig: {
                    voiceConfig: {
                        prebuiltVoiceConfig: {
                            voiceName: 'Kore'
                        }
                    }
                }
            },
            temperature: 1.1,
            topP: 0.95,
            topK: 40,
            maxOutputTokens: 8192,
            responseMimeType: 'text/plain'
        });
        
        console.log('⏳ Waiting for response...');
        
        // Wait for 10 seconds to see if we get a response
        setTimeout(() => {
            console.log('\n📊 Test Results:');
            console.log(`🤖 Gemini Response Received: ${messageReceived ? 'YES' : 'NO'}`);
            
            if (!messageReceived) {
                console.log('\n❌ CRITICAL ISSUE: Gemini is not responding at all');
                console.log('   This suggests:');
                console.log('   1. API key issue');
                console.log('   2. Model configuration problem');
                console.log('   3. Gemini Live API service issue');
                console.log('   4. Network connectivity problem');
            } else {
                console.log('\n🎉 SUCCESS: Gemini is responding!');
            }
            
            // Close session
            try {
                session.close();
            } catch {
                console.log('Session already closed');
            }
            
            process.exit(messageReceived ? 0 : 1);
        }, 10000);
        
    } catch (error) {
        console.error('❌ Error creating Gemini session:', error);
        console.error('Error details:', JSON.stringify(error, null, 2));
        process.exit(1);
    }
}

testGeminiSession();
