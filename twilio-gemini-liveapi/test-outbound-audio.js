#!/usr/bin/env node

/**
 * Comprehensive test for outbound audio streaming
 * Tests the actual audio flow and session persistence
 */

import WebSocket from 'ws';
import fs from 'fs';
import crypto from 'crypto';

const WS_BASE_URL = process.env.WS_BASE_URL || 'ws://localhost:3101';
const TEST_DURATION = 30000; // 30 seconds
const AUDIO_SEND_INTERVAL = 100; // Send audio every 100ms
const HEARTBEAT_INTERVAL = 5000; // Send heartbeat every 5 seconds

class OutboundAudioTester {
    constructor() {
        this.connectionStartTime = null;
        this.sessionStartTime = null;
        this.lastAudioReceived = null;
        this.audioPacketsReceived = 0;
        this.audioPacketsSent = 0;
        this.errors = [];
        this.sessionActive = false;
        this.connectionActive = false;
    }

    generateTestAudio() {
        // Generate 16-bit PCM audio data (silence with some noise)
        const sampleRate = 24000;
        const channels = 1;
        const duration = 0.1; // 100ms
        const samples = Math.floor(sampleRate * duration);
        const buffer = new Int16Array(samples);
        
        // Add some random noise to make it "realistic"
        for (let i = 0; i < samples; i++) {
            buffer[i] = Math.floor((Math.random() - 0.5) * 1000);
        }
        
        return Buffer.from(buffer.buffer).toString('base64');
    }

    async testOutboundAudio() {
        console.log('\n🎵 Starting comprehensive outbound audio test...');
        console.log(`📍 Connecting to: ${WS_BASE_URL}/test-outbound`);
        
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(`${WS_BASE_URL}/test-outbound`);
            let testTimer = null;
            let audioSendTimer = null;
            let heartbeatTimer = null;
            
            const cleanup = () => {
                if (testTimer) clearTimeout(testTimer);
                if (audioSendTimer) clearInterval(audioSendTimer);
                if (heartbeatTimer) clearInterval(heartbeatTimer);
                this.connectionActive = false;
                this.sessionActive = false;
            };

            // Test timeout
            testTimer = setTimeout(() => {
                cleanup();
                this.errors.push('Test timeout - connection lasted less than expected');
                resolve(this.getResults());
            }, TEST_DURATION);

            ws.on('open', () => {
                this.connectionStartTime = Date.now();
                this.connectionActive = true;
                console.log('🔌 WebSocket connected at', new Date().toISOString());
                
                // Send start-session message
                const startMessage = {
                    type: 'start-session',
                    aiInstructions: 'You are testing an outbound sales call. Respond naturally to audio input. When you hear audio, respond with speech.',
                    voice: 'Kore',
                    model: 'gemini-2.5-flash-preview-native-audio-dialog'
                };
                
                console.log('📤 Sending start-session message...');
                ws.send(JSON.stringify(startMessage));
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    console.log(`📨 Received message: ${message.type} at ${new Date().toISOString()}`);
                    
                    if (message.type === 'session-started') {
                        this.sessionStartTime = Date.now();
                        this.sessionActive = true;
                        console.log('✅ Session started successfully');
                        
                        // Send a test text message to trigger AI response
                        setTimeout(() => {
                            if (this.sessionActive && ws.readyState === WebSocket.OPEN) {
                                console.log('💬 Sending text message to trigger AI response...');
                                ws.send(JSON.stringify({
                                    type: 'text-message',
                                    text: 'Hello, this is a test. Please respond with speech.'
                                }));
                            }
                        }, 2000);
                        
                        // Start sending audio data periodically
                        audioSendTimer = setInterval(() => {
                            if (this.sessionActive && ws.readyState === WebSocket.OPEN) {
                                const audioData = this.generateTestAudio();
                                this.audioPacketsSent++;
                                
                                ws.send(JSON.stringify({
                                    type: 'audio-data',
                                    audioData: audioData
                                }));
                                
                                if (this.audioPacketsSent % 20 === 0) {
                                    console.log(`📢 Sent ${this.audioPacketsSent} audio packets`);
                                }
                            }
                        }, AUDIO_SEND_INTERVAL);
                        
                        // Start heartbeat
                        heartbeatTimer = setInterval(() => {
                            if (this.sessionActive && ws.readyState === WebSocket.OPEN) {
                                console.log('💓 Sending heartbeat...');
                                ws.send(JSON.stringify({ type: 'heartbeat' }));
                            }
                        }, HEARTBEAT_INTERVAL);
                    }
                    
                    if (message.type === 'audio') {
                        this.audioPacketsReceived++;
                        this.lastAudioReceived = Date.now();
                        console.log(`🔊 Received audio packet #${this.audioPacketsReceived} (size: ${message.audio?.length || 0})`);
                    }
                    
                    if (message.type === 'session-error') {
                        this.errors.push(`Session error: ${message.error}`);
                        console.error('❌ Session error:', message.error);
                    }
                    
                } catch (error) {
                    this.errors.push(`Message parsing error: ${error.message}`);
                    console.error('❌ Error parsing message:', error);
                }
            });

            ws.on('error', (error) => {
                this.errors.push(`WebSocket error: ${error.message}`);
                console.error('❌ WebSocket error:', error);
                cleanup();
                resolve(this.getResults());
            });

            ws.on('close', (code, reason) => {
                console.log(`🔌 WebSocket closed at ${new Date().toISOString()}`);
                console.log(`   Code: ${code}, Reason: ${reason || 'No reason'}`);
                
                if (code === 1005) {
                    this.errors.push('Connection closed with code 1005 (No Status Received) - likely timing issue');
                }
                
                cleanup();
                resolve(this.getResults());
            });
        });
    }

    getResults() {
        const now = Date.now();
        const connectionDuration = this.connectionStartTime ? now - this.connectionStartTime : 0;
        const sessionDuration = this.sessionStartTime ? now - this.sessionStartTime : 0;
        const timeSinceLastAudio = this.lastAudioReceived ? now - this.lastAudioReceived : null;
        
        return {
            connectionEstablished: !!this.connectionStartTime,
            sessionEstablished: !!this.sessionStartTime,
            connectionDuration,
            sessionDuration,
            audioPacketsSent: this.audioPacketsSent,
            audioPacketsReceived: this.audioPacketsReceived,
            lastAudioReceived: this.lastAudioReceived,
            timeSinceLastAudio,
            errors: this.errors,
            success: this.sessionActive && this.audioPacketsReceived > 0 && this.errors.length === 0
        };
    }

    printResults(results) {
        console.log('\n' + '='.repeat(60));
        console.log('📊 OUTBOUND AUDIO TEST RESULTS');
        console.log('='.repeat(60));
        
        console.log(`🔌 Connection established: ${results.connectionEstablished ? '✅' : '❌'}`);
        console.log(`📱 Session established: ${results.sessionEstablished ? '✅' : '❌'}`);
        console.log(`⏱️  Connection duration: ${results.connectionDuration}ms`);
        console.log(`⏱️  Session duration: ${results.sessionDuration}ms`);
        console.log(`📤 Audio packets sent: ${results.audioPacketsSent}`);
        console.log(`📥 Audio packets received: ${results.audioPacketsReceived}`);
        
        if (results.lastAudioReceived) {
            console.log(`🔊 Last audio received: ${new Date(results.lastAudioReceived).toISOString()}`);
            console.log(`⏰ Time since last audio: ${results.timeSinceLastAudio}ms`);
        } else {
            console.log('🔇 No audio received');
        }
        
        if (results.errors.length > 0) {
            console.log('\n❌ Errors:');
            results.errors.forEach((error, index) => {
                console.log(`   ${index + 1}. ${error}`);
            });
        }
        
        console.log('\n' + '='.repeat(60));
        console.log(`Overall result: ${results.success ? '✅ SUCCESS' : '❌ FAILED'}`);
        console.log('='.repeat(60));
        
        return results;
    }
}

// Run the test
async function main() {
    const tester = new OutboundAudioTester();
    
    try {
        const results = await tester.testOutboundAudio();
        const finalResults = tester.printResults(results);
        
        // Exit with appropriate code
        process.exit(finalResults.success ? 0 : 1);
        
    } catch (error) {
        console.error('🚨 Test failed with exception:', error);
        process.exit(1);
    }
}

if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
}

export default OutboundAudioTester;
