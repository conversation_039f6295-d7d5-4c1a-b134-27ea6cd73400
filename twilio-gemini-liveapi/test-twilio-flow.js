#!/usr/bin/env node

import WebSocket from 'ws';

const wsUrl = 'wss://gemini-api.verduona.com/media-stream';
console.log(`Testing Twilio WebSocket flow to: ${wsUrl}`);

const ws = new WebSocket(wsUrl, {
    headers: {
        'User-Agent': 'Twilio-Test'
    }
});

let streamSid = null;

ws.on('open', () => {
    console.log('✅ WebSocket connected successfully!');
    
    // Simulate Twilio start event
    const startMessage = {
        event: 'start',
        start: {
            streamSid: 'MZ' + Math.random().toString(36).substring(2, 15),
            callSid: 'CA' + Math.random().toString(36).substring(2, 15),
            customParameters: {
                CallSid: 'CA' + Math.random().toString(36).substring(2, 15)
            }
        }
    };
    
    streamSid = startMessage.start.streamSid;
    console.log('📤 Sending start event:', JSON.stringify(startMessage));
    ws.send(JSON.stringify(startMessage));
    
    // Simulate connected event after start
    setTimeout(() => {
        const connectedMessage = {
            event: 'connected',
            protocol: 'Call',
            version: '1.0.0'
        };
        console.log('📤 Sending connected event');
        ws.send(JSON.stringify(connectedMessage));
    }, 100);
    
    // Send a media message after connected
    setTimeout(() => {
        const mediaMessage = {
            event: 'media',
            streamSid: streamSid,
            media: {
                payload: Buffer.from('Hello from test').toString('base64')
            }
        };
        console.log('📤 Sending media event');
        ws.send(JSON.stringify(mediaMessage));
    }, 500);
});

ws.on('message', (data) => {
    console.log('📨 Received:', data.toString());
});

ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error.message);
});

ws.on('close', (code, reason) => {
    console.log(`🔌 WebSocket closed. Code: ${code}, Reason: ${reason}`);
});

// Close after 10 seconds
setTimeout(() => {
    console.log('⏰ Closing connection...');
    ws.close();
}, 10000);