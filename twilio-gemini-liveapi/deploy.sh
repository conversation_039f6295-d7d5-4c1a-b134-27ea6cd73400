#!/bin/bash

# Exit on error
set -e

echo "Starting deployment process..."

# Create necessary directories
mkdir -p letsencrypt

# Build and install frontend
echo "Building frontend..."
cd call-center-frontend
npm install --legacy-peer-deps
npm run build
cd ..

# Create docker network if it doesn't exist
docker network create web || true

# Start the services with Docker Compose
echo "Starting services..."
docker-compose down
docker-compose build --no-cache
docker-compose up -d

echo "Checking service status..."
docker-compose ps

echo "Deployment complete!"
echo "Frontend running on: https://t-oai.jackwolf.dev"
echo "Backend running on: https://t-oai.jackwolf.dev/api"

# Show logs
echo "Showing service logs..."
docker-compose logs --tail=10