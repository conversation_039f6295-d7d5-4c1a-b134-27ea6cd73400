#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Performing health check on services...${NC}"

# Check backend (port 3001)
if curl -s http://localhost:3001 > /dev/null; then
  echo -e "${GREEN}✓ Backend is running on port 3001${NC}"
else
  echo -e "${RED}✗ Backend is NOT running on port 3001${NC}"
fi

# Check frontend (port 3000)
if curl -s http://localhost:3000 > /dev/null; then
  echo -e "${GREEN}✓ Frontend is running on port 3000${NC}"
else
  echo -e "${RED}✗ Frontend is NOT running on port 3000${NC}"
fi

# Check if both services are running
if curl -s http://localhost:3001 > /dev/null && curl -s http://localhost:3000 > /dev/null; then
  echo -e "${GREEN}All services are running correctly!${NC}"
  exit 0
else
  echo -e "${RED}One or more services are not running${NC}"
  echo -e "${YELLOW}To start all services, run: ./start.sh${NC}"
  exit 1
fi