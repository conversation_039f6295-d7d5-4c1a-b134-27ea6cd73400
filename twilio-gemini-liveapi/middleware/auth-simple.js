/**
 * Simplified Authentication Middleware - now relies on nginx auth_request
 * User info is passed via headers from nginx after token validation
 * NO MORE BLOCKING - graceful degradation if auth fails
 */

import { createFastifyAuthMiddleware, logAuthStatus } from '../../shared-auth/auth-utils.js';

/**
 * Simplified authentication middleware using shared auth utilities
 */
export const validateSupabaseAuth = createFastifyAuthMiddleware({
  required: false, // Don't block access - allow graceful degradation
  skipPaths: [
    '/incoming-call',    // Twilio webhook
    '/media-stream',     // Twilio WebSocket
    '/health',           // Health check
    '/static'            // Static files
  ]
});

/**
 * Enhanced auth middleware with logging
 */
export async function validateSupabaseAuthWithLogging(request, reply) {
  // Apply base auth middleware
  await validateSupabaseAuth(request, reply);

  // Log authentication status
  logAuthStatus(request, 'Gemini Demo');
}
