# Gemini Live API Integration Cookbook: Fixing the "API Key Not Valid" Error

## Problem Discovery

When integrating Google's Gemini Live API with a Node.js backend, you may encounter a frustrating issue where the API key appears to be correctly configured, but the Live API session immediately closes with error code `1007: API key not valid. Please pass a valid API key.`

This cookbook documents the root cause and solution for this specific integration issue.

## Symptoms

- ✅ Gemini client initializes successfully
- ✅ API key is correctly loaded from environment variables
- ✅ Direct API key tests work fine
- ❌ Live API sessions immediately close with error 1007
- ❌ Session duration is very short (50-100ms)

## Root Cause Analysis

The issue stems from how the `GoogleGenAI` constructor expects to receive the API key parameter. There are two ways to initialize the client:

### ❌ Incorrect (causes 1007 error):
```javascript
import { GoogleGenAI } from '@google/genai';

const client = new GoogleGenAI(apiKey);  // Passing API key directly as string
```

### ✅ Correct:
```javascript
import { GoogleGenAI } from '@google/genai';

const client = new GoogleGenAI({ apiKey: apiKey });  // Passing API key as object property
```

## The Fix

### Step 1: Locate Your Gemini Client Initialization

Find where your `GoogleGenAI` client is initialized. This is typically in a file like `src/gemini/client.js` or similar.

### Step 2: Update the Constructor Call

**Before:**
```javascript
export function initializeGeminiClient(apiKey) {
    if (!apiKey) {
        logger.error('❌ GEMINI_API_KEY is required');
        return null;
    }

    try {
        const client = new GoogleGenAI(apiKey);  // ❌ Wrong way
        logger.info('🤖 Gemini client initialized successfully');
        return client;
    } catch (error) {
        logger.error('❌ Error initializing Gemini client', { error });
        return null;
    }
}
```

**After:**
```javascript
export function initializeGeminiClient(apiKey) {
    if (!apiKey) {
        logger.error('❌ GEMINI_API_KEY is required');
        return null;
    }

    try {
        const client = new GoogleGenAI({ apiKey: apiKey });  // ✅ Correct way
        logger.info('🤖 Gemini client initialized successfully');
        return client;
    } catch (error) {
        logger.error('❌ Error initializing Gemini client', { error });
        return null;
    }
}
```

### Step 3: Restart Your Application

After making the change, restart your application completely:

```bash
# If using PM2
pm2 restart your-app-name

# If running directly
# Stop the process and start again
node your-app.js
```

## Verification

### Test Script to Verify the Fix

Create a test script to verify your Gemini Live API connection:

```javascript
#!/usr/bin/env node

import { GoogleGenAI } from '@google/genai';
import dotenv from 'dotenv';

dotenv.config();

const API_KEY = process.env.GEMINI_API_KEY;

if (!API_KEY) {
    console.error('❌ GEMINI_API_KEY not found in environment');
    process.exit(1);
}

console.log('🧪 Testing Gemini Live API connection...');

try {
    // Use the correct initialization method
    const genAI = new GoogleGenAI({ apiKey: API_KEY });
    console.log('✅ Gemini client initialized');

    const session = await genAI.live.connect({
        model: 'gemini-2.5-flash-preview-native-audio-dialog',
        callbacks: {
            onopen: () => {
                console.log('✅ Live session opened successfully!');
                console.log('🎉 API key is working correctly!');
                session.close();
                process.exit(0);
            },
            onclose: (event) => {
                if (event.code === 1007) {
                    console.error('❌ Still getting API key error - check your implementation');
                    process.exit(1);
                } else {
                    console.log('✅ Session closed normally');
                    process.exit(0);
                }
            },
            onerror: (error) => {
                console.error('❌ Session error:', error);
                process.exit(1);
            }
        }
    });
} catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
}
```

### Expected Success Output

```
🧪 Testing Gemini Live API connection...
✅ Gemini client initialized
✅ Live session opened successfully!
🎉 API key is working correctly!
```

### Expected Error Output (if not fixed)

```
🧪 Testing Gemini Live API connection...
✅ Gemini client initialized
❌ Still getting API key error - check your implementation
```

## Common Patterns and Examples

### Pattern 1: Configuration-based Initialization

```javascript
// config/gemini.js
import { GoogleGenAI } from '@google/genai';
import { config } from './config.js';

export function createGeminiClient() {
    return new GoogleGenAI({ 
        apiKey: config.auth.gemini.apiKey 
    });
}
```

### Pattern 2: Factory Pattern

```javascript
// services/gemini-factory.js
import { GoogleGenAI } from '@google/genai';

export class GeminiClientFactory {
    static create(apiKey) {
        if (!apiKey) {
            throw new Error('API key is required');
        }
        
        return new GoogleGenAI({ apiKey });
    }
}
```

### Pattern 3: Singleton Pattern

```javascript
// services/gemini-singleton.js
import { GoogleGenAI } from '@google/genai';

class GeminiService {
    constructor() {
        this.client = null;
    }
    
    initialize(apiKey) {
        if (!this.client) {
            this.client = new GoogleGenAI({ apiKey });
        }
        return this.client;
    }
    
    getClient() {
        if (!this.client) {
            throw new Error('Gemini client not initialized');
        }
        return this.client;
    }
}

export const geminiService = new GeminiService();
```

## Debugging Tips

### 1. Add Debug Logging

```javascript
export function initializeGeminiClient(apiKey) {
    console.log('🔍 API Key length:', apiKey?.length);
    console.log('🔍 API Key prefix:', apiKey?.substring(0, 10) + '...');
    
    try {
        const client = new GoogleGenAI({ apiKey });
        console.log('✅ Client created successfully');
        return client;
    } catch (error) {
        console.error('❌ Client creation failed:', error);
        return null;
    }
}
```

### 2. Test API Key Validity

```javascript
// Test if API key works with a simple request first
async function testApiKey(apiKey) {
    try {
        const response = await fetch(
            `https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`
        );
        
        if (response.ok) {
            console.log('✅ API key is valid');
            return true;
        } else {
            console.error('❌ API key validation failed:', response.status);
            return false;
        }
    } catch (error) {
        console.error('❌ API key test error:', error);
        return false;
    }
}
```

### 3. Environment Variable Verification

```javascript
// Verify environment variables are loaded correctly
function verifyEnvironment() {
    const apiKey = process.env.GEMINI_API_KEY;
    
    console.log('🔍 Environment check:');
    console.log('  - NODE_ENV:', process.env.NODE_ENV);
    console.log('  - API Key present:', !!apiKey);
    console.log('  - API Key length:', apiKey?.length || 0);
    console.log('  - API Key format:', apiKey?.startsWith('AIzaSy') ? 'Valid format' : 'Invalid format');
    
    return !!apiKey;
}
```

## Prevention Checklist

- [ ] Always pass API key as object: `{ apiKey: yourKey }`
- [ ] Verify API key format (should start with `AIzaSy`)
- [ ] Test API key with simple HTTP request first
- [ ] Use environment variables, never hardcode keys
- [ ] Add proper error handling and logging
- [ ] Test in both development and production environments
- [ ] Document the correct initialization pattern for your team

## Related Issues

This fix also resolves related issues such as:
- Sessions closing immediately after opening
- "Connection failed" errors with valid API keys
- Inconsistent behavior between direct API calls and Live API sessions
- Authentication errors in production but not in development

## Additional Resources

- [Google Gemini Live API Documentation](https://ai.google.dev/gemini-api/docs/live-api)
- [GoogleGenAI SDK Documentation](https://www.npmjs.com/package/@google/genai)
- [Environment Variable Best Practices](https://12factor.net/config)

---

**Note**: This issue specifically affects the `@google/genai` SDK when used with the Live API. The same pattern should be used consistently throughout your application to avoid similar issues with other Gemini API features.
