#!/usr/bin/env node

import { GoogleGenAI } from '@google/genai';
import dotenv from 'dotenv';
import { config } from './src/config/config.js';
import { initializeGeminiClient } from './src/gemini/client.js';

dotenv.config();

console.log('🧪 Comprehensive Gemini API Key Verification Test\n');
console.log('=' .repeat(60));

const API_KEY = process.env.GEMINI_API_KEY || config.auth.gemini.apiKey;

if (!API_KEY) {
    console.error('❌ GEMINI_API_KEY not found in environment');
    process.exit(1);
}

console.log('📋 Configuration:');
console.log(`   - API Key length: ${API_KEY.length}`);
console.log(`   - API Key prefix: ${API_KEY.substring(0, 10)}...`);
console.log(`   - API Key format: ${API_KEY.startsWith('AIzaSy') ? '✅ Valid format' : '❌ Invalid format'}`);
console.log('=' .repeat(60) + '\n');

// Test 1: Direct API Key Validation
console.log('🔍 Test 1: Direct API Key Validation');
try {
    const response = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models?key=${API_KEY}`
    );
    
    if (response.ok) {
        console.log('✅ API key is valid for direct API calls');
    } else {
        console.error(`❌ API key validation failed: ${response.status} ${response.statusText}`);
        const errorData = await response.text();
        console.error('Error details:', errorData);
    }
} catch (error) {
    console.error('❌ API key test error:', error);
}

console.log('\n' + '=' .repeat(60) + '\n');

// Test 2: Wrong Initialization Method (should fail with 1007)
console.log('🔍 Test 2: Wrong Initialization Method (Expected to fail)');
try {
    const wrongClient = new GoogleGenAI(API_KEY); // ❌ Wrong way
    console.log('⚠️  Client created with wrong method');
    
    const wrongSession = await wrongClient.live.connect({
        model: 'gemini-2.5-flash-preview-native-audio-dialog',
        callbacks: {
            onopen: () => {
                console.log('❌ Unexpected: Session opened with wrong initialization!');
                wrongSession.close();
            },
            onclose: (event) => {
                if (event.code === 1007) {
                    console.log('✅ Expected behavior: Got error 1007 with wrong initialization');
                    console.log(`   Reason: ${event.reason}`);
                } else {
                    console.log(`⚠️  Session closed with code ${event.code}: ${event.reason}`);
                }
            },
            onerror: (error) => {
                console.error('❌ Session error:', error);
            }
        }
    });
    
    // Wait a bit for the session to close
    await new Promise(resolve => setTimeout(resolve, 1000));
} catch (error) {
    console.error('❌ Error with wrong initialization:', error);
}

console.log('\n' + '=' .repeat(60) + '\n');

// Test 3: Correct Initialization Method (should succeed)
console.log('🔍 Test 3: Correct Initialization Method');
try {
    const correctClient = new GoogleGenAI({ apiKey: API_KEY }); // ✅ Correct way
    console.log('✅ Client created with correct method');
    
    let sessionOpened = false;
    const correctSession = await correctClient.live.connect({
        model: 'gemini-2.5-flash-preview-native-audio-dialog',
        callbacks: {
            onopen: () => {
                sessionOpened = true;
                console.log('✅ Session opened successfully with correct initialization!');
                console.log('🎉 API key is working correctly!');
                correctSession.close();
            },
            onclose: (event) => {
                if (event.code === 1007) {
                    console.error('❌ Still getting API key error - implementation needs fixing');
                } else if (sessionOpened) {
                    console.log('✅ Session closed normally');
                }
            },
            onerror: (error) => {
                console.error('❌ Session error:', error);
            }
        }
    });
    
    // Wait a bit for the session to complete
    await new Promise(resolve => setTimeout(resolve, 1000));
} catch (error) {
    console.error('❌ Error with correct initialization:', error);
}

console.log('\n' + '=' .repeat(60) + '\n');

// Test 4: Backend Implementation Test
console.log('🔍 Test 4: Backend Implementation Test (using project\'s initializeGeminiClient)');
const backendClient = initializeGeminiClient(API_KEY);

if (!backendClient) {
    console.error('❌ Failed to initialize backend client');
    process.exit(1);
}

console.log('✅ Backend client initialized');

try {
    let backendSessionOpened = false;
    const backendSession = await backendClient.live.connect({
        model: config.ai.gemini.defaultModel || 'gemini-2.5-flash-preview-native-audio-dialog',
        callbacks: {
            onopen: () => {
                backendSessionOpened = true;
                console.log('✅ Backend session opened successfully!');
                console.log('🎉 Backend implementation is correct!');
                backendSession.close();
            },
            onclose: (event) => {
                if (event.code === 1007) {
                    console.error('❌ Backend implementation has API key error!');
                    console.error('   The initializeGeminiClient function needs to be fixed');
                } else if (backendSessionOpened) {
                    console.log('✅ Backend session closed normally');
                }
            },
            onerror: (error) => {
                console.error('❌ Backend session error:', error);
            }
        }
    });
    
    // Wait for completion
    await new Promise(resolve => setTimeout(resolve, 1000));
} catch (error) {
    console.error('❌ Error with backend implementation:', error);
}

console.log('\n' + '=' .repeat(60));
console.log('🏁 Test Summary:');
console.log('   - If Test 2 failed with 1007 and Test 3 succeeded: Implementation is correct');
console.log('   - If both Test 2 and Test 3 failed with 1007: API key might be invalid');
console.log('   - If Test 4 succeeded: Backend implementation is working correctly');
console.log('=' .repeat(60));

process.exit(0);