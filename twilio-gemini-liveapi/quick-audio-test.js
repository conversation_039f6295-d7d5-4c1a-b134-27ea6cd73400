import WebSocket from 'ws';

console.log('🧪 Quick Audio Test for Twilio Gemini...');

const ws = new WebSocket('ws://localhost:3101/local-audio-session');
let audioReceived = false;

const timeout = setTimeout(() => {
    console.log('❌ Test timeout - no audio received');
    ws.close();
    process.exit(1);
}, 25000);

ws.on('open', () => {
    console.log('✅ Connected to Twilio Gemini backend');
    
    // Start a new testing session
    ws.send(JSON.stringify({
        type: 'start-session',
        voice: 'Kore',
        model: 'gemini-2.5-flash-preview-native-audio-dialog',
        script: 'Quick audio test'
    }));
});

ws.on('message', (data) => {
    try {
        const message = JSON.parse(data.toString());
        console.log(`📨 Received: ${message.type}`);

        if (message.type === 'session-started') {
            console.log('✅ Session started, sending test message...');
            setTimeout(() => {
                ws.send(JSON.stringify({
                    type: 'text-message',
                    text: 'Say hello'
                }));
            }, 1000);
        }

        if (message.type === 'audio') {
            console.log('🎉 SUCCESS: Audio response received!');
            console.log(`   Audio length: ${message.audio ? message.audio.length : 0} chars`);
            audioReceived = true;
            clearTimeout(timeout);
            ws.close();
            console.log('✅ Audio fix is working correctly!');
            process.exit(0);
        }

        if (message.type === 'error') {
            console.error(`❌ Error: ${message.message}`);
        }

    } catch (error) {
        console.error('❌ Parse error:', error);
    }
});

ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error);
    clearTimeout(timeout);
    process.exit(1);
});

ws.on('close', (code, reason) => {
    console.log(`🔌 Connection closed: ${code} - ${reason}`);
    clearTimeout(timeout);
    
    if (!audioReceived) {
        console.log('❌ No audio received - check backend logs');
        process.exit(1);
    }
});
