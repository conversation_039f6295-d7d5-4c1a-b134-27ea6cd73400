#!/usr/bin/env node

import pkg from '@google/genai';
const { GoogleGenAI, Modality } = pkg;
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🧪 Testing Gemini Live API Key...\n');

const API_KEY = process.env.GEMINI_API_KEY;
console.log(`📝 API Key loaded: ${API_KEY ? 'Yes' : 'No'}`);
console.log(`📝 API Key: ${API_KEY}\n`);

if (!API_KEY) {
    console.error('❌ No API key found in environment!');
    process.exit(1);
}

try {
    // Initialize the Gemini client
    const genAI = new GoogleGenAI({ apiKey: API_KEY });
    console.log('✅ Gemini client initialized\n');

    console.log('🔍 Testing Live API connection...');
    
    // Try to connect to the Live API
    const session = await genAI.live.connect({
        model: 'gemini-2.5-flash-preview-native-audio-dialog',
        callbacks: {
            onopen: () => {
                console.log('✅ Live session opened successfully!');
                console.log('✅ API Key is VALID for Live API');
                session.close();
            },
            onclose: (event) => {
                console.log(`🔌 Session closed: ${JSON.stringify(event)}`);
                if (event.code === 1007) {
                    console.error('❌ API Key is INVALID: ' + event.reason);
                }
                process.exit(event.code === 1007 ? 1 : 0);
            },
            onerror: (error) => {
                console.error('❌ Live session error:', error);
                process.exit(1);
            }
        },
        config: {
            responseModalities: [Modality.AUDIO],
            speechConfig: {
                voiceConfig: {
                    prebuiltVoiceConfig: {
                        voiceName: 'Kore'
                    }
                }
            }
        }
    });

    console.log('⏳ Waiting for session to connect...');

} catch (error) {
    console.error('❌ Error:', error.message);
    console.error('\nFull error:', error);
    process.exit(1);
}