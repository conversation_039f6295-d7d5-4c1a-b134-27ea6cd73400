# Codex Agent Guidelines

This repository follows the **Cline Rules** found in `.clinerules`. The key points for Codex are summarized below.

## Required Commands
- `npm run lint:fix` - auto-fix and check code style
- `npm test` - run the built-in Node test runner (all 27+ tests must pass)

These commands must be executed before each commit.

## Development Policies
- **Campaign scripts only**: do not add system prompts. All AI behavior derives from campaign scripts.
- Use ES modules throughout; avoid CommonJS.
- Keep files under 300 lines and apply single-responsibility. Extract utilities to `src/utils/` as needed.
- Use environment variables defined in `src/config/config.js`; never hardcode credentials.
- When processing audio, use `sendBrowserAudioToGemini` for browser audio and `sendAudioToGemini` for Twilio audio.

## Logging Rules
- Use `src/utils/logger.js` for all logging—`console.log` is forbidden.
- Prefix logs with emojis for context (🚀, 📞, 🎤, 💬, ❌, ⚠️, ✅, 🔍).
- Include contextual fields such as `callSid` or `sessionId`.

## Testing Expectations
- Comprehensive tests cover outbound/inbound flows via Twilio and browser.
- Use the Node test runner (`node:test`) and mock utilities from `src/utils/test-utils.js` when needed.

## Git Workflow
- Work on feature branches and merge to `develop` before `main`.
- Include tests in pull requests and ensure version numbers are updated.

Following these guidelines ensures compliance with the project’s Cline Rules and keeps the CI pipeline healthy.
