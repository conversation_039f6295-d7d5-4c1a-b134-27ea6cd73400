#!/usr/bin/env node

/**
 * Test script to verify that the AI is now speaking properly
 * with the real campaign script loaded.
 */

import WebSocket from 'ws';

const WS_URL = 'ws://localhost:3101/local-audio-session';

console.log('🧪 Testing AI speaking with real campaign script...');

const ws = new WebSocket(WS_URL);

ws.on('open', () => {
    console.log('✅ WebSocket connected');
    
    // Send start-session message to trigger session initialization
    console.log('📤 Sending start-session message...');
    ws.send(JSON.stringify({
        type: 'start-session',
        model: 'gemini-2.5-flash-preview-native-audio-dialog',
        voice: 'Kore',
        aiInstructions: 'Test instructions', // This should be overridden by real campaign script
        scriptId: 1
    }));
    
    // Wait for session to start, then send some test audio
    setTimeout(() => {
        console.log('📤 Sending test audio message...');
        ws.send(JSON.stringify({
            type: 'audio-data',
            audioData: 'dGVzdCBhdWRpbyBkYXRh' // base64 encoded "test audio data"
        }));
    }, 3000);
    
    // Close after 15 seconds to allow time for AI response
    setTimeout(() => {
        console.log('🔚 Closing connection...');
        ws.close();
    }, 15000);
});

ws.on('message', (data) => {
    try {
        const message = JSON.parse(data);
        console.log('📨 Received:', message.type);
        
        if (message.type === 'session-started') {
            console.log('✅ Session started successfully');
        } else if (message.type === 'audio') {
            console.log('🔊 AI AUDIO RESPONSE RECEIVED! Length:', message.audioData?.length || 'unknown');
        } else if (message.type === 'audio-response') {
            console.log('🎵 AI AUDIO RESPONSE! Length:', message.audioData?.length || 'unknown');
        } else {
            console.log('📨 Other message:', message.type, Object.keys(message));
        }
    } catch {
        console.log('📨 Received (raw):', data.toString().substring(0, 100));
    }
});

ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error);
});

ws.on('close', (code, reason) => {
    console.log(`🔌 WebSocket closed: ${code} ${reason}`);
    process.exit(0);
});
