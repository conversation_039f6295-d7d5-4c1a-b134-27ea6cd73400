# Conversation‑Context Cookbook

*For developers building an AI coding assistant that chats fluently while staying within model limits*

---

## Table of Contents

1. [About This Cookbook](#about)
2. [Quick Reference of Recipes](#quickref)
3. [Recipes](#recipes)
   - [1 Sliding‑Window Memory](#r1)
   - [2 Summarisation Buffer](#r2)
   - [3 Hybrid — Window + Summary](#r3)
   - [4 Retrieval‑Augmented Memory](#r4)
   - [5 Hierarchical Chunking for Large Docs](#r5)
   - [6 Role Separation & Pinned Instructions](#r6)
   - [7 Privacy Filtering & Redaction](#r7)
   - [8 Token Budgeting & Cost Control](#r8)
   - [9 Streaming Summariser (Live Digest)](#r9)
4. [Additional Tips & Resources](#tips)

---



## About This Cookbook

> **Goal:** give you ready‑to‑cook patterns—code + heuristics—to manage conversational context when calling GPT‑4o, Gemini 1.5, or other large‑language‑models.
>
> **Assumptions:** you already know how to make a basic chat completion call; you want to scale to long coding/help sessions without blowing the context window or your budget.

---



## Quick Reference of Recipes

| # | Pattern               | Best For                    | Code Length | Setup Effort |
| - | --------------------- | --------------------------- | ----------- | ------------ |
| 1 | Sliding‑Window        | ≤ 250 turns, small budget   | 10 lines    | ★☆☆          |
| 2 | Summarisation Buffer  | long tutoring sessions      | 40 lines    | ★★☆          |
| 3 | Hybrid (W+S)          | mixed depth + recency       | 60 lines    | ★★☆          |
| 4 | Retrieval‑Augmented   | semantic jump backs         | 80 lines    | ★★★          |
| 5 | Hierarchical Chunking | giant paste‑ins (codebases) | 90 lines    | ★★★          |
| 6 | Role Separation       | persistent persona          | n/a         | ★☆☆          |
| 7 | Privacy Filtering     | PII/keys in logs            | 20 lines    | ★★☆          |
| 8 | Token Budgeting       | cost caps                   | 25 lines    | ★☆☆          |
| 9 | Streaming Summariser  | real‑time dashboard         | 70 lines    | ★★☆          |

---

&#x20;\# Recipes

---



### 1 · Sliding‑Window Memory

**Problem:** keep only the last *K* tokens so you never exceed the window.

**Ingredients**

- `messages: list[dict]` (role, content)
- `max_request_tokens` – model window minus output allowance
- `tiktoken` or model tokenizer

**Steps**

1. Walk the message list from newest → oldest, accumulating token count.
2. Stop when adding the next message would exceed `max_request_tokens`.
3. Reverse the collected slice (to restore chronological order) and send.

```python
from tiktoken import encoding_for_model

def trim_by_window(messages, max_request_tokens=12000, model="gpt-4o-mini"):
    enc = encoding_for_model(model)
    tokens = 0
    keep = []
    for m in reversed(messages):
        tokens += len(enc.encode(m["content"])) + 4  # cheap sys‑count
        if tokens > max_request_tokens:
            break
        keep.append(m)
    return list(reversed(keep))
```

**Chef’s Notes**

- Fastest, cheapest pattern; no extra calls.
- Loses long‑range context (user might feel “forgetful”).

---



### 2 · Summarisation Buffer

**Problem:** preserve *what happened* even after dropping verbatim chat.

**Ingredients**

- Sliding‑window recipe
- Extra system message: cumulative summary
- Summariser model (can be smaller/cheaper)

**Steps**

1. After each assistant reply, append the new turn to a temporary log.
2. When token budget ≈ threshold, call the summariser:
   > “Summarise the following dialogue in ≤ 150 words…”
3. Replace the verbatim chunk with the summary paragraph.

```python
summary_msg = {"role": "system", "content": cumulative_summary}
messages = [summary_msg] + recent_turns
```

**Chef’s Notes**

- Trust but verify: occasionally re‑summarise to correct drift.
- Surface the summary to the user (“Here’s what I remember so far…”)—great UX touch.

---



### 3 · Hybrid — Window + Summary

Combine Recipes 1 + 2: keep e.g. last 20 turns verbatim **and** a running digest of earlier material.

```text
system: "You are…"
system: <running summary>
<last n turns>
user: <new prompt>
```

---



### 4 · Retrieval‑Augmented Memory (Vector DB)

**Problem:** user may refer back to *anything* said earlier (“Can you revisit that regex you suggested Tuesday?”).

**Ingredients**

- Embedding model (e.g. `text-embedding-3-small`)
- Vector store (e.g. FAISS, PGVector)
- `similarity_search(k=5)`

**Steps**

1. After each message, store `(embedding, metadata, content)`.
2. On each new user prompt:
   ```python
   hits = vstore.similarity_search(prompt, k=5)
   retrieved = [h.meta["content"] for h in hits]
   messages = base + retrieved + sliding_window(messages)
   ```

**Chef’s Notes**

- Keep embeddings lightweight—compress or delete after 30 days if GDPR matters.
- Works best when user asks about *concepts*, not exact wording.

---



### 5 · Hierarchical Chunking for Large Docs

When the user pastes a 10 000‑line codebase:

1. Chunk at file → function → paragraph levels.
2. Embed each chunk.
3. Run a hierarchical RAG: find top files ⇒ then functions ⇒ then lines.
4. Assemble only the hits into context.

---



### 6 · Role Separation & Pinned Instructions

- Put stable persona + rules in **system**.
- Put dynamic summary in **system** but second.
- Keep assistant and user turns clean.

```json
[{"role":"system","content":"You are a rust‑ace AI coding tutor…"},
 {"role":"system","content":"Memory so far: …"},
 …]
```

---



### 7 · Privacy Filtering & Redaction

Regex‑scrub secrets **before** storage or embedding.

```python
import re
def redact(text):
    patterns = [r"sk‑live_[0-9a-zA-Z]{24}", r"-----BEGIN PRIVATE KEY-----[\s\S]+?END PRIVATE KEY-----"]
    for p in patterns:
        text = re.sub(p, "[REDACTED]", text)
    return text
```

---



### 8 · Token Budgeting & Cost Control

- 1 k tokens ≈ US\$0.0015–0.008 (model‑dependent).
- Track monthly spend per user; downgrade to smaller model if spend > quota.
- Provide “Context Size” indicator in UI.

---



### 9 · Streaming Summariser (Live Digest)

- Spin up a background worker that appends each new exchange to a rolling Markdown log.
- When `len(log) % 5 == 0`, summarise and push to memory message.
- Show digest in a sidebar so the user can edit their own memory—crowd‑sourced accuracy!

---



## Additional Tips & Resources

- **Tokenisers:** use OpenAI `tiktoken`, Google `sentencepiece` for offline counts.
- **Eval leaks:** unit‑test that summaries do not leak secrets—generate synthetic turns with fake keys.
- **Open‑source helpers:**
  - *LangChain* `ConversationBufferMemory`, `SummaryMemory`, `VectorStoreRetrieverMemory`.
  - *LlamaIndex* “ChatMemoryBuffer” and “GPTIndexMemoryRetriever”.
- **Further Reading:**
  - OpenAI *Function Calling & Tools* (tool message pattern extends recipes above).
  - Google DeepMind “Memory in LLM Agents” (2024 white‑paper).
  - Anthropic *Claude 3 Prompt Engineering Guide*—excellent section on long‑context strategy.

> **Happy cooking!** May your assistant remember everything **except** the API keys 😉

