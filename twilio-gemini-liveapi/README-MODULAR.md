# Twilio Gemini Live API - Modular Architecture

## 🎯 **Requirements Fulfilled**

✅ **a) Keep session alive** - Sessions persist until manually stopped by user  
✅ **b) Automatic reconnection** - Full conversation context recovery on API drops  
✅ **c) Session summary** - Generated when calls/tests end  
✅ **d) Clean session termination** - Proper cleanup of all resources  

## 📁 **Modular File Structure**

```
twilio-gemini-liveapi/
├── index.js                          # Main server entry point
├── src/
│   ├── gemini/
│   │   └── client.js                 # Gemini client, voices, models
│   ├── session/
│   │   ├── context-manager.js        # Session context & recovery
│   │   └── session-manager.js        # Gemini session management
│   ├── audio/
│   │   └── audio-processor.js        # Audio conversion & enhancement
│   ├── websocket/
│   │   └── handlers.js               # WebSocket handlers (Twilio + Local)
│   ├── api/
│   │   ├── routes.js                 # Core API routes
│   │   ├── management.js             # Scripts & campaign management
│   │   └── testing.js                # Testing routes & scenarios
│   └── middleware/
│       └── auth-simple.js            # Authentication middleware
└── static/                           # Static files for management UIs
```

## 🚀 **All Scenarios Supported**

### **Call Types:**
- ✅ **Inbound calls** - Customer calls in
- ✅ **Outbound calls** - AI calls customer  
- ✅ **Inbound testing** - Test incoming scenarios
- ✅ **Outbound testing** - Test outbound scripts
- ✅ **Local audio testing** - Browser mic/speaker testing

### **Session Management:**
- ✅ **Start call** - Initialize Gemini session
- ✅ **End call** - Generate summary + cleanup
- ✅ **Start test** - Configure test scenarios  
- ✅ **End test** - Test results + cleanup
- ✅ **Session recovery** - Automatic reconnection with context

### **Script Management:**
- ✅ **Campaign scripts** - 12 configurable scripts (1-12)
- ✅ **Incoming scenarios** - Customer service, sales scenarios
- ✅ **Outbound scripts** - Sales, follow-up scripts
- ✅ **Script loading** - Dynamic script configuration

## 🔧 **Key Features**

### **Session Recovery System:**
- **Context Storage** - Full conversation history preserved
- **Interruption Detection** - Automatic API drop detection  
- **Recovery Logic** - Up to 3 attempts within 5 minutes
- **Seamless Continuation** - User unaware of technical issues

### **Audio Processing:**
- **Format Conversion** - Twilio μ-law ↔ Gemini PCM
- **Quality Enhancement** - Noise reduction, de-essing, AGC
- **Upsampling/Downsampling** - 8kHz ↔ 16kHz ↔ 24kHz
- **Real-time Processing** - Low-latency audio pipeline

### **Voice & Model Management:**
- **8 Gemini Voices** - Aoede, Puck, Charon, Kore, Fenrir, Leda, Orus, Zephyr
- **Voice Mapping** - OpenAI voice compatibility
- **2 Gemini Models** - Native audio dialog + Live models
- **Dynamic Configuration** - Runtime voice/model switching

## 🌐 **API Endpoints**

### **Core Endpoints:**
- `GET /` - API information
- `GET /health` - Health check with metrics
- `POST /configure-call` - Configure next call
- `POST /end-session/:callSid` - Manual session termination

### **Voice & Model Management:**
- `GET /available-voices` - List Gemini voices
- `GET /available-models` - List Gemini models  
- `POST /set-voice` - Set default voice
- `POST /set-model` - Set default model

### **Campaign Management:**
- `GET /get-campaign-script/:id` - Get script (1-12)
- `POST /update-campaign-script/:id` - Update script
- `GET /list-campaign-scripts` - List all scripts

### **Testing Endpoints:**
- `POST /api/incoming-test-call` - Test incoming scenarios
- `POST /api/outbound-test-call` - Test outbound scripts
- `POST /api/start-local-test` - Local audio testing
- `GET /api/test-results/:testId` - Get test results

### **Analytics & Monitoring:**
- `GET /api/connection-metrics` - Real-time metrics
- `GET /api/provider-health` - Gemini/Twilio health
- `POST /api/trigger-recovery/:callSid` - Manual recovery

## 🔌 **WebSocket Endpoints**

### **Production:**
- `ws://localhost:3000/media-stream` - Twilio media streams

### **Testing:**
- `ws://localhost:3000/local-audio-session` - Local browser testing
  - Use the following message types for manual sessions:
    `start-session`, `audio-data`, `text-message`, `turn-complete`, `end-session`

## 📊 **Monitoring & Metrics**

### **Session Metrics:**
- Messages sent/received
- Recovery attempts
- Session duration
- Last activity timestamp

### **Audio Quality:**
- Latency measurements
- Audio dropouts
- Quality scores
- Noise levels

### **Context Statistics:**
- Total active contexts
- Recovery success rate
- Oldest/newest contexts
- Cleanup statistics

## 🔄 **Recovery Flow**

1. **Normal Operation** → Context saved continuously
2. **API Drop Detected** → Session marked as interrupted  
3. **Recovery Triggered** → New session created with full context
4. **Context Restored** → Complete conversation history sent
5. **Seamless Continuation** → User unaware of interruption

## 🧪 **Testing Capabilities**

### **Test Types:**
- **Incoming Tests** - Customer service scenarios
- **Outbound Tests** - Sales script testing
- **Local Tests** - Browser audio testing
- **Audio Quality Tests** - Latency & quality measurement

### **Test Results:**
- Conversation logs
- Audio quality metrics
- Session statistics
- Recovery performance

## 🎛️ **Audio Settings**

### **Configurable Options:**
- De-essing (reduce harsh sibilants)
- Noise reduction (noise gate)
- Compression (dynamic range)
- AGC (automatic gain control)

## 📈 **Scalability**

### **Modular Benefits:**
- **Maintainable** - Each module has single responsibility
- **Testable** - Individual components can be unit tested
- **Extensible** - Easy to add new features
- **Debuggable** - Clear separation of concerns

### **Performance:**
- **Memory Efficient** - Context cleanup every 5 minutes
- **Connection Pooling** - Reusable Gemini sessions
- **Error Isolation** - Module failures don't crash system

## 🔧 **Configuration**

### **Environment Variables:**
```bash
GEMINI_API_KEY=your_gemini_key
TWILIO_ACCOUNT_SID=your_twilio_sid  
TWILIO_AUTH_TOKEN=your_twilio_token
PORT=3000
GEMINI_DEFAULT_VOICE=Kore
GEMINI_DEFAULT_MODEL=gemini-2.5-flash-preview-native-audio-dialog
SUMMARY_GENERATION_PROMPT="Report campaign related result and important context or follow up."
```

## 🚀 **Getting Started**

```bash
# Install dependencies
npm install

# Set environment variables
cp .env.example .env
# Edit .env with your credentials

# Start server
node index.js
```

The server will start with all scenarios supported:
- Inbound/outbound calls
- Testing capabilities  
- Session recovery
- Script management
- Audio processing
- Real-time monitoring

**All requirements (a, b, c, d) are fully implemented with complete feature parity to the original system.**
