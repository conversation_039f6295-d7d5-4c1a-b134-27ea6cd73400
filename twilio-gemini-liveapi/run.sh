#!/bin/bash

# Exit on error
set -e

MODE=${1:-"local"}  # Default to local mode if not specified

echo "Starting in $MODE mode..."

function cleanup_ports() {
    echo "Cleaning up ports..."
    for port in 3000 3100; do
        pid=$(lsof -ti:$port) || true
        if [ ! -z "$pid" ]; then
            echo "Killing process on port $port"
            kill -9 $pid || true
        fi
    done
}

function start_backend() {
    echo "Starting backend..."
    npm install --legacy-peer-deps
    pm2 delete twilio-backend 2>/dev/null || true
    pm2 start ecosystem.config.cjs --only twilio-backend
}

function start_frontend() {
    echo "Building and starting frontend..."
    cd call-center-frontend
    # Clean up previous builds
    rm -rf .next
    npm install --legacy-peer-deps
    npm run build
    cd ..
    pm2 delete twilio-frontend 2>/dev/null || true
    pm2 start ecosystem.config.cjs --only twilio-frontend
}

function start_docker() {
    echo "Starting services with Docker..."
    mkdir -p letsencrypt
    docker-compose down || true
    docker-compose build --no-cache
    docker-compose up -d
}

case $MODE in
    "local")
        # Install PM2 if not present
        if ! command -v pm2 &> /dev/null; then
            echo "Installing PM2..."
            sudo npm install -g pm2
        fi

        cleanup_ports
        start_backend
        start_frontend
        
        # Save PM2 configuration
        pm2 save

        echo "Local deployment complete!"
        echo "Frontend: http://localhost:3000"
        echo "Backend: http://localhost:3100"
        ;;

    "docker")
        cleanup_ports
        start_docker
        
        echo "Docker deployment complete!"
        echo "Frontend: https://t-oai.jackwolf.dev"
        echo "Backend: https://t-oai.jackwolf.dev/api"
        ;;

    *)
        echo "Invalid mode. Use 'local' or 'docker'"
        exit 1
        ;;
esac

# Show status
if [ "$MODE" = "local" ]; then
    echo "PM2 Process Status:"
    pm2 list
    echo "Recent Logs:"
    pm2 logs --lines 10 --nostream
else
    echo "Docker Container Status:"
    docker-compose ps
    echo "Recent Logs:"
    docker-compose logs --tail=10
fi