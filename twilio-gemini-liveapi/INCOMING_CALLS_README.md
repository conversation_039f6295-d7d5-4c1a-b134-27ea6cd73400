# 📞 Incoming Call Scripts System

## Overview

Your Twilio Gemini app now supports **both incoming and outgoing calls** with different AI scripts/scenarios for incoming calls. When someone calls your Twilio number, they'll be automatically connected to your Gemini AI assistant using one of the predefined scripts.

## 🎯 How It Works

- **Outbound Calls**: Use your existing web app - works exactly the same as before
- **Incoming Calls**: Automatically answered using the currently selected incoming call script

## 📋 Available Scripts

### 1. **General Customer Service** (<PERSON><PERSON><PERSON>)
- **Character**: Sarah - Professional customer service representative
- **Voice**: <PERSON><PERSON> (Soft alto, empathetic)
- **Use Case**: General inquiries and customer support
- **Greeting**: "Hello! Thank you for calling. My name is <PERSON>, how can I assist you today?"

### 2. **Sales & Lead Generation**
- **Character**: Alex - Enthusiastic sales representative
- **Voice**: <PERSON>uck (Lively, higher tenor - energetic)
- **Use Case**: Converting leads, scheduling demos, qualifying prospects
- **Greeting**: "Hello! Thank you for calling! This is <PERSON>. I'm excited to help you discover how we can solve your challenges. What brought you to call us today?"

### 3. **Technical Support**
- **Character**: Morgan - Technical support specialist
- **Voice**: <PERSON><PERSON> (Deep, warm baritone - authoritative but reassuring)
- **Use Case**: Troubleshooting, technical assistance, step-by-step guidance
- **Greeting**: "Hello, you've reached technical support. This is Morgan. I'm here to help you resolve any technical issues you're experiencing. Can you tell me what problem you're having?"

### 4. **Emergency Response**
- **Character**: Casey - Emergency response coordinator
- **Voice**: Fenrir (Assertive mid-range - confident)
- **Use Case**: Urgent situations requiring immediate attention
- **Greeting**: "Emergency response line, this is Casey. What is your emergency?"

## 🔧 Managing Scripts

### Web Interface
Visit: `http://localhost:3101/scripts` (or `https://gemini-api.verduona.com/scripts`)

- View all available scripts
- See which script is currently active
- Switch between scripts by clicking on them
- Real-time updates

### API Endpoints

#### Get All Scripts
```bash
GET /api/incoming-scripts
```

#### Get Current Active Script
```bash
GET /api/incoming-scripts/current
```

#### Set Active Script
```bash
POST /api/incoming-scripts/set/{scriptId}
```

#### Create Custom Script
```bash
POST /api/incoming-scripts/create
Content-Type: application/json

{
  "id": "custom_script",
  "name": "Custom Script Name",
  "description": "Description of the script",
  "systemPrompt": "Your custom system prompt...",
  "voice": "Kore",
  "greetingAudio": null
}
```

## 🧪 Testing

### Phone Number
Call: **+******************

The AI will automatically answer using the currently selected script.

### Example Test Flow
1. Go to `http://localhost:3101/scripts`
2. Select "Sales & Lead Generation" script
3. Call +****************
4. You'll hear Alex (energetic sales voice) greet you
5. Try different scripts to see the personality changes

## 🔄 Script Selection Examples

### Customer Service Mode
```bash
curl -X POST http://localhost:3101/api/incoming-scripts/set/general
```

### Sales Mode
```bash
curl -X POST http://localhost:3101/api/incoming-scripts/set/sales
```

### Technical Support Mode
```bash
curl -X POST http://localhost:3101/api/incoming-scripts/set/support
```

### Emergency Mode
```bash
curl -X POST http://localhost:3101/api/incoming-scripts/set/emergency
```

## 📝 Call Flow

1. **Incoming Call Received** → Twilio webhook triggers `/incoming-call`
2. **Script Selection** → System uses currently active incoming script
3. **AI Connection** → Gemini Live API initialized with script's prompt and voice
4. **Conversation** → AI responds according to the selected character/scenario
5. **Call Summary** → Automatic summary generated when call ends

## 🎭 Character Personalities

Each script has a distinct AI personality:

- **Sarah (General)**: Professional, helpful, efficient
- **Alex (Sales)**: Enthusiastic, rapport-building, goal-oriented
- **Morgan (Support)**: Patient, methodical, reassuring
- **Casey (Emergency)**: Calm under pressure, direct, authoritative

## 🚫 What Doesn't Change

✅ Your existing outbound calling functionality - **100% unchanged**
✅ Your web app interface - **100% unchanged**
✅ Your campaign scripts and configurations - **100% unchanged**
✅ All existing API endpoints - **100% unchanged**

## 🔧 Custom Scripts

You can create custom scripts for specific scenarios:

### Example: Appointment Booking
```json
{
  "id": "appointments",
  "name": "Appointment Booking",
  "description": "For scheduling appointments and consultations",
  "systemPrompt": "<bio>You are Jamie, a friendly appointment coordinator...</bio>",
  "voice": "Zephyr"
}
```

### Example: Order Status
```json
{
  "id": "orders",
  "name": "Order Status & Tracking",
  "description": "For order inquiries and tracking",
  "systemPrompt": "<bio>You are Sam, an order specialist...</bio>",
  "voice": "Leda"
}
```

## 📊 Monitoring

- All incoming calls generate summaries just like outbound calls
- Call data stored in `/data/{callSid}_info.json`
- Real-time logging shows which script is being used
- Web interface shows current active script

## 🎉 Ready to Use!

Your system now supports both directions:
- **📤 Outbound**: Your web app calls customers (existing functionality)
- **📞 Incoming**: Customers can call you and get AI assistance (new functionality)

Call **+****************** to test it right now! 🚀 