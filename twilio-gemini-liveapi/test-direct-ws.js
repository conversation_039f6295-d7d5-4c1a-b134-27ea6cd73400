#!/usr/bin/env node

import WebSocket from 'ws';

// Test both direct backend and nginx proxy
const tests = [
    {
        name: 'Direct Backend',
        url: 'ws://localhost:3101/media-stream'
    },
    {
        name: 'Through Nginx',
        url: 'wss://gemini-api.verduona.com/media-stream'
    }
];

async function testWebSocket(test) {
    console.log(`\n=== Testing ${test.name} ===`);
    console.log(`URL: ${test.url}`);
    
    return new Promise((resolve) => {
        const ws = new WebSocket(test.url, {
            rejectUnauthorized: false // For testing
        });
        
        let connected = false;
        
        ws.on('open', () => {
            console.log('✅ Connected successfully');
            connected = true;
            
            // Send Twilio start message
            const startMsg = {
                event: 'start',
                start: {
                    streamSid: 'MZ' + Date.now(),
                    callSid: 'CA' + Date.now()
                }
            };
            console.log('📤 Sending:', JSON.stringify(startMsg));
            ws.send(JSON.stringify(startMsg));
            
            setTimeout(() => {
                ws.close();
                resolve(true);
            }, 2000);
        });
        
        ws.on('message', (data) => {
            console.log('📨 Received:', data.toString().substring(0, 100));
        });
        
        ws.on('error', (error) => {
            console.error('❌ Error:', error.message);
            resolve(false);
        });
        
        ws.on('close', (code, reason) => {
            console.log(`🔌 Closed. Code: ${code}, Reason: ${reason}`);
            if (!connected) {
                resolve(false);
            }
        });
        
        // Timeout
        setTimeout(() => {
            if (!connected) {
                console.error('⏰ Connection timeout');
                ws.close();
                resolve(false);
            }
        }, 5000);
    });
}

// Run tests
(async () => {
    for (const test of tests) {
        await testWebSocket(test);
    }
})();