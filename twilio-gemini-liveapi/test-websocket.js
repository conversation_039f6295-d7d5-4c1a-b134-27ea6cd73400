#!/usr/bin/env node

import WebSocket from 'ws';

const wsUrl = 'wss://gemini-api.verduona.com/media-stream';
console.log(`Testing WebSocket connection to: ${wsUrl}`);

const ws = new WebSocket(wsUrl);

ws.on('open', () => {
    console.log('✅ WebSocket connected successfully!');
    
    // Simulate Twilio start event
    ws.send(JSON.stringify({
        event: 'start',
        start: {
            streamSid: 'test-stream',
            callSid: 'test-call-' + Date.now()
        }
    }));
});

ws.on('message', (data) => {
    console.log('📨 Received:', data.toString());
});

ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error.message);
});

ws.on('close', (code, reason) => {
    console.log(`🔌 WebSocket closed. Code: ${code}, Reason: ${reason}`);
});

// Close after 5 seconds
setTimeout(() => {
    console.log('⏰ Closing connection...');
    ws.close();
}, 5000);