[TAILING] Tailing last 1000 lines for [twilio-gemini-backend] process (change the value with --lines option)
/home/<USER>/.pm2/logs/twilio-gemini-backend-out.log last 1000 lines:
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded outbound campaign 2 (test: false)
69|twilio- | ✅ Loading outbound campaign 2 for ID 2
69|twilio- | {"timestamp":"2025-07-13T16:43:46.485Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.486Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.486Z","level":"INFO","message":"🎤 Mapped voice 'energetic' → 'Puck' (Male, Lively, energetic tenor with higher pitch)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.486Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.486Z","level":"INFO","message":"🎤 Mapped voice 'energetic' → 'Puck' (Male, Lively, energetic tenor with higher pitch)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.486Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.487Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.487Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:43:46.493Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:43:46.543Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | 📋 Using outbound script 1 for call
69|twilio- | ⚠️ Using task parameter as AI instructions (deprecated - use campaign scripts)
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:43:46.563Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | 📋 Using outbound script 1 for call
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded incoming campaign 1 (test: false)
69|twilio- | ✅ Loading incoming campaign 1 for ID 7
69|twilio- | 🔌 WebSocket connection received on /media-stream
69|twilio- | 🔌 WebSocket headers: {
69|twilio- |   'sec-websocket-version': '13',
69|twilio- |   'sec-websocket-key': 'JNDUzY1nyPSxvAwVE/LZ8w==',
69|twilio- |   connection: 'Upgrade',
69|twilio- |   upgrade: 'websocket',
69|twilio- |   'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits',
69|twilio- |   host: 'localhost:3101'
69|twilio- | }
69|twilio- | 📞 [OUTBOUND] Client connected for outbound call (test: false)
69|twilio- | 🔌 [OUTBOUND_CALL] Client connected
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:43:46.927Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ [OUTBOUND] Using current outbound script: 1
69|twilio- | 📞 [pending] Flow: outbound_call, Incoming: false
69|twilio- | 🤖 [pending] Using model: gemini-2.5-flash-preview-native-audio-dialog, voice: Kore
69|twilio- | 🔗 [GEMINI-LIVE] Direct Gemini Live WebSocket connection
69|twilio- | 🧪 [OUTBOUND TEST] Client connected for outbound testing
69|twilio- | {"timestamp":"2025-07-13T16:43:46.936Z","level":"INFO","message":"OUTBOUND_TEST testing session started","component":"WEBSOCKET","callSid":null,"sessionId":"outbound_test-1752425026936-u26qmv740"}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.938Z","level":"INFO","message":"OUTBOUND_TEST testing connection closed","component":"WEBSOCKET","callSid":null,"sessionId":"outbound_test-1752425026936-u26qmv740","code":1005,"reason":{"type":"Buffer","data":[]}}
69|twilio- | 🔚 [outbound_test-1752425026936-u26qmv740] Ending session via lifecycle manager (reason: connection_closed)
69|twilio- | ✅ Loaded incoming campaign 2 (test: false)
69|twilio- | ✅ Loading incoming campaign 2 for ID 8
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded outbound campaign 2 (test: false)
69|twilio- | ✅ Loading outbound campaign 2 for ID 2
69|twilio- | ✅ Loaded outbound campaign 3 (test: false)
69|twilio- | ✅ Loading outbound campaign 3 for ID 3
69|twilio- | ✅ Loaded outbound campaign 4 (test: false)
69|twilio- | ✅ Loading outbound campaign 4 for ID 4
69|twilio- | ✅ Loaded outbound campaign 5 (test: false)
69|twilio- | ✅ Loading outbound campaign 5 for ID 5
69|twilio- | ✅ Loaded outbound campaign 6 (test: false)
69|twilio- | ✅ Loading outbound campaign 6 for ID 6
69|twilio- | ✅ Loaded incoming campaign 1 (test: false)
69|twilio- | ✅ Loading incoming campaign 1 for ID 7
69|twilio- | ✅ Loaded incoming campaign 2 (test: false)
69|twilio- | ✅ Loading incoming campaign 2 for ID 8
69|twilio- | ✅ Loaded incoming campaign 3 (test: false)
69|twilio- | ✅ Loading incoming campaign 3 for ID 9
69|twilio- | ✅ Loaded incoming campaign 4 (test: false)
69|twilio- | ✅ Loading incoming campaign 4 for ID 10
69|twilio- | ✅ Loaded incoming campaign 5 (test: false)
69|twilio- | ✅ Loading incoming campaign 5 for ID 11
69|twilio- | ✅ Loaded incoming campaign 6 (test: false)
69|twilio- | ✅ Loading incoming campaign 6 for ID 12
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded outbound campaign 2 (test: false)
69|twilio- | ✅ Loading outbound campaign 2 for ID 2
69|twilio- | {"timestamp":"2025-07-13T16:44:38.148Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.148Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.148Z","level":"INFO","message":"🎤 Mapped voice 'energetic' → 'Puck' (Male, Lively, energetic tenor with higher pitch)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.148Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.148Z","level":"INFO","message":"🎤 Mapped voice 'energetic' → 'Puck' (Male, Lively, energetic tenor with higher pitch)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.148Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.149Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.149Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:44:38.155Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:44:38.181Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | 📋 Using outbound script 1 for call
69|twilio- | ⚠️ Using task parameter as AI instructions (deprecated - use campaign scripts)
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:44:38.217Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | 📋 Using outbound script 1 for call
69|twilio- | 🔌 WebSocket connection received on /media-stream
69|twilio- | 🔌 WebSocket headers: {
69|twilio- |   'sec-websocket-version': '13',
69|twilio- |   'sec-websocket-key': 'D44lQJ36J+nV9NB/m//MQg==',
69|twilio- |   connection: 'Upgrade',
69|twilio- |   upgrade: 'websocket',
69|twilio- |   'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits',
69|twilio- |   host: 'localhost:3101'
69|twilio- | }
69|twilio- | 📞 [OUTBOUND] Client connected for outbound call (test: false)
69|twilio- | 🔌 [OUTBOUND_CALL] Client connected
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:44:38.347Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ [OUTBOUND] Using current outbound script: 1
69|twilio- | 📞 [pending] Flow: outbound_call, Incoming: false
69|twilio- | 🤖 [pending] Using model: gemini-2.5-flash-preview-native-audio-dialog, voice: Kore
69|twilio- | 🔗 [GEMINI-LIVE] Direct Gemini Live WebSocket connection
69|twilio- | 🧪 [OUTBOUND TEST] Client connected for outbound testing
69|twilio- | {"timestamp":"2025-07-13T16:44:38.350Z","level":"INFO","message":"OUTBOUND_TEST testing session started","component":"WEBSOCKET","callSid":null,"sessionId":"outbound_test-1752425078350-wnojw4ztm"}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.352Z","level":"INFO","message":"OUTBOUND_TEST testing connection closed","component":"WEBSOCKET","callSid":null,"sessionId":"outbound_test-1752425078350-wnojw4ztm","code":1005,"reason":{"type":"Buffer","data":[]}}
69|twilio- | 🔚 [outbound_test-1752425078350-wnojw4ztm] Ending session via lifecycle manager (reason: connection_closed)
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded incoming campaign 1 (test: false)
69|twilio- | ✅ Loading incoming campaign 1 for ID 7
69|twilio- | ✅ Loaded incoming campaign 2 (test: false)
69|twilio- | ✅ Loading incoming campaign 2 for ID 8
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded outbound campaign 2 (test: false)
69|twilio- | ✅ Loading outbound campaign 2 for ID 2
69|twilio- | ✅ Loaded outbound campaign 3 (test: false)
69|twilio- | ✅ Loading outbound campaign 3 for ID 3
69|twilio- | ✅ Loaded outbound campaign 4 (test: false)
69|twilio- | ✅ Loading outbound campaign 4 for ID 4
69|twilio- | ✅ Loaded outbound campaign 5 (test: false)
69|twilio- | ✅ Loading outbound campaign 5 for ID 5
69|twilio- | ✅ Loaded outbound campaign 6 (test: false)
69|twilio- | ✅ Loading outbound campaign 6 for ID 6
69|twilio- | ✅ Loaded incoming campaign 1 (test: false)
69|twilio- | ✅ Loading incoming campaign 1 for ID 7
69|twilio- | ✅ Loaded incoming campaign 2 (test: false)
69|twilio- | ✅ Loading incoming campaign 2 for ID 8
69|twilio- | ✅ Loaded incoming campaign 3 (test: false)
69|twilio- | ✅ Loading incoming campaign 3 for ID 9
69|twilio- | ✅ Loaded incoming campaign 4 (test: false)
69|twilio- | ✅ Loading incoming campaign 4 for ID 10
69|twilio- | ✅ Loaded incoming campaign 5 (test: false)
69|twilio- | ✅ Loading incoming campaign 5 for ID 11
69|twilio- | ✅ Loaded incoming campaign 6 (test: false)
69|twilio- | ✅ Loading incoming campaign 6 for ID 12
69|twilio- | ✅ Loaded incoming campaign 1 (test: false)
69|twilio- | ✅ Loading incoming campaign 1 for ID 7
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded outbound campaign 2 (test: false)
69|twilio- | ✅ Loading outbound campaign 2 for ID 2
69|twilio- | {"timestamp":"2025-07-13T16:47:00.752Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.752Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.752Z","level":"INFO","message":"🎤 Mapped voice 'energetic' → 'Puck' (Male, Lively, energetic tenor with higher pitch)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.752Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.752Z","level":"INFO","message":"🎤 Mapped voice 'energetic' → 'Puck' (Male, Lively, energetic tenor with higher pitch)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.752Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.752Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.752Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:47:00.759Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:47:00.826Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | 📋 Using outbound script 1 for call
69|twilio- | ⚠️ Using task parameter as AI instructions (deprecated - use campaign scripts)
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:47:00.842Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | 📋 Using outbound script 1 for call
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | 🔌 WebSocket connection received on /media-stream
69|twilio- | 🔌 WebSocket headers: {
69|twilio- |   'sec-websocket-version': '13',
69|twilio- |   'sec-websocket-key': 'mnVGdzydFLbaMli5BiEG7Q==',
69|twilio- |   connection: 'Upgrade',
69|twilio- |   upgrade: 'websocket',
69|twilio- |   'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits',
69|twilio- |   host: 'localhost:3101'
69|twilio- | }
69|twilio- | 📞 [OUTBOUND] Client connected for outbound call (test: false)
69|twilio- | 🔌 [OUTBOUND_CALL] Client connected
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:47:01.013Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ [OUTBOUND] Using current outbound script: 1
69|twilio- | 📞 [pending] Flow: outbound_call, Incoming: false
69|twilio- | 🤖 [pending] Using model: gemini-2.5-flash-preview-native-audio-dialog, voice: Kore
69|twilio- | 🔗 [GEMINI-LIVE] Direct Gemini Live WebSocket connection
69|twilio- | 🧪 [OUTBOUND TEST] Client connected for outbound testing
69|twilio- | {"timestamp":"2025-07-13T16:47:01.017Z","level":"INFO","message":"OUTBOUND_TEST testing session started","component":"WEBSOCKET","callSid":null,"sessionId":"outbound_test-1752425221017-qrqag2kq9"}
69|twilio- | {"timestamp":"2025-07-13T16:47:01.019Z","level":"INFO","message":"OUTBOUND_TEST testing connection closed","component":"WEBSOCKET","callSid":null,"sessionId":"outbound_test-1752425221017-qrqag2kq9","code":1005,"reason":{"type":"Buffer","data":[]}}
69|twilio- | 🔚 [outbound_test-1752425221017-qrqag2kq9] Ending session via lifecycle manager (reason: connection_closed)
69|twilio- | ✅ Loaded incoming campaign 1 (test: false)
69|twilio- | ✅ Loading incoming campaign 1 for ID 7
69|twilio- | ✅ Loaded incoming campaign 2 (test: false)
69|twilio- | ✅ Loading incoming campaign 2 for ID 8
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded outbound campaign 2 (test: false)
69|twilio- | ✅ Loading outbound campaign 2 for ID 2
69|twilio- | ✅ Loaded outbound campaign 3 (test: false)
69|twilio- | ✅ Loading outbound campaign 3 for ID 3
69|twilio- | ✅ Loaded outbound campaign 4 (test: false)
69|twilio- | ✅ Loading outbound campaign 4 for ID 4
69|twilio- | ✅ Loaded outbound campaign 5 (test: false)
69|twilio- | ✅ Loading outbound campaign 5 for ID 5
69|twilio- | ✅ Loaded outbound campaign 6 (test: false)
69|twilio- | ✅ Loading outbound campaign 6 for ID 6
69|twilio- | ✅ Loaded incoming campaign 1 (test: false)
69|twilio- | ✅ Loading incoming campaign 1 for ID 7
69|twilio- | ✅ Loaded incoming campaign 2 (test: false)
69|twilio- | ✅ Loading incoming campaign 2 for ID 8
69|twilio- | ✅ Loaded incoming campaign 3 (test: false)
69|twilio- | ✅ Loading incoming campaign 3 for ID 9
69|twilio- | ✅ Loaded incoming campaign 4 (test: false)
69|twilio- | ✅ Loading incoming campaign 4 for ID 10
69|twilio- | ✅ Loaded incoming campaign 5 (test: false)
69|twilio- | ✅ Loading incoming campaign 5 for ID 11
69|twilio- | ✅ Loaded incoming campaign 6 (test: false)
69|twilio- | ✅ Loading incoming campaign 6 for ID 12
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded outbound campaign 2 (test: false)
69|twilio- | ✅ Loading outbound campaign 2 for ID 2
69|twilio- | {"timestamp":"2025-07-13T16:47:27.402Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.402Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.402Z","level":"INFO","message":"🎤 Mapped voice 'energetic' → 'Puck' (Male, Lively, energetic tenor with higher pitch)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.403Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.403Z","level":"INFO","message":"🎤 Mapped voice 'energetic' → 'Puck' (Male, Lively, energetic tenor with higher pitch)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.403Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.403Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.404Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:47:27.414Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:47:27.431Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | 📋 Using outbound script 1 for call
69|twilio- | ⚠️ Using task parameter as AI instructions (deprecated - use campaign scripts)
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:47:27.509Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | 📋 Using outbound script 1 for call
69|twilio- | 🔌 WebSocket connection received on /media-stream
69|twilio- | 🔌 WebSocket headers: {
69|twilio- |   'sec-websocket-version': '13',
69|twilio- |   'sec-websocket-key': 'gkhAEJigkY5el5RIxyv6TA==',
69|twilio- |   connection: 'Upgrade',
69|twilio- |   upgrade: 'websocket',
69|twilio- |   'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits',
69|twilio- |   host: 'localhost:3101'
69|twilio- | }
69|twilio- | 📞 [OUTBOUND] Client connected for outbound call (test: false)
69|twilio- | 🔌 [OUTBOUND_CALL] Client connected
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:47:27.619Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ [OUTBOUND] Using current outbound script: 1
69|twilio- | 📞 [pending] Flow: outbound_call, Incoming: false
69|twilio- | 🤖 [pending] Using model: gemini-2.5-flash-preview-native-audio-dialog, voice: Kore
69|twilio- | 🔗 [GEMINI-LIVE] Direct Gemini Live WebSocket connection
69|twilio- | 🧪 [OUTBOUND TEST] Client connected for outbound testing
69|twilio- | {"timestamp":"2025-07-13T16:47:27.622Z","level":"INFO","message":"OUTBOUND_TEST testing session started","component":"WEBSOCKET","callSid":null,"sessionId":"outbound_test-1752425247622-nuirp1p89"}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.624Z","level":"INFO","message":"OUTBOUND_TEST testing connection closed","component":"WEBSOCKET","callSid":null,"sessionId":"outbound_test-1752425247622-nuirp1p89","code":1005,"reason":{"type":"Buffer","data":[]}}
69|twilio- | 🔚 [outbound_test-1752425247622-nuirp1p89] Ending session via lifecycle manager (reason: connection_closed)
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded incoming campaign 1 (test: false)
69|twilio- | ✅ Loading incoming campaign 1 for ID 7
69|twilio- | ✅ Loaded incoming campaign 2 (test: false)
69|twilio- | ✅ Loading incoming campaign 2 for ID 8
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded outbound campaign 2 (test: false)
69|twilio- | ✅ Loading outbound campaign 2 for ID 2
69|twilio- | ✅ Loaded outbound campaign 3 (test: false)
69|twilio- | ✅ Loading outbound campaign 3 for ID 3
69|twilio- | ✅ Loaded outbound campaign 4 (test: false)
69|twilio- | ✅ Loading outbound campaign 4 for ID 4
69|twilio- | ✅ Loaded outbound campaign 5 (test: false)
69|twilio- | ✅ Loading outbound campaign 5 for ID 5
69|twilio- | ✅ Loaded outbound campaign 6 (test: false)
69|twilio- | ✅ Loading outbound campaign 6 for ID 6
69|twilio- | ✅ Loaded incoming campaign 1 (test: false)
69|twilio- | ✅ Loading incoming campaign 1 for ID 7
69|twilio- | ✅ Loaded incoming campaign 2 (test: false)
69|twilio- | ✅ Loading incoming campaign 2 for ID 8
69|twilio- | ✅ Loaded incoming campaign 3 (test: false)
69|twilio- | ✅ Loading incoming campaign 3 for ID 9
69|twilio- | ✅ Loaded incoming campaign 4 (test: false)
69|twilio- | ✅ Loading incoming campaign 4 for ID 10
69|twilio- | ✅ Loaded incoming campaign 5 (test: false)
69|twilio- | ✅ Loading incoming campaign 5 for ID 11
69|twilio- | ✅ Loaded incoming campaign 6 (test: false)
69|twilio- | ✅ Loading incoming campaign 6 for ID 12
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded outbound campaign 2 (test: false)
69|twilio- | ✅ Loading outbound campaign 2 for ID 2
69|twilio- | {"timestamp":"2025-07-13T16:47:51.059Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.059Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.059Z","level":"INFO","message":"🎤 Mapped voice 'energetic' → 'Puck' (Male, Lively, energetic tenor with higher pitch)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.059Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.059Z","level":"INFO","message":"🎤 Mapped voice 'energetic' → 'Puck' (Male, Lively, energetic tenor with higher pitch)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.059Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.059Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.059Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:47:51.065Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:47:51.081Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:47:51.081Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | 📋 Using outbound script 1 for call
69|twilio- | ⚠️ Using task parameter as AI instructions (deprecated - use campaign scripts)
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:47:51.102Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | 📋 Using outbound script 1 for call
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | 🔌 WebSocket connection received on /media-stream
69|twilio- | 🔌 WebSocket headers: {
69|twilio- |   'sec-websocket-version': '13',
69|twilio- |   'sec-websocket-key': 'FY7pJ9pDpnH9QWO0PhCPbg==',
69|twilio- |   connection: 'Upgrade',
69|twilio- |   upgrade: 'websocket',
69|twilio- |   'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits',
69|twilio- |   host: 'localhost:3101'
69|twilio- | }
69|twilio- | 📞 [OUTBOUND] Client connected for outbound call (test: false)
69|twilio- | 🔌 [OUTBOUND_CALL] Client connected
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:47:51.289Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ [OUTBOUND] Using current outbound script: 1
69|twilio- | 📞 [pending] Flow: outbound_call, Incoming: false
69|twilio- | 🤖 [pending] Using model: gemini-2.5-flash-preview-native-audio-dialog, voice: Kore
69|twilio- | 🔗 [GEMINI-LIVE] Direct Gemini Live WebSocket connection
69|twilio- | 🧪 [OUTBOUND TEST] Client connected for outbound testing
69|twilio- | {"timestamp":"2025-07-13T16:47:51.293Z","level":"INFO","message":"OUTBOUND_TEST testing session started","component":"WEBSOCKET","callSid":null,"sessionId":"outbound_test-1752425271293-dppwmtiew"}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.298Z","level":"INFO","message":"OUTBOUND_TEST testing connection closed","component":"WEBSOCKET","callSid":null,"sessionId":"outbound_test-1752425271293-dppwmtiew","code":1005,"reason":{"type":"Buffer","data":[]}}
69|twilio- | 🔚 [outbound_test-1752425271293-dppwmtiew] Ending session via lifecycle manager (reason: connection_closed)
69|twilio- | ✅ Loaded incoming campaign 2 (test: false)
69|twilio- | ✅ Loading incoming campaign 2 for ID 8
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded outbound campaign 2 (test: false)
69|twilio- | ✅ Loading outbound campaign 2 for ID 2
69|twilio- | ✅ Loaded outbound campaign 3 (test: false)
69|twilio- | ✅ Loading outbound campaign 3 for ID 3
69|twilio- | ✅ Loaded outbound campaign 4 (test: false)
69|twilio- | ✅ Loading outbound campaign 4 for ID 4
69|twilio- | ✅ Loaded outbound campaign 5 (test: false)
69|twilio- | ✅ Loading outbound campaign 5 for ID 5
69|twilio- | ✅ Loaded outbound campaign 6 (test: false)
69|twilio- | ✅ Loading outbound campaign 6 for ID 6
69|twilio- | ✅ Loaded incoming campaign 1 (test: false)
69|twilio- | ✅ Loading incoming campaign 1 for ID 7
69|twilio- | ✅ Loaded incoming campaign 2 (test: false)
69|twilio- | ✅ Loading incoming campaign 2 for ID 8
69|twilio- | ✅ Loaded incoming campaign 3 (test: false)
69|twilio- | ✅ Loading incoming campaign 3 for ID 9
69|twilio- | ✅ Loaded incoming campaign 4 (test: false)
69|twilio- | ✅ Loading incoming campaign 4 for ID 10
69|twilio- | ✅ Loaded incoming campaign 5 (test: false)
69|twilio- | ✅ Loading incoming campaign 5 for ID 11
69|twilio- | ✅ Loaded incoming campaign 6 (test: false)
69|twilio- | ✅ Loading incoming campaign 6 for ID 12
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded outbound campaign 2 (test: false)
69|twilio- | ✅ Loading outbound campaign 2 for ID 2
69|twilio- | {"timestamp":"2025-07-13T16:50:01.567Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.567Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.567Z","level":"INFO","message":"🎤 Mapped voice 'energetic' → 'Puck' (Male, Lively, energetic tenor with higher pitch)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.567Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.567Z","level":"INFO","message":"🎤 Mapped voice 'energetic' → 'Puck' (Male, Lively, energetic tenor with higher pitch)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.568Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.568Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.568Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:50:01.578Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:50:01.596Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | 📋 Using outbound script 1 for call
69|twilio- | ⚠️ Using task parameter as AI instructions (deprecated - use campaign scripts)
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:50:01.626Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | 📋 Using outbound script 1 for call
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | 🔌 WebSocket connection received on /media-stream
69|twilio- | 🔌 WebSocket headers: {
69|twilio- |   'sec-websocket-version': '13',
69|twilio- |   'sec-websocket-key': 'qJvIJl0J1ezhw6rjgJIO7w==',
69|twilio- |   connection: 'Upgrade',
69|twilio- |   upgrade: 'websocket',
69|twilio- |   'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits',
69|twilio- |   host: 'localhost:3101'
69|twilio- | }
69|twilio- | 📞 [OUTBOUND] Client connected for outbound call (test: false)
69|twilio- | 🔌 [OUTBOUND_CALL] Client connected
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:50:01.807Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ [OUTBOUND] Using current outbound script: 1
69|twilio- | 📞 [pending] Flow: outbound_call, Incoming: false
69|twilio- | 🤖 [pending] Using model: gemini-2.5-flash-preview-native-audio-dialog, voice: Kore
69|twilio- | 🔗 [GEMINI-LIVE] Direct Gemini Live WebSocket connection
69|twilio- | 🧪 [OUTBOUND TEST] Client connected for outbound testing
69|twilio- | {"timestamp":"2025-07-13T16:50:01.812Z","level":"INFO","message":"OUTBOUND_TEST testing session started","component":"WEBSOCKET","callSid":null,"sessionId":"outbound_test-1752425401811-bvw3cdepv"}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.813Z","level":"INFO","message":"OUTBOUND_TEST testing connection closed","component":"WEBSOCKET","callSid":null,"sessionId":"outbound_test-1752425401811-bvw3cdepv","code":1005,"reason":{"type":"Buffer","data":[]}}
69|twilio- | 🔚 [outbound_test-1752425401811-bvw3cdepv] Ending session via lifecycle manager (reason: connection_closed)
69|twilio- | ✅ Loaded incoming campaign 1 (test: false)
69|twilio- | ✅ Loading incoming campaign 1 for ID 7
69|twilio- | ✅ Loaded incoming campaign 2 (test: false)
69|twilio- | ✅ Loading incoming campaign 2 for ID 8
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded outbound campaign 2 (test: false)
69|twilio- | ✅ Loading outbound campaign 2 for ID 2
69|twilio- | ✅ Loaded outbound campaign 3 (test: false)
69|twilio- | ✅ Loading outbound campaign 3 for ID 3
69|twilio- | ✅ Loaded outbound campaign 4 (test: false)
69|twilio- | ✅ Loading outbound campaign 4 for ID 4
69|twilio- | ✅ Loaded outbound campaign 5 (test: false)
69|twilio- | ✅ Loading outbound campaign 5 for ID 5
69|twilio- | ✅ Loaded outbound campaign 6 (test: false)
69|twilio- | ✅ Loading outbound campaign 6 for ID 6
69|twilio- | ✅ Loaded incoming campaign 1 (test: false)
69|twilio- | ✅ Loading incoming campaign 1 for ID 7
69|twilio- | ✅ Loaded incoming campaign 2 (test: false)
69|twilio- | ✅ Loading incoming campaign 2 for ID 8
69|twilio- | ✅ Loaded incoming campaign 3 (test: false)
69|twilio- | ✅ Loading incoming campaign 3 for ID 9
69|twilio- | ✅ Loaded incoming campaign 4 (test: false)
69|twilio- | ✅ Loading incoming campaign 4 for ID 10
69|twilio- | ✅ Loaded incoming campaign 5 (test: false)
69|twilio- | ✅ Loading incoming campaign 5 for ID 11
69|twilio- | ✅ Loaded incoming campaign 6 (test: false)
69|twilio- | ✅ Loading incoming campaign 6 for ID 12
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded outbound campaign 2 (test: false)
69|twilio- | ✅ Loading outbound campaign 2 for ID 2
69|twilio- | {"timestamp":"2025-07-13T16:53:09.182Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.183Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.183Z","level":"INFO","message":"🎤 Mapped voice 'energetic' → 'Puck' (Male, Lively, energetic tenor with higher pitch)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.183Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.183Z","level":"INFO","message":"🎤 Mapped voice 'energetic' → 'Puck' (Male, Lively, energetic tenor with higher pitch)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.183Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.183Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.183Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:53:09.192Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:53:09.211Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:53:09.212Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | 📋 Using outbound script 1 for call
69|twilio- | ⚠️ Using task parameter as AI instructions (deprecated - use campaign scripts)
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:53:09.298Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | 📋 Using outbound script 1 for call
69|twilio- | 🔌 WebSocket connection received on /media-stream
69|twilio- | 🔌 WebSocket headers: {
69|twilio- |   'sec-websocket-version': '13',
69|twilio- |   'sec-websocket-key': 'd0rhTUC+h+vgGApSLi6I4A==',
69|twilio- |   connection: 'Upgrade',
69|twilio- |   upgrade: 'websocket',
69|twilio- |   'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits',
69|twilio- |   host: 'localhost:3101'
69|twilio- | }
69|twilio- | 📞 [OUTBOUND] Client connected for outbound call (test: false)
69|twilio- | 🔌 [OUTBOUND_CALL] Client connected
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:53:09.411Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ [OUTBOUND] Using current outbound script: 1
69|twilio- | 📞 [pending] Flow: outbound_call, Incoming: false
69|twilio- | 🤖 [pending] Using model: gemini-2.5-flash-preview-native-audio-dialog, voice: Kore
69|twilio- | 🔗 [GEMINI-LIVE] Direct Gemini Live WebSocket connection
69|twilio- | 🧪 [OUTBOUND TEST] Client connected for outbound testing
69|twilio- | {"timestamp":"2025-07-13T16:53:09.414Z","level":"INFO","message":"OUTBOUND_TEST testing session started","component":"WEBSOCKET","callSid":null,"sessionId":"outbound_test-1752425589414-0fd24383i"}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.415Z","level":"INFO","message":"OUTBOUND_TEST testing connection closed","component":"WEBSOCKET","callSid":null,"sessionId":"outbound_test-1752425589414-0fd24383i","code":1005,"reason":{"type":"Buffer","data":[]}}
69|twilio- | 🔚 [outbound_test-1752425589414-0fd24383i] Ending session via lifecycle manager (reason: connection_closed)
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded incoming campaign 1 (test: false)
69|twilio- | ✅ Loading incoming campaign 1 for ID 7
69|twilio- | ✅ Loaded incoming campaign 2 (test: false)
69|twilio- | ✅ Loading incoming campaign 2 for ID 8
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded outbound campaign 2 (test: false)
69|twilio- | ✅ Loading outbound campaign 2 for ID 2
69|twilio- | ✅ Loaded outbound campaign 3 (test: false)
69|twilio- | ✅ Loading outbound campaign 3 for ID 3
69|twilio- | ✅ Loaded outbound campaign 4 (test: false)
69|twilio- | ✅ Loading outbound campaign 4 for ID 4
69|twilio- | ✅ Loaded outbound campaign 5 (test: false)
69|twilio- | ✅ Loading outbound campaign 5 for ID 5
69|twilio- | ✅ Loaded outbound campaign 6 (test: false)
69|twilio- | ✅ Loading outbound campaign 6 for ID 6
69|twilio- | ✅ Loaded incoming campaign 1 (test: false)
69|twilio- | ✅ Loading incoming campaign 1 for ID 7
69|twilio- | ✅ Loaded incoming campaign 2 (test: false)
69|twilio- | ✅ Loading incoming campaign 2 for ID 8
69|twilio- | ✅ Loaded incoming campaign 3 (test: false)
69|twilio- | ✅ Loading incoming campaign 3 for ID 9
69|twilio- | ✅ Loaded incoming campaign 4 (test: false)
69|twilio- | ✅ Loading incoming campaign 4 for ID 10
69|twilio- | ✅ Loaded incoming campaign 5 (test: false)
69|twilio- | ✅ Loading incoming campaign 5 for ID 11
69|twilio- | ✅ Loaded incoming campaign 6 (test: false)
69|twilio- | ✅ Loading incoming campaign 6 for ID 12
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded outbound campaign 2 (test: false)
69|twilio- | ✅ Loading outbound campaign 2 for ID 2
69|twilio- | {"timestamp":"2025-07-13T16:53:34.730Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.731Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.731Z","level":"INFO","message":"🎤 Mapped voice 'energetic' → 'Puck' (Male, Lively, energetic tenor with higher pitch)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.731Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.731Z","level":"INFO","message":"🎤 Mapped voice 'energetic' → 'Puck' (Male, Lively, energetic tenor with higher pitch)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.731Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.731Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.731Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:53:34.739Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:53:34.777Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | 📋 Using outbound script 1 for call
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:53:34.803Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | 📋 Using outbound script 1 for call
69|twilio- | ⚠️ Using task parameter as AI instructions (deprecated - use campaign scripts)
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | 🔌 WebSocket connection received on /media-stream
69|twilio- | 🔌 WebSocket headers: {
69|twilio- |   'sec-websocket-version': '13',
69|twilio- |   'sec-websocket-key': 'zHR0C6b35/2rcLbtNVfoXg==',
69|twilio- |   connection: 'Upgrade',
69|twilio- |   upgrade: 'websocket',
69|twilio- |   'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits',
69|twilio- |   host: 'localhost:3101'
69|twilio- | }
69|twilio- | 📞 [OUTBOUND] Client connected for outbound call (test: false)
69|twilio- | 🔌 [OUTBOUND_CALL] Client connected
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:53:34.985Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ [OUTBOUND] Using current outbound script: 1
69|twilio- | 📞 [pending] Flow: outbound_call, Incoming: false
69|twilio- | 🤖 [pending] Using model: gemini-2.5-flash-preview-native-audio-dialog, voice: Kore
69|twilio- | 🔗 [GEMINI-LIVE] Direct Gemini Live WebSocket connection
69|twilio- | 🧪 [OUTBOUND TEST] Client connected for outbound testing
69|twilio- | {"timestamp":"2025-07-13T16:53:34.988Z","level":"INFO","message":"OUTBOUND_TEST testing session started","component":"WEBSOCKET","callSid":null,"sessionId":"outbound_test-1752425614988-ou4if551c"}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.992Z","level":"INFO","message":"OUTBOUND_TEST testing connection closed","component":"WEBSOCKET","callSid":null,"sessionId":"outbound_test-1752425614988-ou4if551c","code":1005,"reason":{"type":"Buffer","data":[]}}
69|twilio- | 🔚 [outbound_test-1752425614988-ou4if551c] Ending session via lifecycle manager (reason: connection_closed)
69|twilio- | ✅ Loaded incoming campaign 1 (test: false)
69|twilio- | ✅ Loading incoming campaign 1 for ID 7
69|twilio- | ✅ Loaded incoming campaign 2 (test: false)
69|twilio- | ✅ Loading incoming campaign 2 for ID 8
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded outbound campaign 2 (test: false)
69|twilio- | ✅ Loading outbound campaign 2 for ID 2
69|twilio- | ✅ Loaded outbound campaign 3 (test: false)
69|twilio- | ✅ Loading outbound campaign 3 for ID 3
69|twilio- | ✅ Loaded outbound campaign 4 (test: false)
69|twilio- | ✅ Loading outbound campaign 4 for ID 4
69|twilio- | ✅ Loaded outbound campaign 5 (test: false)
69|twilio- | ✅ Loading outbound campaign 5 for ID 5
69|twilio- | ✅ Loaded outbound campaign 6 (test: false)
69|twilio- | ✅ Loading outbound campaign 6 for ID 6
69|twilio- | ✅ Loaded incoming campaign 1 (test: false)
69|twilio- | ✅ Loading incoming campaign 1 for ID 7
69|twilio- | ✅ Loaded incoming campaign 2 (test: false)
69|twilio- | ✅ Loading incoming campaign 2 for ID 8
69|twilio- | ✅ Loaded incoming campaign 3 (test: false)
69|twilio- | ✅ Loading incoming campaign 3 for ID 9
69|twilio- | ✅ Loaded incoming campaign 4 (test: false)
69|twilio- | ✅ Loading incoming campaign 4 for ID 10
69|twilio- | ✅ Loaded incoming campaign 5 (test: false)
69|twilio- | ✅ Loading incoming campaign 5 for ID 11
69|twilio- | ✅ Loaded incoming campaign 6 (test: false)
69|twilio- | ✅ Loading incoming campaign 6 for ID 12
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded outbound campaign 2 (test: false)
69|twilio- | ✅ Loading outbound campaign 2 for ID 2
69|twilio- | {"timestamp":"2025-07-13T16:53:59.376Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.376Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.376Z","level":"INFO","message":"🎤 Mapped voice 'energetic' → 'Puck' (Male, Lively, energetic tenor with higher pitch)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.376Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.377Z","level":"INFO","message":"🎤 Mapped voice 'energetic' → 'Puck' (Male, Lively, energetic tenor with higher pitch)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.377Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.377Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.377Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:53:59.386Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:53:59.413Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | 📋 Using outbound script 1 for call
69|twilio- | ⚠️ Using task parameter as AI instructions (deprecated - use campaign scripts)
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:53:59.464Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | 📋 Using outbound script 1 for call
69|twilio- | 🔌 WebSocket connection received on /media-stream
69|twilio- | 🔌 WebSocket headers: {
69|twilio- |   'sec-websocket-version': '13',
69|twilio- |   'sec-websocket-key': 'CIXUb2e9/N/36cSJDbDFnQ==',
69|twilio- |   connection: 'Upgrade',
69|twilio- |   upgrade: 'websocket',
69|twilio- |   'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits',
69|twilio- |   host: 'localhost:3101'
69|twilio- | }
69|twilio- | 📞 [OUTBOUND] Client connected for outbound call (test: false)
69|twilio- | 🔌 [OUTBOUND_CALL] Client connected
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:53:59.583Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ [OUTBOUND] Using current outbound script: 1
69|twilio- | 📞 [pending] Flow: outbound_call, Incoming: false
69|twilio- | 🤖 [pending] Using model: gemini-2.5-flash-preview-native-audio-dialog, voice: Kore
69|twilio- | 🔗 [GEMINI-LIVE] Direct Gemini Live WebSocket connection
69|twilio- | 🧪 [OUTBOUND TEST] Client connected for outbound testing
69|twilio- | {"timestamp":"2025-07-13T16:53:59.586Z","level":"INFO","message":"OUTBOUND_TEST testing session started","component":"WEBSOCKET","callSid":null,"sessionId":"outbound_test-1752425639586-u36f3m45u"}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.587Z","level":"INFO","message":"OUTBOUND_TEST testing connection closed","component":"WEBSOCKET","callSid":null,"sessionId":"outbound_test-1752425639586-u36f3m45u","code":1005,"reason":{"type":"Buffer","data":[]}}
69|twilio- | 🔚 [outbound_test-1752425639586-u36f3m45u] Ending session via lifecycle manager (reason: connection_closed)
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded incoming campaign 2 (test: false)
69|twilio- | ✅ Loading incoming campaign 2 for ID 8
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded outbound campaign 2 (test: false)
69|twilio- | ✅ Loading outbound campaign 2 for ID 2
69|twilio- | ✅ Loaded outbound campaign 3 (test: false)
69|twilio- | ✅ Loading outbound campaign 3 for ID 3
69|twilio- | ✅ Loaded outbound campaign 4 (test: false)
69|twilio- | ✅ Loading outbound campaign 4 for ID 4
69|twilio- | ✅ Loaded outbound campaign 5 (test: false)
69|twilio- | ✅ Loading outbound campaign 5 for ID 5
69|twilio- | ✅ Loaded outbound campaign 6 (test: false)
69|twilio- | ✅ Loading outbound campaign 6 for ID 6
69|twilio- | ✅ Loaded incoming campaign 1 (test: false)
69|twilio- | ✅ Loading incoming campaign 1 for ID 7
69|twilio- | ✅ Loaded incoming campaign 2 (test: false)
69|twilio- | ✅ Loading incoming campaign 2 for ID 8
69|twilio- | ✅ Loaded incoming campaign 3 (test: false)
69|twilio- | ✅ Loading incoming campaign 3 for ID 9
69|twilio- | ✅ Loaded incoming campaign 4 (test: false)
69|twilio- | ✅ Loading incoming campaign 4 for ID 10
69|twilio- | ✅ Loaded incoming campaign 5 (test: false)
69|twilio- | ✅ Loading incoming campaign 5 for ID 11
69|twilio- | ✅ Loaded incoming campaign 6 (test: false)
69|twilio- | ✅ Loading incoming campaign 6 for ID 12
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded outbound campaign 2 (test: false)
69|twilio- | ✅ Loading outbound campaign 2 for ID 2
69|twilio- | {"timestamp":"2025-07-13T16:54:38.256Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.256Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.256Z","level":"INFO","message":"🎤 Mapped voice 'energetic' → 'Puck' (Male, Lively, energetic tenor with higher pitch)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.256Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.257Z","level":"INFO","message":"🎤 Mapped voice 'energetic' → 'Puck' (Male, Lively, energetic tenor with higher pitch)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.257Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.257Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.257Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:54:38.266Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:54:38.287Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | 📋 Using outbound script 1 for call
69|twilio- | ⚠️ Using task parameter as AI instructions (deprecated - use campaign scripts)
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:54:38.341Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | 📋 Using outbound script 1 for call
69|twilio- | 🔌 WebSocket connection received on /media-stream
69|twilio- | 🔌 WebSocket headers: {
69|twilio- |   'sec-websocket-version': '13',
69|twilio- |   'sec-websocket-key': 'zgoDih62608qLvnKzpbnYw==',
69|twilio- |   connection: 'Upgrade',
69|twilio- |   upgrade: 'websocket',
69|twilio- |   'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits',
69|twilio- |   host: 'localhost:3101'
69|twilio- | }
69|twilio- | 📞 [OUTBOUND] Client connected for outbound call (test: false)
69|twilio- | 🔌 [OUTBOUND_CALL] Client connected
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T16:54:38.469Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ [OUTBOUND] Using current outbound script: 1
69|twilio- | 📞 [pending] Flow: outbound_call, Incoming: false
69|twilio- | 🤖 [pending] Using model: gemini-2.5-flash-preview-native-audio-dialog, voice: Kore
69|twilio- | 🔗 [GEMINI-LIVE] Direct Gemini Live WebSocket connection
69|twilio- | 🧪 [OUTBOUND TEST] Client connected for outbound testing
69|twilio- | {"timestamp":"2025-07-13T16:54:38.472Z","level":"INFO","message":"OUTBOUND_TEST testing session started","component":"WEBSOCKET","callSid":null,"sessionId":"outbound_test-1752425678472-v1weo2qh8"}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.473Z","level":"INFO","message":"OUTBOUND_TEST testing connection closed","component":"WEBSOCKET","callSid":null,"sessionId":"outbound_test-1752425678472-v1weo2qh8","code":1005,"reason":{"type":"Buffer","data":[]}}
69|twilio- | 🔚 [outbound_test-1752425678472-v1weo2qh8] Ending session via lifecycle manager (reason: connection_closed)
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded incoming campaign 1 (test: false)
69|twilio- | ✅ Loading incoming campaign 1 for ID 7
69|twilio- | ✅ Loaded incoming campaign 2 (test: false)
69|twilio- | ✅ Loading incoming campaign 2 for ID 8
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded outbound campaign 2 (test: false)
69|twilio- | ✅ Loading outbound campaign 2 for ID 2
69|twilio- | ✅ Loaded outbound campaign 3 (test: false)
69|twilio- | ✅ Loading outbound campaign 3 for ID 3
69|twilio- | ✅ Loaded outbound campaign 4 (test: false)
69|twilio- | ✅ Loading outbound campaign 4 for ID 4
69|twilio- | ✅ Loaded outbound campaign 5 (test: false)
69|twilio- | ✅ Loading outbound campaign 5 for ID 5
69|twilio- | ✅ Loaded outbound campaign 6 (test: false)
69|twilio- | ✅ Loading outbound campaign 6 for ID 6
69|twilio- | ✅ Loaded incoming campaign 1 (test: false)
69|twilio- | ✅ Loading incoming campaign 1 for ID 7
69|twilio- | ✅ Loaded incoming campaign 2 (test: false)
69|twilio- | ✅ Loading incoming campaign 2 for ID 8
69|twilio- | ✅ Loaded incoming campaign 3 (test: false)
69|twilio- | ✅ Loading incoming campaign 3 for ID 9
69|twilio- | ✅ Loaded incoming campaign 4 (test: false)
69|twilio- | ✅ Loading incoming campaign 4 for ID 10
69|twilio- | ✅ Loaded incoming campaign 5 (test: false)
69|twilio- | ✅ Loading incoming campaign 5 for ID 11
69|twilio- | ✅ Loaded incoming campaign 6 (test: false)
69|twilio- | ✅ Loading incoming campaign 6 for ID 12
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T17:09:48.424Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T17:09:48.425Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | 📋 Using outbound script 1 for call
69|twilio- | 📞 [CA4b9d58e4d232a30dfaf1176554503674] Outbound call initiated to +************
69|twilio- | 📊 [CA4b9d58e4d232a30dfaf1176554503674] Call status update: initiated
69|twilio- | 🚀 [CA4b9d58e4d232a30dfaf1176554503674] Call initiated from +************ to +************
69|twilio- | 💾 [CA4b9d58e4d232a30dfaf1176554503674] Session context saved at 2025-07-13T17:09:49.081Z
69|twilio- | 📊 [CA4b9d58e4d232a30dfaf1176554503674] Call status update: ringing
69|twilio- | 📞 [CA4b9d58e4d232a30dfaf1176554503674] Call ringing
69|twilio- | 💾 [CA4b9d58e4d232a30dfaf1176554503674] Session context saved at 2025-07-13T17:09:50.197Z
69|twilio- | 📊 [CA4b9d58e4d232a30dfaf1176554503674] Call status update: in-progress
69|twilio- | ℹ️ [CA4b9d58e4d232a30dfaf1176554503674] Unknown call status: in-progress
69|twilio- | 💾 [CA4b9d58e4d232a30dfaf1176554503674] Session context saved at 2025-07-13T17:09:53.043Z
69|twilio- | 📞 [INBOUND] Incoming call webhook received
69|twilio- | 📋 [INBOUND] Call data: Empty <[Object: null prototype] {}> {
69|twilio- |   Called: '+************',
69|twilio- |   ToState: '',
69|twilio- |   CallerCountry: 'CZ',
69|twilio- |   Direction: 'outbound-api',
69|twilio- |   CallerState: 'Praha',
69|twilio- |   ToZip: '',
69|twilio- |   CallSid: 'CA4b9d58e4d232a30dfaf1176554503674',
69|twilio- |   To: '+************',
69|twilio- |   CallerZip: '',
69|twilio- |   ToCountry: 'CZ',
69|twilio- |   CalledZip: '',
69|twilio- |   ApiVersion: '2010-04-01',
69|twilio- |   CalledCity: '',
69|twilio- |   CallStatus: 'in-progress',
69|twilio- |   From: '+************',
69|twilio- |   AccountSid: 'ACa75fdadde40218c273f8b4ab3abd880b',
69|twilio- |   CalledCountry: 'CZ',
69|twilio- |   CallerCity: '',
69|twilio- |   ToCity: '',
69|twilio- |   FromCountry: 'CZ',
69|twilio- |   Caller: '+************',
69|twilio- |   FromCity: '',
69|twilio- |   CalledState: '',
69|twilio- |   FromZip: '',
69|twilio- |   FromState: 'Praha'
69|twilio- | }
69|twilio- | 📞 [OUTBOUND] Call direction: outbound-api
69|twilio- | ✅ [OUTBOUND] Using nextCallConfig for outbound call
69|twilio- | ✅ [OUTBOUND] Call CA4b9d58e4d232a30dfaf1176554503674 configured with script: 1
69|twilio- | 📞 [CA4b9d58e4d232a30dfaf1176554503674] Call Details: {
69|twilio- |   from: '+************',
69|twilio- |   to: '+************',
69|twilio- |   direction: 'outbound-api',
69|twilio- |   status: 'in-progress',
69|twilio- |   scriptType: 'outbound',
69|twilio- |   hasInstructions: true,
69|twilio- |   voice: 'Kore',
69|twilio- |   model: 'gemini-2.5-flash-preview-native-audio-dialog'
69|twilio- | }
69|twilio- | 🔗 [OUTBOUND] WebSocket URL: wss://gemini-api.verduona.com/media-stream
69|twilio- | 🔗 [CA4b9d58e4d232a30dfaf1176554503674] PUBLIC_URL: https://gemini-api.verduona.com
69|twilio- | 📄 [OUTBOUND] Returning TwiML: <?xml version="1.0" encoding="UTF-8"?>
69|twilio- | <Response>
69|twilio- |     <Connect>
69|twilio- |         <Stream url="wss://gemini-api.verduona.com/media-stream" />
69|twilio- |     </Connect>
69|twilio- | </Response>
69|twilio- | 🔌 WebSocket connection received on /media-stream
69|twilio- | 🔌 WebSocket headers: {
69|twilio- |   host: 'gemini-api.verduona.com',
69|twilio- |   'x-real-ip': '***********',
69|twilio- |   'x-forwarded-for': '***********',
69|twilio- |   'x-forwarded-proto': 'https',
69|twilio- |   upgrade: 'websocket',
69|twilio- |   connection: 'upgrade',
69|twilio- |   'sec-websocket-version': '13',
69|twilio- |   'sec-websocket-key': 'wEpWnzfOVEBW8wNtLw+lcA==',
69|twilio- |   'user-agent': 'Twilio.TmeWs/1.0',
69|twilio- |   'x-twilio-signature': '51Ka00dNsdEOpqcu0s0GFkR/EGE='
69|twilio- | }
69|twilio- | 📞 [OUTBOUND] Client connected for outbound call (test: false)
69|twilio- | 🔌 [OUTBOUND_CALL] Client connected
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T17:09:53.539Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ [OUTBOUND] Using current outbound script: 1
69|twilio- | 📞 [pending] Flow: outbound_call, Incoming: false
69|twilio- | 🤖 [pending] Using model: gemini-2.5-flash-preview-native-audio-dialog, voice: Kore
69|twilio- | 📊 [CA4b9d58e4d232a30dfaf1176554503674] Call status update: completed
69|twilio- | ⏱️ [CA4b9d58e4d232a30dfaf1176554503674] Call duration: 0 seconds
69|twilio- | 🏁 [CA4b9d58e4d232a30dfaf1176554503674] Call completed (Duration: 0s)
69|twilio- | 🗑️ [CA4b9d58e4d232a30dfaf1176554503674] Session context cleared
69|twilio- | 💾 [CA4b9d58e4d232a30dfaf1176554503674] Session context saved at 2025-07-13T17:09:54.057Z
69|twilio- | {"timestamp":"2025-07-13T17:13:07.057Z","level":"INFO","message":"\n🛑 Received SIGINT, initiating graceful shutdown...","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.057Z","level":"INFO","message":"🔒 Stopping server from accepting new connections...","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.058Z","level":"INFO","message":"🧹 Cleaning up session management resources...","component":null,"callSid":null}
69|twilio- | 🛑 Health monitoring stopped
69|twilio- | 🧹 HealthMonitor: Cleared 0 connection states
69|twilio- | {"timestamp":"2025-07-13T17:13:07.059Z","level":"INFO","message":"🔚 Ending 0 active sessions...","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.059Z","level":"INFO","message":"🗑️ Clearing data structures...","component":null,"callSid":null}
69|twilio- | 🧹 ContextManager: Cleared 1 contexts and 0 recovery attempts
69|twilio- | 🧹 RecoveryManager: Cleared 0 queued, 0 in-progress recoveries, and 0 health check intervals
69|twilio- | 🧹 SummaryManager: Cleared 0 timeouts and 0 in-progress summaries
69|twilio- | 🔌 All transcription connections closed
69|twilio- | 🧹 TranscriptionManager: Cleaned up 0 active transcription connections
69|twilio- | {"timestamp":"2025-07-13T17:13:07.059Z","level":"INFO","message":"✅ Graceful shutdown completed","component":null,"callSid":null}
69|twilio- | ✅ Universal Campaign Script Loader ready - handles all 4 scenarios with real scripts only
69|twilio- | {"timestamp":"2025-07-13T17:13:07.568Z","level":"INFO","message":"Configuration validation passed","component":"CONFIG","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.569Z","level":"INFO","message":"🚀 Twilio Gemini Live API Server starting...","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.569Z","level":"INFO","message":"📝 Using Gemini API Key: SET ✅","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.569Z","level":"INFO","message":"📞 Twilio Config: SET ✅","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.569Z","level":"INFO","message":"🎤 Deepgram API Key: SET ✅","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.569Z","level":"INFO","message":"🔗 Public URL: https://gemini-api.verduona.com","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.569Z","level":"INFO","message":"🤖 Default Model: gemini-2.5-flash-preview-native-audio-dialog","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.569Z","level":"INFO","message":"🎵 Default Voice: Kore","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.570Z","level":"INFO","message":"🤖 Gemini client initialized successfully","component":"GEMINI","callSid":null}
69|twilio- | 🏥 Connection health monitoring started (interval: 30000ms)
69|twilio- | {"timestamp":"2025-07-13T17:13:07.582Z","level":"INFO","message":"🔧 Registering WebSocket handlers...","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.583Z","level":"INFO","message":"✅ WebSocket handlers registered","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.583Z","level":"INFO","message":"🔧 Calling registerApiRoutes...","component":null,"callSid":null}
69|twilio- | 🚀 Starting API route registration...
69|twilio- | 🔍 [DEBUG] Dependencies received: [
69|twilio- |   'geminiClient',
69|twilio- |   'contextManager',
69|twilio- |   'sessionManager',
69|twilio- |   'healthMonitor',
69|twilio- |   'summaryManager',
69|twilio- |   'lifecycleManager',
69|twilio- |   'recoveryManager',
69|twilio- |   'scriptManager',
69|twilio- |   'audioProcessor',
69|twilio- |   'transcriptionManager',
69|twilio- |   'activeConnections',
69|twilio- |   'voiceManager',
69|twilio- |   'modelManager',
69|twilio- |   'GEMINI_DEFAULT_VOICE',
69|twilio- |   'GEMINI_DEFAULT_MODEL',
69|twilio- |   'SUMMARY_GENERATION_PROMPT',
69|twilio- |   'AI_PREPARE_MESSAGE',
69|twilio- |   'TWILIO_ACCOUNT_SID',
69|twilio- |   'TWILIO_AUTH_TOKEN',
69|twilio- |   'PUBLIC_URL'
69|twilio- | ]
69|twilio- | ✅ Dependencies extracted successfully
69|twilio- | 🔍 [DEBUG] scriptManager extracted: true object
69|twilio- | 🔍 [DEBUG] About to register incoming scenarios route...
69|twilio- | 🔧 Registering /api/incoming-scenarios route...
69|twilio- | 🔍 [DEBUG] scriptManager available: true
69|twilio- | 🔍 [DEBUG] scriptManager.getIncomingScripts available: function
69|twilio- | ✅ /api/incoming-scenarios route registered successfully
69|twilio- | 🔧 Registering /api/incoming-scenarios/select route...
69|twilio- | ✅ /api/incoming-scenarios/select route registered successfully
69|twilio- | 🔧 Registering /api/configure-incoming-scenario route...
69|twilio- | ✅ /api/configure-incoming-scenario route registered successfully
69|twilio- | 🔧 Registering test route...
69|twilio- | ✅ Test route registered successfully
69|twilio- | 🔍 [DEBUG] Reached end of route registration function
69|twilio- | ✅ All API routes registered successfully!
69|twilio- | {"timestamp":"2025-07-13T17:13:07.595Z","level":"INFO","message":"✅ API routes registration completed","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.595Z","level":"INFO","message":"🔧 Registering management routes...","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.597Z","level":"INFO","message":"✅ Management routes registered","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.597Z","level":"INFO","message":"🔧 Registering testing routes...","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.599Z","level":"INFO","message":"✅ Testing routes registered","component":null,"callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loaded incoming campaign 1 (test: false)
69|twilio- | ✅ Loaded outbound campaign 2 (test: false)
69|twilio- | ✅ Loaded incoming campaign 2 (test: false)
69|twilio- | ✅ Loaded outbound campaign 3 (test: false)
69|twilio- | ✅ Loaded incoming campaign 3 (test: false)
69|twilio- | ✅ Loaded outbound campaign 4 (test: false)
69|twilio- | ✅ Loaded incoming campaign 4 (test: false)
69|twilio- | ✅ Loaded outbound campaign 5 (test: false)
69|twilio- | ✅ Loaded incoming campaign 5 (test: false)
69|twilio- | ✅ Loaded outbound campaign 6 (test: false)
69|twilio- | ✅ Loaded incoming campaign 6 (test: false)
69|twilio- | {"timestamp":"2025-07-13T17:13:07.602Z","level":"INFO","message":"📋 Script preloading completed","component":null,"callSid":null,"count":12,"duration_ms":3}
69|twilio- | 📡 Registering /media-stream WebSocket route for outbound calls
69|twilio- | 📡 Registering /media-stream-inbound WebSocket route for inbound calls
69|twilio- | {"timestamp":"2025-07-13T17:13:07.646Z","level":"INFO","message":"🚀 Server listening on port 3101","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.646Z","level":"INFO","message":"🔗 WebSocket endpoint: ws://localhost:3101/media-stream","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.647Z","level":"INFO","message":"🧪 Local audio testing: ws://localhost:3101/local-audio-session","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.647Z","level":"INFO","message":"🏥 Health check: http://localhost:3101/health","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.647Z","level":"INFO","message":"📊 API documentation: http://localhost:3101/","component":null,"callSid":null}
69|twilio- | 🤖 Current Model: gemini-2.5-flash-preview-native-audio-dialog
69|twilio- | 🤖 Default Model: gemini-2.5-flash-preview-native-audio-dialog
69|twilio- | 📋 Available Models: gemini-2.5-flash-preview-native-audio-dialog, gemini-2.0-flash-live-001
69|twilio- | 🔧 Model Selection: Enabled
69|twilio- | ⚙️ Configuration Source: Environment
69|twilio- | {"timestamp":"2025-07-13T17:13:07.647Z","level":"INFO","message":"🎤 Default Voice: Kore (soft alto, empathetic)","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.647Z","level":"INFO","message":"🎵 Available Voices: Aoede, Puck, Charon, Kore, Fenrir, Leda, Orus, Zephyr","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.647Z","level":"INFO","message":"🔧 Voice Selection: Enabled","component":null,"callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:07.647Z","level":"INFO","message":"✅ Server is ready to handle calls!","component":null,"callSid":null}
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | ✅ Loading outbound campaign 1 for ID 1
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T17:14:04.960Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | 📋 Using outbound script 1 for call
69|twilio- | 📞 [CA0b59ea70b6aa33adb5f00c72c4e374c3] Outbound call initiated to +************
69|twilio- | 📊 [CA0b59ea70b6aa33adb5f00c72c4e374c3] Call status update: initiated
69|twilio- | 🚀 [CA0b59ea70b6aa33adb5f00c72c4e374c3] Call initiated from +************ to +************
69|twilio- | ���� [CA0b59ea70b6aa33adb5f00c72c4e374c3] Session context saved at 2025-07-13T17:14:05.486Z
69|twilio- | 📊 [CA0b59ea70b6aa33adb5f00c72c4e374c3] Call status update: ringing
69|twilio- | 📞 [CA0b59ea70b6aa33adb5f00c72c4e374c3] Call ringing
69|twilio- | 💾 [CA0b59ea70b6aa33adb5f00c72c4e374c3] Session context saved at 2025-07-13T17:14:06.823Z
69|twilio- | 📊 [CA0b59ea70b6aa33adb5f00c72c4e374c3] Call status update: in-progress
69|twilio- | ℹ️ [CA0b59ea70b6aa33adb5f00c72c4e374c3] Unknown call status: in-progress
69|twilio- | 💾 [CA0b59ea70b6aa33adb5f00c72c4e374c3] Session context saved at 2025-07-13T17:14:09.541Z
69|twilio- | 📞 [INBOUND] Incoming call webhook received
69|twilio- | 📋 [INBOUND] Call data: Empty <[Object: null prototype] {}> {
69|twilio- |   Called: '+************',
69|twilio- |   ToState: '',
69|twilio- |   CallerCountry: 'CZ',
69|twilio- |   Direction: 'outbound-api',
69|twilio- |   CallerState: 'Praha',
69|twilio- |   ToZip: '',
69|twilio- |   CallSid: 'CA0b59ea70b6aa33adb5f00c72c4e374c3',
69|twilio- |   To: '+************',
69|twilio- |   CallerZip: '',
69|twilio- |   ToCountry: 'CZ',
69|twilio- |   CalledZip: '',
69|twilio- |   ApiVersion: '2010-04-01',
69|twilio- |   CalledCity: '',
69|twilio- |   CallStatus: 'in-progress',
69|twilio- |   From: '+************',
69|twilio- |   AccountSid: 'ACa75fdadde40218c273f8b4ab3abd880b',
69|twilio- |   CalledCountry: 'CZ',
69|twilio- |   CallerCity: '',
69|twilio- |   ToCity: '',
69|twilio- |   FromCountry: 'CZ',
69|twilio- |   Caller: '+************',
69|twilio- |   FromCity: '',
69|twilio- |   CalledState: '',
69|twilio- |   FromZip: '',
69|twilio- |   FromState: 'Praha'
69|twilio- | }
69|twilio- | 📞 [OUTBOUND] Call direction: outbound-api
69|twilio- | ✅ [OUTBOUND] Using nextCallConfig for outbound call
69|twilio- | ✅ [OUTBOUND] Call CA0b59ea70b6aa33adb5f00c72c4e374c3 configured with script: 1
69|twilio- | 📞 [CA0b59ea70b6aa33adb5f00c72c4e374c3] Call Details: {
69|twilio- |   from: '+************',
69|twilio- |   to: '+************',
69|twilio- |   direction: 'outbound-api',
69|twilio- |   status: 'in-progress',
69|twilio- |   scriptType: 'outbound',
69|twilio- |   hasInstructions: false,
69|twilio- |   voice: 'Kore',
69|twilio- |   model: 'gemini-2.5-flash-preview-native-audio-dialog'
69|twilio- | }
69|twilio- | 🔗 [OUTBOUND] WebSocket URL: wss://gemini-api.verduona.com/media-stream
69|twilio- | 🔗 [CA0b59ea70b6aa33adb5f00c72c4e374c3] PUBLIC_URL: https://gemini-api.verduona.com
69|twilio- | 📄 [OUTBOUND] Returning TwiML: <?xml version="1.0" encoding="UTF-8"?>
69|twilio- | <Response>
69|twilio- |     <Connect>
69|twilio- |         <Stream url="wss://gemini-api.verduona.com/media-stream" />
69|twilio- |     </Connect>
69|twilio- | </Response>
69|twilio- | 🔌 WebSocket connection received on /media-stream
69|twilio- | 🔌 WebSocket headers: {
69|twilio- |   host: 'gemini-api.verduona.com',
69|twilio- |   'x-real-ip': '**************',
69|twilio- |   'x-forwarded-for': '**************',
69|twilio- |   'x-forwarded-proto': 'https',
69|twilio- |   upgrade: 'websocket',
69|twilio- |   connection: 'upgrade',
69|twilio- |   'sec-websocket-version': '13',
69|twilio- |   'sec-websocket-key': 'L11by4c7Ji4OS5C/2oTq2A==',
69|twilio- |   'user-agent': 'Twilio.TmeWs/1.0',
69|twilio- |   'x-twilio-signature': '51Ka00dNsdEOpqcu0s0GFkR/EGE='
69|twilio- | }
69|twilio- | 📞 [OUTBOUND] Client connected for outbound call (test: false)
69|twilio- | 🔌 [OUTBOUND_CALL] Client connected
69|twilio- | ✅ Loaded outbound campaign 1 (test: false)
69|twilio- | {"timestamp":"2025-07-13T17:14:09.970Z","level":"INFO","message":"🤖 Using requested model: gemini-2.5-flash-preview-native-audio-dialog","component":"GEMINI","callSid":null}
69|twilio- | ✅ [OUTBOUND] Using current outbound script: 1
69|twilio- | 📞 [pending] Flow: outbound_call, Incoming: false
69|twilio- | 🤖 [pending] Using model: gemini-2.5-flash-preview-native-audio-dialog, voice: Kore
69|twilio- | 📊 [CA0b59ea70b6aa33adb5f00c72c4e374c3] Call status update: completed
69|twilio- | ⏱️ [CA0b59ea70b6aa33adb5f00c72c4e374c3] Call duration: 0 seconds
69|twilio- | 🏁 [CA0b59ea70b6aa33adb5f00c72c4e374c3] Call completed (Duration: 0s)
69|twilio- | 🗑️ [CA0b59ea70b6aa33adb5f00c72c4e374c3] Session context cleared
69|twilio- | 💾 [CA0b59ea70b6aa33adb5f00c72c4e374c3] Session context saved at 2025-07-13T17:14:10.437Z

/home/<USER>/.pm2/logs/twilio-gemini-backend-error.log last 1000 lines:
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | ⚠️ [inbound_test-1752416213272-m1cz59vjr] Cannot send audio: localWs=true, readyState=3
69|twilio- | {"timestamp":"2025-07-13T14:19:02.285Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T14:19:02.791Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T14:19:03.962Z","level":"WARN","message":"⚠️ Unknown voice 'empathetic', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T14:50:19.142Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T14:50:19.143Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T14:50:19.145Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T14:50:20.135Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T14:50:20.140Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T14:50:20.143Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:10:04.801Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:10:06.356Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:10:06.393Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:10:06.394Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:10:06.394Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:10:06.900Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:10:08.111Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:10:10.797Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:10:10.851Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:10:11.230Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:10:11.693Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:10:11.705Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:10:16.078Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:19:57.724Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:30:35.343Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:30:51.061Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:30:51.061Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:30:51.061Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ⚠️ [pending] No connection data found for session end
69|twilio- | {"timestamp":"2025-07-13T15:32:10.011Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:32:18.409Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:32:18.411Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:32:18.412Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:33:58.862Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T15:39:10.329Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:02:40.975Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:22:24.736Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:32:55.896Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:32:56.074Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:33:31.120Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:33:31.263Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:33:43.509Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.206Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.219Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.233Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.236Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.248Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.252Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.266Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.272Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.278Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.292Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.295Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.298Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.299Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.301Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.311Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.314Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.317Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.318Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.320Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.321Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.321Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.327Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.328Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.357Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.368Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.369Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.369Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.440Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.441Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1234567890
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | {"timestamp":"2025-07-13T16:34:30.665Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1987654321
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | {"timestamp":"2025-07-13T16:34:30.686Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.697Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:30.743Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ⚠️ [outbound_test-1752424470754-t4whkx7dz] No connection data found for session end
69|twilio- | {"timestamp":"2025-07-13T16:34:51.964Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:51.974Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:51.978Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:51.982Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:51.989Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:51.991Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:51.998Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.001Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.005Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.014Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.015Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.018Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.018Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.023Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.030Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.035Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.037Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.037Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.037Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.038Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.038Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.045Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.045Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.098Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.098Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.105Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.114Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.115Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1234567890
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1987654321
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | {"timestamp":"2025-07-13T16:34:52.286Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.302Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.316Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:34:52.344Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ⚠️ [outbound_test-1752424492348-vvcfdce43] No connection data found for session end
69|twilio- | {"timestamp":"2025-07-13T16:35:04.339Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:35:04.347Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:40:14.620Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:40:14.620Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:40:23.795Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:40:30.966Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:40:39.078Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:41:46.740Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.025Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.058Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.063Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.065Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.074Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.076Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.086Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.093Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.097Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.107Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.111Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.119Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.119Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.122Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.125Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.129Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.131Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.132Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.132Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.133Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.133Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.138Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.138Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.193Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.195Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.196Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.196Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.211Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.211Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1987654321
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1234567890
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | {"timestamp":"2025-07-13T16:42:31.363Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.387Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.395Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:31.404Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ⚠️ [outbound_test-1752424951408-h15fxtjnx] No connection data found for session end
69|twilio- | {"timestamp":"2025-07-13T16:42:43.422Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:43.438Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:43.450Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:43.455Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:43.462Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:43.467Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:43.472Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:43.478Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:43.483Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:43.488Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:43.494Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:43.500Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:43.506Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.486Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.495Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.499Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.503Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.519Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.522Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.533Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.545Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.549Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.557Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.559Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.564Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.564Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.577Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.578Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.580Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.581Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.581Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.582Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.582Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.583Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.589Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.590Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.654Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.654Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.666Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.676Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.676Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1987654321
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1234567890
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | {"timestamp":"2025-07-13T16:42:51.828Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.844Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.851Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:42:51.852Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | ⚠️ [outbound_test-1752424971859-4j0ngs3kk] No connection data found for session end
69|twilio- | {"timestamp":"2025-07-13T16:43:03.883Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:03.903Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:03.913Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:03.918Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:03.923Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:03.927Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:03.934Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:03.939Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:03.945Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:03.950Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:03.955Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:03.960Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:03.967Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.388Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.415Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.418Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.422Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.427Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.429Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.439Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.444Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.449Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.467Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.472Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.475Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.475Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.479Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.481Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.483Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.485Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.485Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.486Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.487Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.487Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.492Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.493Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.543Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.543Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.551Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.562Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.563Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1234567890
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | {"timestamp":"2025-07-13T16:43:46.744Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.759Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:46.765Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1987654321
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | {"timestamp":"2025-07-13T16:43:46.927Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ⚠️ [outbound_test-1752425026936-u26qmv740] No connection data found for session end
69|twilio- | {"timestamp":"2025-07-13T16:43:58.788Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:58.806Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:58.817Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:58.822Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:58.827Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:58.832Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:58.838Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:58.845Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:58.850Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:58.856Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:58.861Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:58.866Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:43:58.871Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.048Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.071Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.076Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.080Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.087Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.090Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.100Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.105Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.109Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.122Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.124Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.129Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.129Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.137Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.140Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.144Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.147Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.148Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.148Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.149Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.149Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.154Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.155Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.180Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.181Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.205Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.217Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.217Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1987654321
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | {"timestamp":"2025-07-13T16:44:38.347Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ⚠️ [outbound_test-1752425078350-wnojw4ztm] No connection data found for session end
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1234567890
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | {"timestamp":"2025-07-13T16:44:38.373Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:38.382Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:43.387Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:55.421Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:55.433Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:55.444Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:55.450Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:55.454Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:55.462Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:55.469Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:55.474Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:55.480Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:55.484Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:55.489Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:55.494Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:44:55.500Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:45:12.939Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.621Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.652Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.656Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.660Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.689Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.692Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.714Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.717Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.720Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.732Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.734Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.739Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.739Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.741Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.743Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.747Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.752Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.752Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.752Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.752Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.752Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.759Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.759Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.826Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.826Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.832Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.842Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:00.842Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1987654321
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1234567890
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | {"timestamp":"2025-07-13T16:47:01.006Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:01.013Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:01.018Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | ⚠️ [outbound_test-1752425221017-qrqag2kq9] No connection data found for session end
69|twilio- | {"timestamp":"2025-07-13T16:47:06.024Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:18.051Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:18.064Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:18.071Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:18.076Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:18.082Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:18.088Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:18.093Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:18.099Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:18.104Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:18.109Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:18.115Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:18.120Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:18.125Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.325Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.352Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.360Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.362Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.369Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.371Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.377Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.380Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.383Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.391Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.392Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.394Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.394Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.396Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.398Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.399Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.401Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.401Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.402Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.403Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.404Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.413Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.414Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.430Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.431Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.498Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.508Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.509Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1987654321
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | {"timestamp":"2025-07-13T16:47:27.618Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ⚠️ [outbound_test-1752425247622-nuirp1p89] No connection data found for session end
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1234567890
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | {"timestamp":"2025-07-13T16:47:27.657Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:27.664Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:32.670Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:44.695Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:44.708Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:44.716Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:44.721Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:44.727Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:44.734Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:44.739Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:44.745Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:44.750Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:44.757Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:44.762Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:44.769Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:44.775Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:50.954Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:50.985Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:50.990Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:50.996Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.003Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.005Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.020Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.026Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.030Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.041Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.043Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.045Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.045Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.048Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.050Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.052Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.058Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.059Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.059Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.059Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.059Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.064Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.065Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.081Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.081Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.081Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.090Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.102Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.102Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1987654321
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1234567890
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | {"timestamp":"2025-07-13T16:47:51.276Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.288Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:47:51.289Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ⚠️ [outbound_test-1752425271293-dppwmtiew] No connection data found for session end
69|twilio- | {"timestamp":"2025-07-13T16:48:08.327Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:48:08.340Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:48:08.351Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:48:08.357Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:48:08.363Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:48:08.368Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:48:08.373Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:48:08.380Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:48:08.385Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:48:08.390Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:48:08.395Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:48:08.400Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:48:08.406Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.452Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.484Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.507Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.511Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.519Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.523Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.534Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.538Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.542Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.554Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.557Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.559Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.560Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.561Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.563Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.565Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.566Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.566Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.567Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.568Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.568Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.577Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.578Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.596Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.596Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.614Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.625Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.625Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1987654321
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1234567890
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | {"timestamp":"2025-07-13T16:50:01.796Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.807Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:01.807Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | ⚠️ [outbound_test-1752425401811-bvw3cdepv] No connection data found for session end
69|twilio- | {"timestamp":"2025-07-13T16:50:06.814Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:18.840Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:18.852Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:18.863Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:18.868Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:18.873Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:18.879Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:18.884Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:18.889Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:18.895Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:18.900Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:18.906Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:18.911Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:50:18.916Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.093Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.104Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.108Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.115Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.127Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.134Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.142Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.145Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.148Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.159Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.161Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.162Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.163Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.165Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.167Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.169Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.182Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.182Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.183Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.183Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.183Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.191Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.192Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.211Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.211Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.212Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.285Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.297Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.298Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1987654321
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | {"timestamp":"2025-07-13T16:53:09.411Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ⚠️ [outbound_test-1752425589414-0fd24383i] No connection data found for session end
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1234567890
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | {"timestamp":"2025-07-13T16:53:09.442Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:09.449Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:14.454Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:26.484Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:26.496Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:26.506Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:26.511Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:26.516Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:26.521Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:26.526Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:26.530Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:26.537Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:26.541Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:26.546Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:26.552Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:26.558Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.634Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.655Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.659Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.665Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.672Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.674Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.695Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.698Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.701Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.715Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.717Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.719Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.719Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.722Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.724Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.727Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.730Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.730Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.731Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.731Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.731Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.739Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.739Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.766Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.776Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.777Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.803Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.803Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1234567890
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | {"timestamp":"2025-07-13T16:53:34.934Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1987654321
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | {"timestamp":"2025-07-13T16:53:34.951Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:34.985Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ⚠️ [outbound_test-1752425614988-ou4if551c] No connection data found for session end
69|twilio- | {"timestamp":"2025-07-13T16:53:39.961Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:51.984Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:51.995Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:52.003Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:52.008Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:52.013Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:52.018Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:52.023Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:52.029Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:52.034Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:52.038Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:52.043Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:52.049Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:52.054Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.299Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.314Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.320Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.326Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.333Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.334Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.346Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.349Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.352Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.362Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.364Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.366Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.366Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.368Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.370Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.373Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.375Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.376Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.376Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.377Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.377Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.385Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.386Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.412Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.413Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.454Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.464Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.464Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1987654321
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | {"timestamp":"2025-07-13T16:53:59.583Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ⚠️ [outbound_test-1752425639586-u36f3m45u] No connection data found for session end
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1234567890
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | {"timestamp":"2025-07-13T16:53:59.609Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:53:59.615Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:16.647Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:16.658Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:16.668Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:16.674Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:16.680Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:16.685Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:16.690Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:16.695Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:16.701Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:16.706Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:16.711Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:16.718Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:16.724Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.143Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.173Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.184Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.189Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.199Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.202Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.217Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.220Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.223Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.234Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.236Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.243Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.244Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.248Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.251Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.252Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.255Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.255Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.256Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.257Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.257Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.265Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.266Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.286Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.287Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.328Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.341Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.341Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1987654321
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | {"timestamp":"2025-07-13T16:54:38.469Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | ⚠️ [outbound_test-1752425678472-v1weo2qh8] No connection data found for session end
69|twilio- | ❌ Error making call: RestException [Error]: From is not a valid phone number: +1234567890
69|twilio- |     at success (/home/<USER>/github/verduona-full/twilio-gemini-liveapi/node_modules/twilio/lib/base/Version.js:79:23)
69|twilio- |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69|twilio- |     at async Object.<anonymous> (file:///home/<USER>/github/verduona-full/twilio-gemini-liveapi/src/api/routes.js:1233:26) {
69|twilio- |   status: 400,
69|twilio- |   code: 21212,
69|twilio- |   moreInfo: 'https://www.twilio.com/docs/errors/21212',
69|twilio- |   details: undefined
69|twilio- | }
69|twilio- | {"timestamp":"2025-07-13T16:54:38.488Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:38.495Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:43.502Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:55.525Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:55.539Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:55.549Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:55.554Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:55.559Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:55.564Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:55.569Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:55.574Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:55.580Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:55.585Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:55.591Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:55.597Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T16:54:55.602Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:09:33.164Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:09:33.165Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:09:33.166Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:09:33.167Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:09:39.913Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:09:48.386Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:09:48.424Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:09:48.424Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:09:48.425Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:09:49.080Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:09:50.196Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:09:53.043Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:09:53.119Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:09:53.539Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:09:53.869Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:09:54.056Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:09:58.254Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:48.231Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:48.247Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:48.257Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:48.260Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:49.152Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:49.154Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:49.158Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:13:49.162Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:14:03.279Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:14:04.918Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:14:04.958Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:14:04.959Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:14:05.485Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:14:06.823Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:14:09.540Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:14:09.607Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:14:09.970Z","level":"WARN","message":"⚠️ Unknown voice 'relaxed', using default: Kore (Soft, empathetic alto with caring tone)","component":"GEMINI","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:14:10.275Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:14:10.436Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}
69|twilio- | {"timestamp":"2025-07-13T17:14:14.907Z","level":"WARN","message":"No authorization header - allowing for development","component":"AUTH","callSid":null}

