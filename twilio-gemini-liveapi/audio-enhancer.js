/**
 * Advanced Audio Enhancement Module
 * Provides sophisticated audio processing algorithms for better voice quality
 */

export class AudioEnhancer {
    constructor() {
        this.sampleRate = 8000; // Default sample rate for Twilio
        this.frameSize = 160; // 20ms frames at 8kHz
        this.noiseProfile = null;
        this.agcSettings = {
            targetLevel: 0.7,
            attackTime: 0.003, // 3ms
            releaseTime: 0.1, // 100ms
            currentGain: 1.0
        };
        // De-essing configuration
        this.deEssingSettings = {
            enabled: true, // Default enabled
            threshold: 0.3,
            ratio: 4.0,
            frequency: 6000, // Target frequency for sibilants
            bandwidth: 2000 // Frequency range to process
        };

        // Audio debugging and monitoring
        this.debugMode = process.env.AUDIO_DEBUG === 'true';
        this.monitoringEnabled = process.env.AUDIO_MONITORING !== 'false';
        this.processingStats = {
            totalProcessed: 0,
            averageProcessingTime: 0,
            qualityMetrics: {
                snrImprovement: 0,
                dynamicRangeImprovement: 0,
                clippingReduction: 0
            }
        };
    }

    /**
     * Apply comprehensive audio enhancement pipeline
     * @param {Float32Array} samples - Input audio samples
     * @param {Object} options - Enhancement options
     * @returns {Float32Array} Enhanced audio samples
     */
    enhance(samples, options = {}) {
        const startTime = this.debugMode ? Date.now() : 0;
        let qualityBefore = null;
        let qualityAfter = null;

        try {
            // Analyze input quality for debugging
            if (this.debugMode || this.monitoringEnabled) {
                qualityBefore = this.analyzeAudioQuality(samples, 'input');
            }

            let enhanced = new Float32Array(samples);

            // Apply enhancement pipeline
            enhanced = this.applyEnhancementPipeline(enhanced, options);

            // Analyze output quality and update stats
            if (this.debugMode || this.monitoringEnabled) {
                qualityAfter = this.analyzeAudioQuality(enhanced, 'output');
                this.updateProcessingStats(startTime, qualityBefore, qualityAfter);
            }

            return enhanced;
        } catch (error) {
            console.error('❌ Error in audio enhancement:', error);
            return samples; // Return original on error
        }
    }

    /**
     * Apply the enhancement pipeline steps
     * @private
     */
    applyEnhancementPipeline(samples, options) {
        const steps = [
            { 
                enabled: options.noiseReduction !== false, 
                method: 'spectralSubtraction', 
                name: 'noise reduction' 
            },
            { 
                enabled: options.compression !== false, 
                method: 'multibandCompressor', 
                name: 'dynamic range compression' 
            },
            { 
                enabled: options.agc !== false, 
                method: 'adaptiveGainControl', 
                name: 'adaptive gain control' 
            },
            { 
                enabled: options.voiceEnhancement !== false, 
                method: 'voiceEnhancement', 
                name: 'voice enhancement' 
            },
            { 
                enabled: options.deEssing !== false && this.deEssingSettings.enabled, 
                method: 'applyDeEssing', 
                name: 'de-essing' 
            },
            { 
                enabled: true, 
                method: 'softLimiter', 
                name: 'soft limiting' 
            }
        ];

        let enhanced = samples;
        for (const step of steps) {
            if (step.enabled) {
                enhanced = this[step.method](enhanced);
                if (this.debugMode) {
                    console.log(`🔧 [AudioEnhancer] Applied ${step.name}`);
                }
            }
        }

        return enhanced;
    }

    /**
     * Spectral subtraction noise reduction
     * @param {Float32Array} samples - Input samples
     * @returns {Float32Array} Noise-reduced samples
     */
    spectralSubtraction(samples) {
        const frameSize = this.frameSize;
        const hopSize = frameSize / 2;
        const numFrames = Math.floor((samples.length - frameSize) / hopSize) + 1;
        const output = new Float32Array(samples.length);

        // Simple spectral subtraction implementation
        for (let frame = 0; frame < numFrames; frame++) {
            const start = frame * hopSize;
            const end = Math.min(start + frameSize, samples.length);
            
            // Extract frame
            const frameData = samples.slice(start, end);
            
            // Apply window function (Hann window)
            const windowed = this.applyHannWindow(frameData);
            
            // Simple noise reduction (high-pass filter effect)
            const filtered = this.highPassFilter(windowed, 80);
            
            // Overlap-add
            for (let i = 0; i < filtered.length && start + i < output.length; i++) {
                output[start + i] += filtered[i] * 0.5; // 50% overlap
            }
        }

        return output;
    }

    /**
     * Apply Hann window to reduce spectral leakage
     * @param {Float32Array} frame - Input frame
     * @returns {Float32Array} Windowed frame
     */
    applyHannWindow(frame) {
        const windowed = new Float32Array(frame.length);
        for (let i = 0; i < frame.length; i++) {
            const window = 0.5 * (1 - Math.cos(2 * Math.PI * i / (frame.length - 1)));
            windowed[i] = frame[i] * window;
        }
        return windowed;
    }

    /**
     * High-pass filter for noise reduction
     * @param {Float32Array} samples - Input samples
     * @param {number} cutoffFreq - Cutoff frequency in Hz
     * @returns {Float32Array} Filtered samples
     */
    highPassFilter(samples, cutoffFreq) {
        const rc = 1.0 / (cutoffFreq * 2 * Math.PI);
        const dt = 1.0 / this.sampleRate;
        const alpha = rc / (rc + dt);
        
        const filtered = new Float32Array(samples.length);
        filtered[0] = samples[0];
        
        for (let i = 1; i < samples.length; i++) {
            filtered[i] = alpha * (filtered[i-1] + samples[i] - samples[i-1]);
        }
        
        return filtered;
    }

    /**
     * Multiband compressor for dynamic range control
     * @param {Float32Array} samples - Input samples
     * @returns {Float32Array} Compressed samples
     */
    multibandCompressor(samples) {
        // Split into frequency bands
        const lowBand = this.lowPassFilter(samples, 1000);
        const midBand = this.bandPassFilter(samples, 1000, 3000);
        const highBand = this.highPassFilter(samples, 3000);

        // Apply different compression to each band
        const compressedLow = this.compress(lowBand, 0.7, 2.0);
        const compressedMid = this.compress(midBand, 0.6, 3.0);
        const compressedHigh = this.compress(highBand, 0.8, 1.5);

        // Mix bands back together
        const output = new Float32Array(samples.length);
        for (let i = 0; i < samples.length; i++) {
            output[i] = compressedLow[i] * 0.4 + compressedMid[i] * 0.4 + compressedHigh[i] * 0.2;
        }

        return output;
    }

    /**
     * Low-pass filter
     * @param {Float32Array} samples - Input samples
     * @param {number} cutoffFreq - Cutoff frequency in Hz
     * @returns {Float32Array} Filtered samples
     */
    lowPassFilter(samples, cutoffFreq) {
        const rc = 1.0 / (cutoffFreq * 2 * Math.PI);
        const dt = 1.0 / this.sampleRate;
        const alpha = dt / (rc + dt);
        
        const filtered = new Float32Array(samples.length);
        filtered[0] = samples[0];
        
        for (let i = 1; i < samples.length; i++) {
            filtered[i] = filtered[i-1] + alpha * (samples[i] - filtered[i-1]);
        }
        
        return filtered;
    }

    /**
     * Band-pass filter
     * @param {Float32Array} samples - Input samples
     * @param {number} lowFreq - Low cutoff frequency
     * @param {number} highFreq - High cutoff frequency
     * @returns {Float32Array} Filtered samples
     */
    bandPassFilter(samples, lowFreq, highFreq) {
        const highPassed = this.highPassFilter(samples, lowFreq);
        return this.lowPassFilter(highPassed, highFreq);
    }

    /**
     * Dynamic range compressor
     * @param {Float32Array} samples - Input samples
     * @param {number} threshold - Compression threshold
     * @param {number} ratio - Compression ratio
     * @returns {Float32Array} Compressed samples
     */
    compress(samples, threshold, ratio) {
        const output = new Float32Array(samples.length);
        
        for (let i = 0; i < samples.length; i++) {
            const sample = samples[i];
            const absSample = Math.abs(sample);
            
            if (absSample > threshold) {
                const excess = absSample - threshold;
                const compressedExcess = excess / ratio;
                const newLevel = threshold + compressedExcess;
                output[i] = sample >= 0 ? newLevel : -newLevel;
            } else {
                output[i] = sample;
            }
        }
        
        return output;
    }

    /**
     * Adaptive gain control
     * @param {Float32Array} samples - Input samples
     * @returns {Float32Array} Gain-controlled samples
     */
    adaptiveGainControl(samples) {
        const output = new Float32Array(samples.length);
        const agc = this.agcSettings;
        
        for (let i = 0; i < samples.length; i++) {
            const sample = samples[i];
            const level = Math.abs(sample);
            
            // Calculate desired gain
            const targetGain = level > 0 ? agc.targetLevel / level : 1.0;
            
            // Smooth gain changes
            if (targetGain < agc.currentGain) {
                // Attack (fast)
                agc.currentGain += (targetGain - agc.currentGain) * agc.attackTime;
            } else {
                // Release (slow)
                agc.currentGain += (targetGain - agc.currentGain) * agc.releaseTime;
            }
            
            // Limit gain
            agc.currentGain = Math.max(0.1, Math.min(3.0, agc.currentGain));
            
            output[i] = sample * agc.currentGain;
        }
        
        return output;
    }

    /**
     * Voice enhancement (formant enhancement)
     * @param {Float32Array} samples - Input samples
     * @returns {Float32Array} Enhanced samples
     */
    voiceEnhancement(samples) {
        // Simple voice enhancement using emphasis filter
        const output = new Float32Array(samples.length);
        const emphasis = 0.97;
        
        output[0] = samples[0];
        for (let i = 1; i < samples.length; i++) {
            output[i] = samples[i] - emphasis * samples[i-1];
        }
        
        return output;
    }

    /**
     * Advanced de-essing filter to reduce harsh sibilant sounds
     * @param {Float32Array} samples - Input audio samples
     * @returns {Float32Array} De-essed audio samples
     */
    applyDeEssing(samples) {
        try {
            const output = new Float32Array(samples.length);
            const settings = this.deEssingSettings;

            // Simple frequency-selective compression for de-essing
            const lookAhead = Math.min(10, Math.floor(this.sampleRate * 0.001)); // 1ms lookahead

            for (let i = 0; i < samples.length; i++) {
                let highFreqEnergy = 0;
                const windowSize = Math.min(lookAhead, samples.length - i);

                // Calculate high-frequency energy in the lookahead window
                for (let j = 0; j < windowSize; j++) {
                    const sample = samples[i + j];
                    // Simple high-pass filter approximation
                    if (j > 0) {
                        const highFreq = sample - samples[i + j - 1];
                        highFreqEnergy += highFreq * highFreq;
                    }
                }

                highFreqEnergy = Math.sqrt(highFreqEnergy / windowSize);

                // Apply compression if high-frequency energy exceeds threshold
                let processedSample = samples[i];
                if (highFreqEnergy > settings.threshold) {
                    const excess = highFreqEnergy - settings.threshold;
                    const reduction = 1.0 - (excess / settings.ratio);
                    processedSample *= Math.max(0.1, reduction);
                }

                output[i] = processedSample;
            }

            return output;
        } catch (error) {
            console.error('❌ Error in de-essing:', error);
            return samples; // Return original on error
        }
    }

    /**
     * Enable or disable de-essing
     * @param {boolean} enabled - Whether to enable de-essing
     */
    setDeEssingEnabled(enabled) {
        this.deEssingSettings.enabled = enabled;
        console.log(`🎛️ De-essing ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * Get current de-essing status
     * @returns {boolean} Current de-essing enabled status
     */
    isDeEssingEnabled() {
        return this.deEssingSettings.enabled;
    }

    /**
     * Configure de-essing parameters
     * @param {Object} settings - De-essing settings
     */
    configureDeEssing(settings) {
        this.deEssingSettings = { ...this.deEssingSettings, ...settings };
        console.log('🎛️ De-essing configured:', this.deEssingSettings);
    }

    /**
     * Soft limiter to prevent clipping
     * @param {Float32Array} samples - Input samples
     * @returns {Float32Array} Limited samples
     */
    softLimiter(samples) {
        const output = new Float32Array(samples.length);
        const threshold = 0.95;

        for (let i = 0; i < samples.length; i++) {
            const sample = samples[i];
            const absSample = Math.abs(sample);

            if (absSample > threshold) {
                // Soft limiting using tanh
                const sign = sample >= 0 ? 1 : -1;
                output[i] = sign * Math.tanh(absSample) * threshold;
            } else {
                output[i] = sample;
            }
        }

        return output;
    }

    // === AUDIO DEBUGGING AND MONITORING METHODS ===

    /**
     * Analyze audio quality metrics for debugging and monitoring
     * @param {Float32Array} samples - Audio samples to analyze
     * @param {string} label - Label for logging (e.g., 'input', 'output')
     * @returns {Object} Quality metrics
     */
    analyzeAudioQuality(samples, label = 'unknown') {
        try {
            const metrics = {
                sampleCount: samples.length,
                peak: 0,
                rms: 0,
                snr: 0,
                dynamicRange: 0,
                clippingCount: 0,
                silenceRatio: 0,
                spectralCentroid: 0
            };

            let sumSquares = 0;
            let silentSamples = 0;
            const silenceThreshold = 0.001;
            const clippingThreshold = 0.98;

            // Calculate basic metrics
            for (let i = 0; i < samples.length; i++) {
                const sample = Math.abs(samples[i]);

                // Peak detection
                if (sample > metrics.peak) {
                    metrics.peak = sample;
                }

                // RMS calculation
                sumSquares += sample * sample;

                // Clipping detection
                if (sample > clippingThreshold) {
                    metrics.clippingCount++;
                }

                // Silence detection
                if (sample < silenceThreshold) {
                    silentSamples++;
                }
            }

            // Calculate derived metrics
            metrics.rms = Math.sqrt(sumSquares / samples.length);
            metrics.silenceRatio = silentSamples / samples.length;
            metrics.clippingPercentage = (metrics.clippingCount / samples.length) * 100;

            // Estimate SNR (simplified)
            const signalPower = metrics.rms * metrics.rms;
            const noisePower = Math.max(0.0001, signalPower * metrics.silenceRatio);
            metrics.snr = 10 * Math.log10(signalPower / noisePower);

            // Dynamic range (peak to RMS ratio in dB)
            metrics.dynamicRange = 20 * Math.log10(metrics.peak / Math.max(0.0001, metrics.rms));

            // Log detailed metrics in debug mode
            if (this.debugMode) {
                console.log(`🎵 [AudioEnhancer] ${label.toUpperCase()} Quality Analysis:`, {
                    samples: metrics.sampleCount,
                    peak: metrics.peak.toFixed(3),
                    rms: metrics.rms.toFixed(3),
                    snr: metrics.snr.toFixed(1) + ' dB',
                    dynamicRange: metrics.dynamicRange.toFixed(1) + ' dB',
                    clipping: metrics.clippingPercentage.toFixed(1) + '%',
                    silence: (metrics.silenceRatio * 100).toFixed(1) + '%'
                });
            }

            return metrics;
        } catch (error) {
            console.error(`❌ Error analyzing audio quality for ${label}:`, error);
            return null;
        }
    }

    /**
     * Update processing statistics for monitoring
     * @param {number} startTime - Processing start time
     * @param {Object} qualityBefore - Quality metrics before processing
     * @param {Object} qualityAfter - Quality metrics after processing
     */
    updateProcessingStats(startTime, qualityBefore, qualityAfter) {
        try {
            const processingTime = Date.now() - startTime;
            this.processingStats.totalProcessed++;

            // Update average processing time
            const alpha = 0.1; // Smoothing factor
            this.processingStats.averageProcessingTime =
                (1 - alpha) * this.processingStats.averageProcessingTime + alpha * processingTime;

            // Calculate quality improvements
            if (qualityBefore && qualityAfter) {
                const snrImprovement = qualityAfter.snr - qualityBefore.snr;
                const dynamicRangeImprovement = qualityAfter.dynamicRange - qualityBefore.dynamicRange;
                const clippingReduction = qualityBefore.clippingPercentage - qualityAfter.clippingPercentage;

                // Update quality metrics with smoothing
                this.processingStats.qualityMetrics.snrImprovement =
                    (1 - alpha) * this.processingStats.qualityMetrics.snrImprovement + alpha * snrImprovement;
                this.processingStats.qualityMetrics.dynamicRangeImprovement =
                    (1 - alpha) * this.processingStats.qualityMetrics.dynamicRangeImprovement + 
                    alpha * dynamicRangeImprovement;
                this.processingStats.qualityMetrics.clippingReduction =
                    (1 - alpha) * this.processingStats.qualityMetrics.clippingReduction + alpha * clippingReduction;

                if (this.debugMode) {
                    console.log('📊 [AudioEnhancer] Quality Improvements:', {
                        snr: snrImprovement.toFixed(1) + ' dB',
                        dynamicRange: dynamicRangeImprovement.toFixed(1) + ' dB',
                        clippingReduction: clippingReduction.toFixed(1) + '%',
                        processingTime: processingTime.toFixed(2) + ' ms'
                    });
                }
            }

            // Log periodic stats
            if (this.processingStats.totalProcessed % 100 === 0 && this.monitoringEnabled) {
                console.log('📈 [AudioEnhancer] Processing Stats:', {
                    totalProcessed: this.processingStats.totalProcessed,
                    avgProcessingTime: this.processingStats.averageProcessingTime.toFixed(2) + ' ms',
                    avgSnrImprovement: this.processingStats.qualityMetrics.snrImprovement.toFixed(1) + ' dB',
                    avgDynamicRangeImprovement: 
                        this.processingStats.qualityMetrics.dynamicRangeImprovement.toFixed(1) + ' dB',
                    avgClippingReduction: this.processingStats.qualityMetrics.clippingReduction.toFixed(1) + '%'
                });
            }
        } catch (error) {
            console.error('❌ Error updating processing stats:', error);
        }
    }

    /**
     * Get current processing statistics
     * @returns {Object} Processing statistics
     */
    getProcessingStats() {
        return {
            ...this.processingStats,
            debugMode: this.debugMode,
            monitoringEnabled: this.monitoringEnabled,
            deEssingEnabled: this.deEssingSettings.enabled
        };
    }

    /**
     * Reset processing statistics
     */
    resetProcessingStats() {
        this.processingStats = {
            totalProcessed: 0,
            averageProcessingTime: 0,
            qualityMetrics: {
                snrImprovement: 0,
                dynamicRangeImprovement: 0,
                clippingReduction: 0
            }
        };
        console.log('🔄 [AudioEnhancer] Processing statistics reset');
    }

    /**
     * Enable/disable de-essing
     * @param {boolean} enabled - Whether to enable de-essing
     */
    setDeEssingEnabled(enabled) {
        this.deEssingSettings.enabled = enabled;
        console.log(`🔧 [AudioEnhancer] De-essing ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * Check if de-essing is enabled
     * @returns {boolean} De-essing enabled status
     */
    isDeEssingEnabled() {
        return this.deEssingSettings.enabled;
    }

    /**
     * Get audio enhancement configuration
     * @returns {Object} Current configuration
     */
    getConfiguration() {
        return {
            sampleRate: this.sampleRate,
            frameSize: this.frameSize,
            agcSettings: { ...this.agcSettings },
            deEssingSettings: { ...this.deEssingSettings },
            debugMode: this.debugMode,
            monitoringEnabled: this.monitoringEnabled
        };
    }
}

export default AudioEnhancer;
