# Nginx configuration for Twilio Gemini Live API with WebSocket support
# This configuration ensures proper WebSocket handling for Twilio Media Streams

# Backend upstream
upstream twilio_gemini_backend {
    server localhost:3101;
    keepalive 64;
}

server {
    listen 443 ssl http2;
    server_name gemini-api.verduona.com;

    # SSL certificates (update paths as needed)
    ssl_certificate /etc/letsencrypt/live/verduona.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/verduona.com/privkey.pem;
    
    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;
    
    # Logging
    access_log /var/log/nginx/gemini-api.access.log;
    error_log /var/log/nginx/gemini-api.error.log debug;

    # WebSocket endpoints - CRITICAL for Twilio Media Streams
    location ~ ^/(media-stream|media-stream-inbound|test-outbound|test-inbound|local-audio-session|gemini-live)$ {
        proxy_pass http://twilio_gemini_backend;
        
        # WebSocket headers - REQUIRED
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Standard headers
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeouts for long-lived connections
        proxy_connect_timeout 7d;
        proxy_send_timeout 7d;
        proxy_read_timeout 7d;
        
        # Disable buffering for real-time data
        proxy_buffering off;
        proxy_request_buffering off;
        
        # Large buffer sizes for audio data
        proxy_buffer_size 64k;
        proxy_buffers 8 64k;
        proxy_busy_buffers_size 128k;
        
        # Log WebSocket upgrades
        access_log /var/log/nginx/gemini-websocket.access.log;
    }
    
    # Regular HTTP endpoints
    location / {
        proxy_pass http://twilio_gemini_backend;
        
        # Standard proxy headers
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
        
        # Handle large bodies (for file uploads if any)
        client_max_body_size 50M;
    }
    
    # Health check endpoint (no auth required)
    location = /health {
        proxy_pass http://twilio_gemini_backend;
        proxy_set_header Host $host;
        
        # Bypass any authentication for health checks
        proxy_set_header X-Skip-Auth "true";
        
        # Short timeout for health checks
        proxy_connect_timeout 5s;
        proxy_send_timeout 5s;
        proxy_read_timeout 5s;
    }
    
    # Twilio webhook endpoints (no auth required)
    location ~ ^/(incoming-call|call-status|recording-status)$ {
        proxy_pass http://twilio_gemini_backend;
        
        # Standard proxy headers
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Bypass authentication for Twilio webhooks
        proxy_set_header X-Skip-Auth "true";
        
        # Log Twilio webhooks
        access_log /var/log/nginx/twilio-webhooks.access.log;
    }
}

# HTTP to HTTPS redirect
server {
    listen 80;
    server_name gemini-api.verduona.com;
    return 301 https://$server_name$request_uri;
}