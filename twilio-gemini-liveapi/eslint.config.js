export default [
    {
        languageOptions: {
            ecmaVersion: 2022,
            sourceType: 'module',
            globals: {
                console: 'readonly',
                process: 'readonly',
                Buffer: 'readonly',
                __dirname: 'readonly',
                __filename: 'readonly',
                global: 'readonly',
                setTimeout: 'readonly',
                clearTimeout: 'readonly',
                setInterval: 'readonly',
                clearInterval: 'readonly'
            }
        },
        rules: {
            // Error prevention
            'no-unused-vars': ['warn', { 'argsIgnorePattern': '^_' }],
            'no-undef': 'error',
            'no-console': 'off', // We use console for logging
            'no-debugger': 'warn',
            
            // Code quality
            'prefer-const': 'warn',
            'no-var': 'error',
            'eqeqeq': ['error', 'always'],
            'curly': ['error', 'all'],
            
            // Style (relaxed for this project)
            'indent': ['warn', 4],
            'quotes': ['warn', 'single', { 'allowTemplateLiterals': true }],
            'semi': ['warn', 'always'],
            'comma-dangle': ['warn', 'never'],
            
            // Audio processing specific
            'no-magic-numbers': 'off', // Audio processing has many magic numbers
            'max-len': ['warn', { 'code': 120 }],
            'complexity': ['warn', 15] // Audio processing can be complex
        },
        ignores: [
            'node_modules/**',
            'audio-debug/**',
            'data/**',
            '*.min.js'
        ]
    }
];
