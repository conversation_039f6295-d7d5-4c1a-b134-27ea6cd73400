#!/usr/bin/env node

import pkg from '@google/genai';
const { GoogleGenAI } = pkg;
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🧪 Testing Gemini with project SDK...\n');

const API_KEY = process.env.GEMINI_API_KEY;
console.log(`📝 API Key: ${API_KEY}\n`);

try {
    // Use the same initialization as the project
    const geminiClient = new GoogleGenAI({ apiKey: API_KEY });
    console.log('✅ Gemini client initialized\n');

    // Test with text using the Live API
    console.log('🔍 Testing Live API with text...');
    
    let sessionRef = null;
    const session = await geminiClient.live.connect({
        model: 'gemini-2.5-flash-preview-native-audio-dialog',
        callbacks: {
            onopen: async () => {
                console.log('✅ Session opened!');
                
                // Send a text message
                await sessionRef.sendClientContent({
                    turns: [{
                        role: 'user',
                        parts: [{
                            text: 'Say hello and tell me you are working'
                        }]
                    }],
                    turnComplete: true
                });
                
                console.log('📤 Text sent to Gemini');
            },
            onmessage: (message) => {
                if (message.serverContent?.modelTurn?.parts?.[0]?.text) {
                    console.log('📨 Gemini response:', message.serverContent.modelTurn.parts[0].text);
                    console.log('\n✅ SUCCESS! Gemini is responding to text');
                    sessionRef.close();
                    process.exit(0);
                }
            },
            onerror: (error) => {
                console.error('❌ Error:', error);
                process.exit(1);
            },
            onclose: (event) => {
                console.log('🔌 Session closed');
                if (event.code === 1007) {
                    console.error('❌ API Key error:', event.reason);
                }
            }
        }
    });
    
    sessionRef = session;

} catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
}