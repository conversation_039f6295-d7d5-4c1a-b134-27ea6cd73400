'use client';

import { useState, useRef, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { PlayIcon, StopIcon } from '@heroicons/react/24/outline';

interface LocalAudioTesterProps {
  selectedVoice: string;
  selectedModel: string;
  selectedLanguage: string;
  task: string;
  backendUrl: string;
  buttonOnly?: boolean; // New prop for button-only mode
}

interface GeminiSession {
  sendRealtimeInput: (data: { media: { data: string; mimeType: string } }) => void;
  close: () => void;
}

interface MediaData {
  data: string;
  mimeType: string;
  type?: string;
}

export default function LocalAudioTester({
  selectedVoice,
  selectedModel,
  selectedLanguage,
  task,
  backendUrl,
  buttonOnly = false
}: LocalAudioTesterProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [status, setStatus] = useState('Ready to start local testing');
  
  // Audio context and nodes
  const inputAudioContextRef = useRef<AudioContext | null>(null);
  const outputAudioContextRef = useRef<AudioContext | null>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);
  const sourceNodeRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const audioWorkletNodeRef = useRef<AudioWorkletNode | null>(null);
  const gainNodeRef = useRef<GainNode | null>(null);
  
  // Gemini session
  const sessionRef = useRef<GeminiSession | null>(null);
  const wsRef = useRef<WebSocket | null>(null);
  const nextStartTimeRef = useRef(0);
  const audioSourcesRef = useRef<Set<AudioBufferSourceNode>>(new Set());

  // Audio buffering for smooth playback
  const audioBufferRef = useRef<Float32Array[]>([]);
  const bufferTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize audio contexts only when needed
  useEffect(() => {
    // Don't initialize contexts here - do it in startRecording when needed
    return () => {
      cleanup();
    };
  }, []);

  const cleanup = () => {
    // Stop recording
    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => track.stop());
      mediaStreamRef.current = null;
    }

    // Disconnect audio nodes
    if (sourceNodeRef.current) {
      sourceNodeRef.current.disconnect();
      sourceNodeRef.current = null;
    }

    if (audioWorkletNodeRef.current) {
      audioWorkletNodeRef.current.disconnect();
      audioWorkletNodeRef.current = null;
    }

    // Stop all audio sources
    audioSourcesRef.current.forEach(source => {
      try {
        source.stop();
      } catch {
        // Source might already be stopped
      }
    });
    audioSourcesRef.current.clear();

    // Clear audio buffer and timeout
    audioBufferRef.current = [];
    if (bufferTimeoutRef.current) {
      clearTimeout(bufferTimeoutRef.current);
      bufferTimeoutRef.current = null;
    }

    // Close session
    if (sessionRef.current) {
      sessionRef.current.close();
      sessionRef.current = null;
    }
    
    // Close WebSocket
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    
    // Close audio contexts
    if (inputAudioContextRef.current && inputAudioContextRef.current.state !== 'closed') {
      inputAudioContextRef.current.close();
      inputAudioContextRef.current = null;
    }
    if (outputAudioContextRef.current && outputAudioContextRef.current.state !== 'closed') {
      outputAudioContextRef.current.close();
      outputAudioContextRef.current = null;
    }
  };

  const startRecording = async () => {
    if (!task.trim()) {
      toast.error('Please load a campaign script first');
      return;
    }

    // Add global error handlers to catch unhandled errors that might close WebSocket
    const originalOnError = window.onerror;
    const originalOnUnhandledRejection = window.onunhandledrejection;

    window.onerror = (message, source, lineno, colno, error) => {
      console.error('❌ [FRONTEND] Global error caught:', { message, source, lineno, colno, error });
      if (originalOnError) originalOnError.call(window, message, source, lineno, colno, error);
      return true; // Prevent default handling that might close WebSocket
    };

    window.onunhandledrejection = (event) => {
      console.error('❌ [FRONTEND] Unhandled promise rejection:', event.reason);
      if (originalOnUnhandledRejection) originalOnUnhandledRejection.call(window, event);
      event.preventDefault(); // Prevent default handling that might close WebSocket
    };

    try {
      setStatus('Starting test...');

      // 1. Get microphone access
      setStatus('Requesting microphone access...');
      mediaStreamRef.current = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: false,
          autoGainControl: false
        }
      });
      console.log('🎤 Microphone access granted');

      // 2. Initialize audio contexts
      setStatus('Initializing audio contexts...');

      // Create fresh audio contexts - USE BROWSER DEFAULTS (like old working implementation)
      if (!inputAudioContextRef.current || inputAudioContextRef.current.state === 'closed') {
        const AudioContextClass = window.AudioContext || (window as unknown as { webkitAudioContext: typeof AudioContext }).webkitAudioContext;
        inputAudioContextRef.current = new AudioContextClass(); // No forced sample rate
        console.log('🔊 Input audio context created with sample rate:', inputAudioContextRef.current.sampleRate);
      }
      if (!outputAudioContextRef.current || outputAudioContextRef.current.state === 'closed') {
        const AudioContextClass = window.AudioContext || (window as unknown as { webkitAudioContext: typeof AudioContext }).webkitAudioContext;
        outputAudioContextRef.current = new AudioContextClass(); // No forced sample rate  
        console.log('🔊 Output audio context created with sample rate:', outputAudioContextRef.current.sampleRate);
      }

      // Resume contexts if suspended
      if (inputAudioContextRef.current.state === 'suspended') {
        await inputAudioContextRef.current.resume();
        console.log('🔊 Input audio context resumed');
      }
      if (outputAudioContextRef.current.state === 'suspended') {
        await outputAudioContextRef.current.resume();
        console.log('🔊 Output audio context resumed');
      }

      console.log('🔊 Audio contexts initialized:', {
        input: inputAudioContextRef.current.state,
        output: outputAudioContextRef.current.state
      });

      // 3. Create output gain node
      gainNodeRef.current = outputAudioContextRef.current.createGain();
      gainNodeRef.current.gain.value = 1.0;
      gainNodeRef.current.connect(outputAudioContextRef.current.destination);

      // 4. Connect to Gemini backend
      setStatus('Connecting to Gemini...');
      const ws = await new Promise<WebSocket>((resolve, reject) => {
        // Get auth token from URL if present
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');
        
        let wsUrl = `${backendUrl.replace('http', 'ws')}/local-audio-session`;
        // Add token to WebSocket URL if available
        if (token) {
          wsUrl += `?token=${encodeURIComponent(token)}`;
        }
        
        console.log('🔌 Connecting to:', wsUrl);
        const ws = new WebSocket(wsUrl);

        const timeout = setTimeout(() => {
          ws.close();
          reject(new Error('Connection timeout'));
        }, 5000);

        ws.onopen = () => {
          clearTimeout(timeout);
          console.log('✅ WebSocket connected');
          resolve(ws);
        };

        ws.onerror = (error) => {
          clearTimeout(timeout);
          reject(error);
        };
      });

      // Store WebSocket reference
      wsRef.current = ws;

      // 5. Setup WebSocket handlers
      ws.onmessage = async (event) => {
        console.log('📨 [FRONTEND] WebSocket message received, type:', typeof event.data);
        try {
          const data = JSON.parse(event.data);
          console.log('📨 [FRONTEND] Parsed message data:', data.type, 'audio size:', data.audio?.length || 0, 'mimeType:', data.mimeType);
          if (data.type === 'audio' && outputAudioContextRef.current && gainNodeRef.current) {
            console.log('🔊 [FRONTEND] Processing audio response, size:', data.audio?.length || 0, 'mimeType:', data.mimeType);

            try {
              const audioData = new Uint8Array(atob(data.audio).split('').map(c => c.charCodeAt(0)));
              console.log('🔊 [FRONTEND] Decoded audio data, bytes:', audioData.length);

              // SIMPLIFIED AUDIO PROCESSING - like old working implementation
              console.log('🔊 [FRONTEND] Processing audio response, size:', data.audio?.length || 0);

              // Simple approach: Create audio buffer directly without complex resampling
              const audioBuffer = await createAudioBufferSimple(audioData, outputAudioContextRef.current);
              console.log('🔊 [FRONTEND] Created audio buffer, duration:', audioBuffer.duration.toFixed(3), 'seconds');

              // Play immediately for real-time response
              playAudioImmediately(audioBuffer);

              console.log('✅ [FRONTEND] Audio chunk buffered successfully');

              // Test WebSocket connection after audio playback
              setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                  console.log('✅ [FRONTEND] WebSocket still open after audio playback');
                } else {
                  console.error('❌ [FRONTEND] WebSocket closed after audio playback, state:', ws.readyState);
                }
              }, 1000);

            } catch (audioError) {
              console.error('❌ [FRONTEND] Audio processing error:', audioError);
              // Don't close the WebSocket on audio errors - just log and continue
            }
          }
        } catch (error) {
          console.error('❌ [FRONTEND] WebSocket message error:', error);
          // Don't close the WebSocket on message errors - just log and continue
        }
      };

      ws.onclose = (event) => {
        console.log('🔒 [FRONTEND] WebSocket connection closed');
        console.log('🔍 [FRONTEND] Close code:', event.code, 'reason:', event.reason);
        console.log('🔍 [FRONTEND] Was clean:', event.wasClean);
        
        if (event.code === 1006) {
          console.error('❌ [FRONTEND] Code 1006: Abnormal closure - network issue or crash');
          console.error('❌ [FRONTEND] This should NOT happen - frontend should stay connected until user ends call');
          
          // Attempt to reconnect after a short delay
          setTimeout(() => {
            if (isRecording) {
              console.log('🔄 Attempting to reconnect WebSocket...');
              // Restart the recording session to reconnect
              startRecording();
            }
          }, 2000);
        }
      };

      // 6. Send start-session message with configuration
      ws.send(JSON.stringify({
        type: 'start-session',
        voice: selectedVoice,
        model: selectedModel,
        language: selectedLanguage,
        aiInstructions: task
      }));

      // 7. Create session wrapper
      sessionRef.current = {
        sendRealtimeInput: (data: { media: { data: string; mimeType: string } }) => {
          if (ws.readyState === WebSocket.OPEN) {
            const mediaData = data.media as MediaData;
            if (mediaData && 'type' in mediaData && mediaData.type === 'stop') {
              // Send stop message directly
              ws.send(JSON.stringify({ type: 'stop' }));
            } else {
              // Send audio data in the format expected by backend
              ws.send(JSON.stringify({
                type: 'audio-data',
                audioData: data.media.data
              }));
            }
          }
        },
        close: () => ws.close()
      };

      // 8. Setup audio processing using modern AudioWorkletNode
      setStatus('Setting up audio processing...');
      sourceNodeRef.current = inputAudioContextRef.current.createMediaStreamSource(mediaStreamRef.current);

      // Load AudioWorklet processor
      try {
        await inputAudioContextRef.current.audioWorklet.addModule('/api/audio-processor');
        console.log('✅ AudioWorklet module loaded successfully');

        // Create AudioWorkletNode
        audioWorkletNodeRef.current = new AudioWorkletNode(inputAudioContextRef.current, 'audio-processor');
        console.log('✅ AudioWorkletNode created successfully');

        let audioPacketCount = 0;

        // Handle audio data from AudioWorklet
        audioWorkletNodeRef.current.port.onmessage = (event) => {
          if (!isRecording || !sessionRef.current) return;

          const { type, data } = event.data;
          if (type === 'audioData') {
            // Calculate audio level for debugging
            const pcmData = new Int16Array(data.buffer);
            let sum = 0;
            let maxLevel = 0;
            for (let i = 0; i < pcmData.length; i++) {
              const level = Math.abs(pcmData[i] / 32768);
              sum += level;
              maxLevel = Math.max(maxLevel, level);
            }
            const avgLevel = sum / pcmData.length;

            // Log audio levels every 50 packets for debugging
            audioPacketCount++;
            if (audioPacketCount % 50 === 0) {
              console.log(`🎤 Audio levels - Avg: ${avgLevel.toFixed(4)}, Max: ${maxLevel.toFixed(4)}`);
            }

            // Lower threshold for audio detection
            if (maxLevel < 0.001) return; // Skip very quiet audio

            try {
              const base64Audio = btoa(String.fromCharCode(...data));

              sessionRef.current.sendRealtimeInput({
                media: { data: base64Audio, mimeType: 'audio/pcm;rate=16000' }
              });

              if (audioPacketCount % 100 === 0) {
                console.log(`📤 Sent audio packet #${audioPacketCount}, size: ${data.length} bytes`);
              }
            } catch (error) {
              console.error('❌ [AUDIO] Error sending audio data:', error);
              console.error('❌ [AUDIO] Error details:', error instanceof Error ? error.message : String(error));
              // Don't stop recording on audio send errors - just log and continue
              if (audioPacketCount % 50 === 0) {
                console.log('⚠️ [AUDIO] Continuing despite audio send error...');
              }
            }
          }
        };

        // Connect audio processing chain
        sourceNodeRef.current.connect(audioWorkletNodeRef.current);
        audioWorkletNodeRef.current.connect(inputAudioContextRef.current.destination);

      } catch (error) {
        console.error('❌ Error setting up AudioWorklet, falling back to ScriptProcessorNode:', error);

        // Fallback to ScriptProcessorNode if AudioWorklet fails
        const scriptProcessor = inputAudioContextRef.current.createScriptProcessor(4096, 1, 1);
        console.log('📡 Fallback: ScriptProcessor created with buffer size 4096');

        let audioPacketCount = 0;
        scriptProcessor.onaudioprocess = (audioProcessingEvent) => {
          if (!isRecording || !sessionRef.current) return;

          const inputBuffer = audioProcessingEvent.inputBuffer;
          const pcmData = inputBuffer.getChannelData(0);

          // Calculate audio level for debugging
          let sum = 0;
          let maxLevel = 0;
          for (let i = 0; i < pcmData.length; i++) {
            const level = Math.abs(pcmData[i]);
            sum += level;
            maxLevel = Math.max(maxLevel, level);
          }
          const avgLevel = sum / pcmData.length;

          // Log audio levels every 50 packets for debugging
          audioPacketCount++;
          if (audioPacketCount % 50 === 0) {
            console.log(`🎤 Audio levels - Avg: ${avgLevel.toFixed(4)}, Max: ${maxLevel.toFixed(4)}`);
          }

          // Lower threshold for audio detection
          if (maxLevel < 0.001) return; // Skip very quiet audio

          try {
            const int16Array = new Int16Array(pcmData.length);
            for (let i = 0; i < pcmData.length; i++) {
              int16Array[i] = Math.max(-32768, Math.min(32767, pcmData[i] * 32768));
            }

            const uint8Array = new Uint8Array(int16Array.buffer);
            const base64Audio = btoa(String.fromCharCode(...uint8Array));

            sessionRef.current.sendRealtimeInput({
              media: { data: base64Audio, mimeType: 'audio/pcm;rate=16000' }
            });

            if (audioPacketCount % 100 === 0) {
              console.log(`📤 Sent audio packet #${audioPacketCount}, size: ${uint8Array.length} bytes`);
            }
          } catch (error) {
            console.error('❌ Error sending audio:', error);
            setStatus('Error sending audio data');
          }
        };

        sourceNodeRef.current.connect(scriptProcessor);
        scriptProcessor.connect(inputAudioContextRef.current.destination);
        audioWorkletNodeRef.current = scriptProcessor as unknown as AudioWorkletNode;
      }

      console.log('🔗 Audio processing chain connected');

      // 9. Start recording
      setIsRecording(true);
      setIsConnected(true);
      setStatus('🎤 Recording... Speak now!');
      toast.success('Test started - speak now!');
      
    } catch (error) {
      console.error('❌ Error starting recording:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setStatus(`❌ Failed: ${errorMessage}`);
      toast.error(`Failed to start test: ${errorMessage}`);

      // Clean up on error
      cleanup();
      setIsRecording(false);
      setIsConnected(false);
    }
  };

  const completeTurn = () => {
    if (wsRef.current && isRecording) {
      try {
        // Send turn-complete message through WebSocket
        wsRef.current.send(JSON.stringify({ type: 'turn-complete' }));
        setStatus('Turn completed - waiting for AI response...');
        console.log('📤 Turn complete signal sent');
        toast.success('Turn completed - AI is thinking...');
      } catch (error) {
        console.error('❌ Error sending turn complete:', error);
        toast.error('Failed to complete turn');
      }
    }
  };

  const stopRecording = () => {
    setIsRecording(false);
    setStatus('Stopping...');

    // REQUIREMENT C & D: Send stop message to backend for summary and clean session end
    if (sessionRef.current) {
      try {
        // Use the session wrapper to send stop message
        sessionRef.current.sendRealtimeInput({
          media: { type: 'stop', data: '', mimeType: '' } as MediaData
        });
        console.log('🛑 [FRONTEND] Stop message sent to backend for session summary');
      } catch (error) {
        console.error('❌ [FRONTEND] Error sending stop message:', error);
      }
    }

    cleanup();

    setStatus('Stopped');
    setIsConnected(false);
    toast.success('Local audio testing stopped - session summary requested');
  };

  // Play audio chunks immediately for better real-time response
  const playAudioImmediately = (audioBuffer: AudioBuffer) => {
    if (!outputAudioContextRef.current || !gainNodeRef.current) return;

    try {
      // Play audio immediately without buffering for better real-time response
      const source = outputAudioContextRef.current.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(gainNodeRef.current!);

      // CRITICAL FIX: Use currentTime for immediate playback, no artificial delays
      const currentTime = outputAudioContextRef.current.currentTime;
      const startTime = Math.max(nextStartTimeRef.current, currentTime);
      source.start(startTime);
      nextStartTimeRef.current = startTime + audioBuffer.duration;
      audioSourcesRef.current.add(source);

      source.addEventListener('ended', () => {
        try {
          audioSourcesRef.current.delete(source);
          console.log('🔊 [AUDIO] Audio chunk ended, remaining sources:', audioSourcesRef.current.size);
        } catch (error) {
          console.error('❌ [AUDIO] Error during audio cleanup:', error);
        }
      });

      console.log('🔊 [AUDIO] Playing audio chunk immediately, duration:', audioBuffer.duration.toFixed(3), 'startTime:', startTime.toFixed(3));

    } catch (error) {
      console.error('❌ [AUDIO] Error playing audio:', error);
    }
  };

  // Simple audio buffer creation with proper sample rate handling
  const createAudioBufferSimple = async (
    data: Uint8Array,
    ctx: AudioContext
  ): Promise<AudioBuffer> => {
    try {
      // Convert Int16 PCM to Float32
      const dataInt16 = new Int16Array(data.buffer);
      const float32Data = new Float32Array(dataInt16.length);
      for (let i = 0; i < dataInt16.length; i++) {
        float32Data[i] = dataInt16[i] / 32768.0;
      }

      // Gemini sends 24kHz audio, but we need to handle different context sample rates
      const geminSampleRate = 24000;
      const contextSampleRate = ctx.sampleRate;
      
      if (geminSampleRate === contextSampleRate) {
        // No resampling needed
        const buffer = ctx.createBuffer(1, float32Data.length, contextSampleRate);
        buffer.copyToChannel(float32Data, 0);
        console.log('✅ [AUDIO] Buffer created without resampling, duration:', buffer.duration.toFixed(3), 'seconds');
        return buffer;
      } else {
        // Need to resample from 24kHz to context sample rate
        const resampleRatio = contextSampleRate / geminSampleRate;
        const resampledLength = Math.floor(float32Data.length * resampleRatio);
        const resampledData = new Float32Array(resampledLength);
        
        // Simple linear interpolation resampling
        for (let i = 0; i < resampledLength; i++) {
          const sourceIndex = i / resampleRatio;
          const index = Math.floor(sourceIndex);
          const fraction = sourceIndex - index;
          
          if (index < float32Data.length - 1) {
            resampledData[i] = float32Data[index] * (1 - fraction) + float32Data[index + 1] * fraction;
          } else {
            resampledData[i] = float32Data[float32Data.length - 1];
          }
        }
        
        const buffer = ctx.createBuffer(1, resampledLength, contextSampleRate);
        buffer.copyToChannel(resampledData, 0);
        
        console.log(`✅ [AUDIO] Buffer resampled from ${geminSampleRate}Hz to ${contextSampleRate}Hz, duration: ${buffer.duration.toFixed(3)}s`);
        return buffer;
      }
    } catch (error) {
      console.error('❌ [AUDIO] Error in createAudioBufferSimple:', error);
      throw error;
    }
  };

  // Button-only mode: just render a single button
  if (buttonOnly) {
    return (
      <button
        type="button"
        onClick={isRecording ? stopRecording : startRecording}
        disabled={!task.trim() || !selectedVoice || !selectedModel}
        className={`inline-flex items-center gap-2 px-3 py-1 text-sm font-medium rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
          isRecording
            ? "bg-red-100 text-red-800 hover:bg-red-200 dark:bg-red-950/40 dark:text-red-300 dark:hover:bg-red-950/60"
            : "bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-950/40 dark:text-blue-300 dark:hover:bg-blue-950/60"
        }`}
      >
        {isRecording ? (
          <>
            <StopIcon className="h-4 w-4"/>
            Stop Test
          </>
        ) : (
          <>
            <PlayIcon className="h-4 w-4"/>
            Test Script
          </>
        )}
      </button>
    );
  }

  // Full component mode: render the complete testing interface
  return (
    <div className="space-y-6 p-6 border border-gray-200 rounded-lg bg-gray-50">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2 text-gray-700">Local Audio Testing</h3>
        <p className="text-sm text-gray-600 mb-4">
          Test AI voice conversation using your browser&apos;s microphone and speakers
        </p>

        <div className="mb-4">
          <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
            isConnected ? 'bg-green-100 text-green-800' :
            isRecording ? 'bg-blue-100 text-blue-800' :
            'bg-gray-100 text-gray-800'
          }`}>
            {status}
          </span>
        </div>

        <div className="flex justify-center gap-4">
          {!isRecording ? (
            <button
              onClick={startRecording}
              disabled={!task.trim()}
              className="bg-green-600 text-white px-6 py-2 rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              🎤 Start Local Test
            </button>
          ) : (
            <>
              <button
                onClick={completeTurn}
                className="bg-green-600 text-white px-6 py-2 rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                ✅ Complete Turn
              </button>
              <button
                onClick={stopRecording}
                className="bg-red-600 text-white px-6 py-2 rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                ⏹️ Stop Test
              </button>
            </>
          )}
        </div>

        {!task.trim() && (
          <p className="text-sm text-red-600 mt-2">
            Please load a campaign script before starting local testing
          </p>
        )}
      </div>
    </div>
  );
}
