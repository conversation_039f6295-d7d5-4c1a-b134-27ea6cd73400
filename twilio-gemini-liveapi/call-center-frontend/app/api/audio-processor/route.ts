import { NextResponse } from 'next/server';
import { readFileSync } from 'fs';
import { join } from 'path';

export async function GET() {
  try {
    // Read the audio-processor.js file from the public directory
    const filePath = join(process.cwd(), 'public', 'audio-processor.js');
    const fileContent = readFileSync(filePath, 'utf8');
    
    // Return the file with proper headers for AudioWorklet
    return new NextResponse(fileContent, {
      status: 200,
      headers: {
        'Content-Type': 'application/javascript',
        'Cache-Control': 'public, max-age=31536000, immutable',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });
  } catch (error) {
    console.error('Error serving audio-processor.js:', error);
    return new NextResponse('Audio processor not found', { status: 404 });
  }
}
