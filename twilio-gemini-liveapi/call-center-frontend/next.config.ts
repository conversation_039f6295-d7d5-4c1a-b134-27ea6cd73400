/** @type {import('next').NextConfig} */ // Use JSDoc type hint instead
const nextConfig = {
  reactStrictMode: true,
  // Removed basePath to work with Nginx prefix stripping
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },

  async headers() {
    return [
      {
        source: '/(.*)', // Apply headers to all paths
        headers: [
          {
            key: 'X-Frame-Options',
            // Allow framing from both Verduona domains
            value: 'ALLOWALL',
          },
          {
            key: 'Content-Security-Policy',
            // Allow framing from both Verduona domains and AudioWorklet support
            value: `frame-ancestors https://verduona.com https://www.verduona.com; worker-src 'self'; child-src 'self'`,
          },
          {
            key: 'Permissions-Policy',
            // Allow clipboard and other features needed for iframe
            value: 'clipboard-read=*, clipboard-write=*, microphone=*, camera=*, geolocation=*, payment=*',
          },
        ],
      },
      {
        source: '/audio-processor.js', // Specific headers for AudioWorklet module
        headers: [
          {
            key: 'Content-Type',
            value: 'application/javascript',
          },
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ]
  },
};

export default nextConfig;
