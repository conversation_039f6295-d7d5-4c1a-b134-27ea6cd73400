// AudioWorklet processor for microphone input processing
// This replaces the deprecated ScriptProcessorNode
// Handles PCM 16-bit audio format for Gemini Live API

class AudioProcessor extends AudioWorkletProcessor {
  constructor() {
    super();
    // Use smaller buffer size for lower latency (256 samples like the original ScriptProcessorNode)
    this.bufferSize = 256;
    this.buffer = new Float32Array(this.bufferSize);
    this.bufferIndex = 0;
  }

  process(inputs, outputs, parameters) {
    const input = inputs[0];

    if (input && input.length > 0) {
      const inputChannel = input[0];

      // Reduced logging - only log occasionally
      if (this.bufferIndex === 0 && this.frameCount % 100 === 0) {
        console.log('🎙️ AudioWorklet processing audio, frames processed:', this.frameCount);
      }
      this.frameCount = (this.frameCount || 0) + 1;

      // Process input audio data
      for (let i = 0; i < inputChannel.length; i++) {
        this.buffer[this.bufferIndex] = inputChannel[i];
        this.bufferIndex++;

        // When buffer is full, send it to main thread
        if (this.bufferIndex >= this.bufferSize) {
          // Convert Float32Array to Int16Array for Gemini Live API
          const int16Array = new Int16Array(this.bufferSize);
          for (let j = 0; j < this.bufferSize; j++) {
            // Clamp and convert to 16-bit PCM
            int16Array[j] = Math.max(-32768, Math.min(32767, this.buffer[j] * 32768));
          }

          // Convert to Uint8Array for base64 encoding
          const uint8Array = new Uint8Array(int16Array.buffer);

          // Send audio data to main thread (reduced logging)
          if (this.sendCount % 50 === 0) {
            console.log('📤 AudioWorklet sent', this.sendCount, 'audio packets');
          }
          this.sendCount = (this.sendCount || 0) + 1;
          this.port.postMessage({
            type: 'audioData',
            data: uint8Array
          });

          // Reset buffer
          this.bufferIndex = 0;
        }
      }
    } else {
      // Debug: Log when no input is available
      if (this.bufferIndex === 0) {
        console.log('⚠️ AudioWorklet: No input data available');
      }
    }

    // Keep the processor alive
    return true;
  }
}

// Register the processor
registerProcessor('audio-processor', AudioProcessor);
