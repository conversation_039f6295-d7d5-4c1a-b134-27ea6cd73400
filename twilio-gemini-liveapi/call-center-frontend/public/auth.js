/**
 * Client-side JWT Authentication for Twilio Gemini Demo
 * Validates JWT tokens passed via URL parameters
 */

/**
 * Extract JWT token from URL parameters
 */
function getTokenFromURL() {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get('token');
}

/**
 * Decode JWT token (client-side, no verification)
 */
function decodeJWT(token) {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid JWT format');
    }
    
    const payload = parts[1];
    const decoded = JSON.parse(atob(payload.replace(/-/g, '+').replace(/_/g, '/')));
    return decoded;
  } catch (error) {
    console.error('Failed to decode JWT:', error);
    return null;
  }
}

/**
 * Validate JWT token
 */
function validateToken(token) {
  if (!token) {
    return {
      valid: false,
      error: 'No authentication token provided. Please access this demo through the Verduona website.',
      user: null
    };
  }

  const decoded = decodeJWT(token);
  if (!decoded) {
    return {
      valid: false,
      error: 'Invalid authentication token format.',
      user: null
    };
  }

  // Check if token is expired
  const now = Math.floor(Date.now() / 1000);
  if (decoded.exp && decoded.exp < now) {
    return {
      valid: false,
      error: 'Authentication token has expired. Please log in again.',
      user: null
    };
  }

  // Check required fields
  if (!decoded.sub || !decoded.email) {
    return {
      valid: false,
      error: 'Invalid authentication token. Missing user information.',
      user: null
    };
  }

  return {
    valid: true,
    error: null,
    user: {
      id: decoded.sub,
      email: decoded.email,
      role: decoded.user_metadata?.role || 'user',
      aud: decoded.aud,
      exp: decoded.exp
    }
  };
}

/**
 * Show authentication error page
 */
function showAuthError(errorMessage) {
  document.body.innerHTML = `
    <div style="
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      text-align: center;
      padding: 20px;
    ">
      <div style="
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 40px;
        max-width: 500px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      ">
        <h1 style="margin: 0 0 20px 0; font-size: 2.5em; font-weight: 300;">🔒 Authentication Required</h1>
        <p style="margin: 0 0 30px 0; font-size: 1.2em; line-height: 1.6; opacity: 0.9;" id="error-message">
        </p>
        <a href="https://www.verduona.com/demos" style="
          display: inline-block;
          background: rgba(255, 255, 255, 0.2);
          color: white;
          text-decoration: none;
          padding: 15px 30px;
          border-radius: 50px;
          font-weight: 500;
          transition: all 0.3s ease;
          border: 2px solid rgba(255, 255, 255, 0.3);
        " onmouseover="this.style.background='rgba(255, 255, 255, 0.3)'" 
           onmouseout="this.style.background='rgba(255, 255, 255, 0.2)'">
          Go to Verduona Demos
        </a>
      </div>
    </div>
  `;
  // Safely set the error message text content to prevent XSS
  const errorElement = document.getElementById('error-message');
  if (errorElement) {
    errorElement.textContent = errorMessage;
  }
}

/**
 * Initialize authentication
 */
function initAuth() {
  const token = getTokenFromURL();
  const validation = validateToken(token);
  
  if (!validation.valid) {
    console.error('Authentication failed:', validation.error);
    showAuthError(validation.error);
    return false;
  }
  
  console.log('✅ Authentication successful for user:', validation.user.email);
  
  // Store user info globally for the app to use
  window.currentUser = validation.user;
  
  // Show the page content after successful authentication
  document.documentElement.style.display = '';
  
  return true;
}

// Auto-initialize authentication when script loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initAuth);
} else {
  initAuth();
}

// Make functions available globally for browser use
window.initAuth = initAuth;
window.validateToken = validateToken;
window.getTokenFromURL = getTokenFromURL;
