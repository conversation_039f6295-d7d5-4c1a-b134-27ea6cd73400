# Removed obsolete 'version' attribute

services:
  backend:
    image: node:18-alpine # Use a standard Node image
    container_name: "oai-backend"
    working_dir: /app
    volumes:
      - .:/app # Mount backend source code
      - /app/node_modules # Use a volume for node_modules to avoid overwriting
    # Environment variables from .env file will be loaded automatically
    environment:
      NODE_ENV: development # Ensure development mode
      # PORT=3001 will be picked from .env
      # Development URLs like NEXT_PUBLIC_BACKEND_URL and PUBLIC_URL from .env will be used
    command: sh -c "npm install && npm start" # Install deps and run start script
    ports:
      - "3001:3001" # Expose host 3001, map to container 3001 (from .env)
    networks:
      - proxy
    restart: unless-stopped

  frontend:
    image: node:18-alpine # Use a standard Node image
    container_name: "oai-frontend"
    working_dir: /app
    volumes:
      - ./call-center-frontend:/app # Mount frontend source code
      - /app/node_modules # Use a volume for node_modules
      - /app/.next # Use a volume for .next build cache
    environment:
      NODE_ENV: development # Ensure development mode
      # PORT 3000 is the default for 'next dev'
      # NEXT_PUBLIC_BACKEND_URL from .env will be used
    command: sh -c "npm install --legacy-peer-deps && npm run dev" # Install deps and run dev script
    ports:
      - "3000:3000" # Expose host 3000, map to container 3000 (Next dev default)
    networks:
      - proxy
    restart: unless-stopped

# Define the external network used by the main Traefik instance
networks:
  proxy:
    external: true