# Connection Recovery and Context Management System

This document describes the robust connection recovery and context management system implemented for the Twilio Gemini Live API demo to handle API disconnections and resume conversations seamlessly.

## Overview

The system provides automatic recovery from connection failures, context preservation, and health monitoring to ensure uninterrupted voice conversations even when the underlying AI provider APIs experience issues.

## Components

### 1. Context Storage System (`ContextManager`)

**Purpose**: Stores conversation state and session configuration for recovery purposes.

**Features**:
- Saves session context every 10 seconds during active calls
- Stores conversation history, session config, and connection state
- Tracks recovery attempts and interruption status
- Automatic cleanup of old contexts (1 hour retention)

**Key Methods**:
- `saveSessionContext(callSid, context)` - Save current session state
- `getSessionContext(callSid)` - Retrieve stored context
- `markSessionInterrupted(callSid, reason)` - Mark session for recovery
- `canRecover(callSid)` - Check if session is recoverable
- `getRecoveryMessage(callSid)` - Generate AI recovery notification

### 2. Connection Health Monitoring (`ConnectionHealthMonitor`)

**Purpose**: Real-time monitoring of API connection status and failure detection.

**Features**:
- Tracks connection states per session (connecting, connected, disconnected, failed, recovering)
- Monitors consecutive failures and triggers recovery
- Health checks every 30 seconds
- Connection metrics and statistics

**Key Methods**:
- `trackConnection(callSid, state, details)` - Update connection status
- `shouldTriggerRecovery(callSid, connectionState)` - Determine if recovery needed
- `getHealthStatus()` - Get current health metrics
- `pingConnection(callSid, geminiSession)` - Test connection health

### 3. Session Recovery Manager (`SessionRecoveryManager`)

**Purpose**: Automatic session recovery that restores conversation context and resumes sessions.

**Features**:
- Detects API disconnections and initiates recovery
- Recreates Gemini sessions with preserved context
- Sends recovery notifications to AI
- Maximum 3 recovery attempts per session
- 30-second recovery timeout

**Key Methods**:
- `recoverSession(callSid, reason)` - Attempt session recovery
- `recreateGeminiSession(callSid, context, connectionData)` - Rebuild session
- `sendRecoveryNotification(callSid, geminiSession, context)` - Notify AI of recovery
- `getRecoveryStatus(callSid)` - Get recovery status

### 4. Provider Health Dashboard

**Purpose**: Real-time monitoring interface displaying system health and connection metrics.

**Features**:
- Provider status (Gemini API connection)
- Connection health metrics (active, failed, recovered connections)
- Session recovery statistics
- Audio quality metrics
- System performance data
- Auto-refresh every 10 seconds

## API Endpoints

### Health Monitoring
- `GET /api/provider-health` - Overall provider health status
- `GET /api/connection-metrics` - Detailed connection and system metrics
- `GET /api/recovery-status/:callSid` - Recovery status for specific session

### Recovery Management
- `POST /api/trigger-recovery/:callSid` - Manually trigger session recovery

## Recovery Process

### 1. Failure Detection
- Connection health monitor detects failures
- Tracks consecutive failures (triggers recovery after 2 failures)
- Monitors connection timeouts and errors

### 2. Context Preservation
- Session context saved every 10 seconds during active calls
- Conversation history tracked (last 20 messages)
- Session configuration and state preserved

### 3. Recovery Execution
1. Check if session can be recovered (max attempts, time limits)
2. Increment recovery attempt counter
3. Recreate Gemini session with original configuration
4. Send recovery notification to AI with context
5. Resume conversation seamlessly

### 4. AI Notification
The AI receives a recovery message like:
```
[SYSTEM RECOVERY NOTICE] You were briefly disconnected due to connection_failure. 
This is recovery attempt 1. Please continue the conversation naturally from where 
you left off. The customer may not have noticed the interruption.
```

## Configuration

### Recovery Settings
- **Max Recovery Attempts**: 3 per session
- **Recovery Timeout**: 30 seconds
- **Context Retention**: 1 hour
- **Health Check Interval**: 30 seconds
- **Context Save Interval**: 10 seconds

### Failure Thresholds
- **Consecutive Failures**: 2 (triggers recovery)
- **Connection Timeout**: 10 seconds
- **Max Recovery Time**: 5 minutes

## Integration Points

### WebSocket Handlers
- Enhanced error handling in Gemini session callbacks
- Connection close analysis and recovery triggering
- Periodic context saving during active sessions

### Session Management
- Context tracking integrated into connection data
- Recovery status monitoring
- Conversation logging for context preservation

## Testing

Run the test suite to verify the recovery system:

```bash
node test-recovery-system.js
```

Tests include:
- Health endpoint functionality
- Recovery trigger validation
- System status verification

## Monitoring

### Dashboard Metrics
- **Active Connections**: Currently active sessions
- **Failed Connections**: Total failed connection attempts
- **Recovered Connections**: Successfully recovered sessions
- **Stored Contexts**: Sessions with saved recovery context
- **Active Recoveries**: Sessions currently being recovered

### Health Indicators
- 🟢 Green: Healthy/Connected
- 🟡 Yellow: Warning/Recovering
- 🔴 Red: Failed/Disconnected

## Benefits

1. **Seamless User Experience**: Customers don't notice brief API interruptions
2. **Conversation Continuity**: AI maintains context across disconnections
3. **Automatic Recovery**: No manual intervention required
4. **Real-time Monitoring**: Immediate visibility into system health
5. **Proactive Healing**: System detects and resolves issues automatically

## Troubleshooting

### Common Issues
1. **Recovery Fails**: Check max attempts not exceeded, context exists
2. **Context Not Saved**: Verify session is active, intervals running
3. **Health Dashboard Empty**: Ensure API endpoints accessible
4. **Recovery Loops**: Check failure detection thresholds

### Debug Information
- All recovery events logged with detailed context
- Connection state changes tracked
- Recovery attempt details recorded
- Health metrics continuously updated

This system ensures robust, production-ready voice AI conversations that can handle real-world API reliability challenges.
