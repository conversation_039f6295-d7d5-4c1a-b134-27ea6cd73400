module.exports = {
    apps: [
        {
            name: 'twilio-gemini-backend',
            script: 'index.js',
            env: {
                PORT: 3101,
                NODE_ENV: 'production',
                PUBLIC_URL: 'https://gemini-api.verduona.com',
                CORS_ORIGIN: 'https://twilio-gemini.verduona.com'
                // GEMINI_API_KEY should be loaded from .env file
            }
        },
        {
            name: 'twilio-gemini-frontend',
            cwd: './call-center-frontend',
            script: 'npm',
            args: 'start',
            env: {
                PORT: 3011,
                NODE_ENV: 'production',
                NEXT_PUBLIC_BACKEND_URL: 'https://gemini-api.verduona.com'
            }
        }
    ]
};
