# ACTION DEVELOPMENT PLAN: <PERSON><PERSON><PERSON> Live API Project
## Structured for Individual Agent Assignment

Based on comprehensive codebase analysis, this plan structures major issues into discrete tasks suitable for individual coding assistants.

---

## **AGENT TASK #1: Voice Gender Display (Simple)**
**Assignable to: 1 Agent | Estimated Time: 15 minutes**

### Problem Statement
Voice gender information already exists in `src/gemini/voice-manager.js` but is not displayed in the UI. Users need to see gender information (e.g., "Puck, male") in voice selection.

### Files to Modify
- `call-center-frontend/app/components/LocalAudioTester.tsx` (display gender from existing data)

### Current State
Gender information already exists in `src/gemini/voice-manager.js`:
```javascript
'Puck': {
    name: '<PERSON>uck',
    gender: 'Male',        // ← Already available
    characteristics: 'lively, higher tenor'
},
```

### Specific Implementation Tasks

1. **Update frontend to display existing gender data:**
```javascript
// In LocalAudioTester.tsx - use existing voice data with gender
const voiceOptions = voices.map(voice => ({
    value: voice.name,
    label: `${voice.name}, ${voice.gender.toLowerCase()} - ${voice.characteristics}`
}));

// Example result: "Puck, male - lively, higher tenor"
```

2. **No backend changes needed** - gender data already exists in voice-manager.js

### Success Criteria
- [x] Voice selection shows gender (e.g., "Puck, male")
- [x] Uses existing gender data from voice-manager.js
- [x] No new configuration or backend changes
- [x] Simple frontend update only

**Progress:** ✅ Completed. Gender display is already implemented in the UI via
the `voiceDetails` data in `page.tsx`.

---

## **AGENT TASK #2: AI Response Lag Investigation & Fix**  
**Assignable to: 1 Agent | Estimated Time: 4-5 hours**

### Problem Statement
AI response lags of several seconds during conversations. Root cause needs investigation before implementing fixes.

### Phase 1: Investigation (1-2 hours)
**Files to Analyze:**
- `src/session/session-manager.js` (session lifecycle timing)
- `src/gemini/client.js` (API call timing)
- `src/audio/audio-processor.js` (audio processing timing)
- `src/websocket/handlers.js` (message processing timing)
- `src/scripts/script-manager.js` (script loading timing)

### Investigation Tasks
1. **Add timing logs to identify bottlenecks:**
```javascript
// Add to each major component
const startTime = performance.now();
// ... operation ...
const endTime = performance.now();
console.log(`🔍 [TIMING] ${operationName}: ${endTime - startTime}ms`);
```

2. **Measure AI API response times:**
```javascript
// In gemini/client.js
const apiStartTime = performance.now();
await geminiSession.sendRealtimeInput(input);
const apiEndTime = performance.now();
console.log(`🔍 [API] Gemini response time: ${apiEndTime - apiStartTime}ms`);
```

3. **Track end-to-end conversation flow timing:**
- Audio input → processing → AI → response → audio output
- Identify where the seconds of delay occur

4. **Check for blocking operations:**
- Synchronous file I/O
- Unoptimized database queries
- Network timeouts
- Memory garbage collection issues

### Phase 2: Root Cause Identification
**Expected Investigation Results:**
- Identify specific component causing multi-second delays
- Measure actual vs expected timing for each operation
- Document the real bottleneck(s)

### Phase 3: Implementation (2-3 hours)
**Fix implementation will depend on investigation findings - could be:**
- Gemini API configuration issues
- Network latency problems  
- Session state management delays
- Campaign script processing inefficiencies
- Audio buffer accumulation
- WebSocket message queuing
- Or other issues discovered during investigation

### Specific Implementation Tasks

1. **Add to .env:**
```bash
# Audio Performance Settings
ENABLE_REALTIME_MODE=true
AUDIO_ENHANCEMENT=false
AUDIO_DEBUG=true
```

2. **Streamline `src/audio/audio-processor.js`:**
```javascript
convertUlawToPCM(audioBuffer) {
    // Check for real-time mode
    if (process.env.ENABLE_REALTIME_MODE === 'true') {
        return this.basicUlawToPCM(audioBuffer); // Skip heavy enhancement
    }
    // Original heavy processing for non-real-time use
    return this.enhanceAudioQualityAdvanced(audioBuffer);
}

// Add lightweight conversion method
basicUlawToPCM(audioBuffer) {
    const int16Buffer = Buffer.alloc(audioBuffer.length * 2);
    for (let i = 0; i < audioBuffer.length; i++) {
        const ulawSample = audioBuffer[i];
        const pcmSample = this.ULAW_TO_LINEAR[ulawSample];
        int16Buffer.writeInt16LE(pcmSample, i * 2);
    }
    return int16Buffer;
}
```

3. **Remove audio buffering in `src/session/session-manager.js`:**
```javascript
// Replace complex audio forwarding with direct streaming
onmessage: async (message) => {
    const audio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;
    if (audio && audio.data) {
        // Direct forwarding without complex processing
        if (connectionData.twilioWs?.readyState === 1) {
            const converted = this.audioProcessor.convertPCMToUlaw(audio.data);
            connectionData.twilioWs.send(JSON.stringify({
                event: 'media',
                streamSid: connectionData.streamSid,
                media: { payload: converted }
            }));
        }
    }
}
```

4. **Add performance monitoring:**
```javascript
// Add timing logs to track performance improvements
const startTime = performance.now();
// ... audio processing ...
const endTime = performance.now();
console.log(`🎵 Audio processing took ${endTime - startTime}ms`);
```

### Success Criteria
- [ ] Audio processing latency reduced by 50%+
- [ ] Real-time mode works without quality loss for voice calls
- [ ] Audio buffering eliminated
- [ ] Performance monitoring shows improvements
- [ ] All 4 call flows maintain audio quality

---

## **AGENT TASK #3: Campaign Script Loading Optimization**
**Assignable to: 1 Agent | Estimated Time: 2-3 hours**

### Problem Statement
Campaign scripts loaded from filesystem synchronously causing blocking I/O delays during script loading.

### Files to Modify
- `src/scripts/script-manager.js` (implement caching)
- `campaign-script-loader.js` (async optimization)
- `index.js` (startup script preloading)

### Specific Implementation Tasks

1. **Implement script caching in `src/scripts/script-manager.js`:**
```javascript
export class ScriptManager {
    constructor() {
        this.scriptCache = new Map();
        this.cacheTimestamps = new Map();
        this.cacheTimeout = 300000; // 5 minutes
        this.isPreloading = false;
    }

    async preloadScripts() {
        if (this.isPreloading) return;
        this.isPreloading = true;
        
        console.log('🚀 Preloading all campaign scripts...');
        const startTime = Date.now();
        
        try {
            // Preload outbound campaigns (1-6)
            for (let i = 1; i <= 6; i++) {
                const script = await this.loadCampaignScript(i, 'outbound');
                this.scriptCache.set(`outbound-${i}`, script);
                this.cacheTimestamps.set(`outbound-${i}`, Date.now());
            }
            
            // Preload incoming campaigns (1-6) 
            for (let i = 1; i <= 6; i++) {
                const script = await this.loadCampaignScript(i, 'incoming');
                this.scriptCache.set(`incoming-${i}`, script);
                this.cacheTimestamps.set(`incoming-${i}`, Date.now());
            }
            
            const loadTime = Date.now() - startTime;
            console.log(`✅ Preloaded ${this.scriptCache.size} scripts in ${loadTime}ms`);
        } catch (error) {
            console.error('❌ Error preloading scripts:', error);
        } finally {
            this.isPreloading = false;
        }
    }

    getScriptFromCache(scriptId, type = 'outbound') {
        const cacheKey = `${type}-${scriptId}`;
        const timestamp = this.cacheTimestamps.get(cacheKey);
        
        // Check if cache is still valid
        if (timestamp && (Date.now() - timestamp) < this.cacheTimeout) {
            return this.scriptCache.get(cacheKey);
        }
        
        // Cache expired or doesn't exist
        this.scriptCache.delete(cacheKey);
        this.cacheTimestamps.delete(cacheKey);
        return null;
    }
}
```

2. **Update script loading to use cache:**
```javascript
getScriptConfig(scriptId, isIncoming = false) {
    // Try cache first
    const type = isIncoming ? 'incoming' : 'outbound';
    let script = this.getScriptFromCache(scriptId, type);
    
    if (!script) {
        // Load and cache if not found
        script = this.loadCampaignScript(scriptId, type);
        if (script) {
            this.scriptCache.set(`${type}-${scriptId}`, script);
            this.cacheTimestamps.set(`${type}-${scriptId}`, Date.now());
        }
    }
    
    return script ? this.convertToScriptConfig(script) : this.getDefaultConfig();
}
```

3. **Add preloading to startup in `index.js`:**
```javascript
// Add after server setup, before starting
await scriptManager.preloadScripts();
console.log('📋 Script preloading completed');
```

### Success Criteria
- [ ] All scripts preloaded at startup
- [ ] Script loading time reduced from ~100ms to <5ms
- [ ] Cache invalidation works properly
- [ ] Memory usage stays reasonable
- [ ] No blocking I/O during calls

---

## **AGENT TASK #4: Unified Call Flow Audio Handling**
**Assignable to: 1 Agent | Estimated Time: 4-5 hours**

### Problem Statement
The 4 call flows (outbound/inbound × twilio/browser) use different audio processing methods, causing inconsistent behavior and duplicated code.

### Files to Modify
- `src/session/session-manager.js` (unified audio methods)
- `src/websocket/twilio-flow-handler.js` (standardize)
- `src/websocket/local-testing-handler.js` (standardize)
- `src/websocket/session-utils.js` (shared utilities)

### Current Inconsistencies
1. Twilio: `sendAudioToGemini()` with μ-law conversion
2. Browser: `sendBrowserAudioToGemini()` with direct PCM
3. Different error handling per flow
4. Duplicated session management logic

### Specific Implementation Tasks

1. **Create unified audio handler in `src/session/session-manager.js`:**
```javascript
// Replace separate methods with unified approach
async sendUnifiedAudioToGemini(callSid, geminiSession, audioData, sourceType) {
    try {
        console.log(`🎵 [${callSid}] Unified audio send - source: ${sourceType}, size: ${audioData?.length || 0}`);
        
        if (!geminiSession || !audioData) {
            console.warn(`⚠️ [${callSid}] Missing session or audio data`);
            return;
        }

        let processedAudio;
        
        // Handle different source types with same target format
        switch (sourceType) {
            case 'twilio':
                // Convert μ-law to PCM for Gemini
                const pcmBuffer = this.audioProcessor.convertUlawToPCM(audioData);
                const float32Data = this.audioProcessor.pcmToFloat32Array(pcmBuffer);
                const audioBlob = this.audioProcessor.createGeminiAudioBlob(float32Data);
                processedAudio = {
                    mimeType: audioBlob.mimeType,
                    data: audioBlob.data
                };
                break;
                
            case 'browser':
                // Browser already sends PCM, use directly
                processedAudio = {
                    mimeType: 'audio/pcm;rate=16000',
                    data: audioData
                };
                break;
                
            default:
                throw new Error(`Unknown source type: ${sourceType}`);
        }

        // Same API call for all sources
        await geminiSession.sendRealtimeInput({
            media: processedAudio
        });
        
        console.log(`✅ [${callSid}] Unified audio sent successfully`);
        
    } catch (error) {
        console.error(`❌ [${callSid}] Unified audio send error:`, error);
    }
}
```

2. **Update flow handlers to use unified method:**
```javascript
// In twilio-flow-handler.js
const audioData = { payload: base64Audio };
await sessionManager.sendUnifiedAudioToGemini(callSid, geminiSession, audioData, 'twilio');

// In local-testing-handler.js  
await sessionManager.sendUnifiedAudioToGemini(callSid, geminiSession, base64Audio, 'browser');
```

3. **Create shared session utilities in `src/websocket/session-utils.js`:**
```javascript
export class SessionUtils {
    static createSessionConfig(flowType, config) {
        // Unified session configuration for all flows
        return {
            flowType,
            voice: config.voice,
            model: config.model,
            aiInstructions: config.aiInstructions,
            isIncomingCall: flowType.includes('inbound'),
            isTestMode: flowType.includes('test')
        };
    }
    
    static handleSessionError(callSid, error, flowType) {
        // Unified error handling for all flows
        console.error(`❌ [${callSid}] ${flowType} session error:`, error);
        // Common error recovery logic
    }
}
```

### Success Criteria
- [ ] All 4 flows use same audio processing method
- [ ] Identical error handling across flows
- [ ] Session management consistency
- [ ] No functionality regression
- [ ] Reduced code duplication by 60%+

---

## **AGENT TASK #5: WebSocket Message Processing Optimization**
**Assignable to: 1 Agent | Estimated Time: 2-3 hours**

### Problem Statement
Complex JSON parsing and message processing in WebSocket handlers introduces latency that accumulates during conversations.

### Files to Modify
- `src/websocket/handlers.js` (streamline message processing)
- `src/websocket/twilio-flow-handler.js` (optimize)
- `src/websocket/local-testing-handler.js` (optimize)

### Specific Implementation Tasks

1. **Streamline message processing in handlers:**
```javascript
// Replace heavy JSON processing with lightweight approach
ws.onmessage = async (event) => {
    try {
        // Minimize parsing overhead
        const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;
        
        // Direct routing without complex validation
        switch (data.event || data.type) {
            case 'media':
                await this.handleAudioData(data.media?.payload);
                break;
            case 'start':
                await this.handleStreamStart(data.start);
                break;
            case 'stop':
                await this.handleStreamStop();
                break;
            default:
                // Skip unknown messages instead of processing
                break;
        }
    } catch (error) {
        // Minimal error handling to avoid processing delays
        console.error('Message error:', error.message);
    }
};
```

2. **Optimize audio data handling:**
```javascript
async handleAudioData(audioPayload) {
    if (!audioPayload || !this.geminiSession) return;
    
    // Direct send without complex queuing
    await this.sessionManager.sendUnifiedAudioToGemini(
        this.callSid, 
        this.geminiSession, 
        audioPayload, 
        'twilio'
    );
}
```

3. **Remove unnecessary processing steps:**
```javascript
// Before: Complex message validation and transformation
// After: Direct processing with minimal validation
```

### Success Criteria
- [ ] WebSocket message latency reduced by 30%+
- [ ] Simplified message processing pipeline
- [ ] No loss of essential functionality
- [ ] Better error isolation
- [ ] Improved real-time performance

---

## **AGENT TASK #6: Performance Monitoring and Testing**
**Assignable to: 1 Agent | Estimated Time: 2-3 hours**

### Problem Statement
Need comprehensive performance monitoring to validate improvements and catch regressions.

### Files to Modify
- `src/utils/performance-monitor.js` (create new)
- `test/performance.test.js` (create new)
- `src/api/routes.js` (add monitoring endpoints)

### Specific Implementation Tasks

1. **Create performance monitoring system:**
```javascript
// src/utils/performance-monitor.js
export class PerformanceMonitor {
    static metrics = {
        audioProcessingTimes: [],
        scriptLoadTimes: [],
        sessionStartTimes: [],
        messageProcessingTimes: []
    };
    
    static startTimer(operation) {
        return {
            operation,
            startTime: performance.now()
        };
    }
    
    static endTimer(timer) {
        const duration = performance.now() - timer.startTime;
        this.metrics[`${timer.operation}Times`]?.push(duration);
        return duration;
    }
    
    static getStats() {
        const stats = {};
        for (const [key, times] of Object.entries(this.metrics)) {
            if (times.length > 0) {
                stats[key] = {
                    count: times.length,
                    avg: times.reduce((a, b) => a + b, 0) / times.length,
                    min: Math.min(...times),
                    max: Math.max(...times)
                };
            }
        }
        return stats;
    }
}
```

2. **Add performance testing:**
```javascript
// test/performance.test.js
import { test } from 'node:test';
import assert from 'node:assert';

test('Audio processing performance', async () => {
    // Test audio processing time is under threshold
    const maxProcessingTime = 50; // ms
    // Implementation...
});

test('Script loading performance', async () => {
    // Test script loading time improvements
    const maxLoadTime = 10; // ms for cached scripts
    // Implementation...
});
```

3. **Add monitoring API endpoints:**
```javascript
// In src/api/routes.js
fastify.get('/api/performance', async (request, reply) => {
    return PerformanceMonitor.getStats();
});
```

### Success Criteria
- [ ] Performance monitoring system operational
- [ ] All optimization targets met:
  - Audio processing: <50ms per chunk
  - Script loading: <10ms (cached)
  - Session start: <500ms
  - Message processing: <5ms
- [ ] Performance regression tests pass
- [ ] Monitoring dashboard functional

---

## **IMPLEMENTATION PRIORITY ORDER**

### Phase 1: Critical Performance (Immediate Impact)
1. **AGENT TASK #2** - Audio Processing Optimization
2. **AGENT TASK #3** - Script Loading Optimization
3. **AGENT TASK #5** - WebSocket Message Optimization

### Phase 2: System Consistency (High Value)
4. **AGENT TASK #4** - Unified Call Flow Audio Handling
5. **AGENT TASK #1** - Voice Gender Integration

### Phase 3: Monitoring & Validation (Quality Assurance)
6. **AGENT TASK #6** - Performance Monitoring and Testing

---

## **NOTES FOR AGENT ASSIGNMENT**

### Git Workflow - Develop Branch Coordination
**CRITICAL:** All agents must work on the `develop` branch and coordinate git operations:

1. **Before starting work:**
```bash
git checkout develop
git pull origin develop
git status  # Ensure clean working directory
```

2. **During development:**
```bash
git add .
git commit -m "TASK #X: Brief description of changes"
git push origin develop
```

3. **Before implementing fixes:**
```bash
git pull origin develop  # Get latest changes from other agents
# Resolve any conflicts if present
npm test  # Ensure all tests still pass after sync
```

4. **Coordination Rules:**
- **Always pull before pushing** - Get latest changes from other agents
- **Commit frequently** - Small, focused commits with clear messages
- **Test after sync** - Run `npm test` after pulling changes
- **Communicate conflicts** - If merge conflicts occur, coordinate with other agents
- **Branch protection** - Never force push to develop

### Prerequisites
- Each agent should run `npm test` before and after changes
- Use `npm run lint:fix` to maintain code quality
- Test all 4 call flows after audio-related changes
- Monitor memory usage during script caching implementation
- **Git sync before and after each major change**

### Success Validation
Each task includes specific success criteria that must be met before considering the task complete. Agents should validate their changes against both functional requirements and performance targets.

### Communication Between Agents
If multiple agents work simultaneously:
- Coordinate changes to shared files like `src/session/session-manager.js`
- Ensure no conflicts in the audio processing pipeline
- **Use git pull/push frequently** to avoid large merge conflicts
- **Communicate through commit messages** about major changes
- **Test integration** after syncing changes from other agents
