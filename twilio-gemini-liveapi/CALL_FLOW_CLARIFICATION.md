# 🔄 Call Flow Terminology Clarification

## ⚠️ CRITICAL: Understanding "Incoming" vs "Outbound" Calls

### The Confusion Explained

The term **"INCOMING CALL SCRIPT"** is confusing because it depends on **whose perspective** you're taking:

#### From the **App User's Perspective** (Person using the web interface):
- **OUTBOUND CALL**: User makes a call TO someone else (customer, prospect, etc.)
- **INBOUND CALL**: Someone calls the user's Twilio number

#### From the **AI Agent's Perspective** (The LLM):
- **"INCOMING CALL"**: The agent receives a call (but this could be either direction!)

### 🎯 The Real Call Flows

#### Flow 1: App User Makes Outbound Calls
```
App User → Web Interface → "Make Call" → Twilio → Calls Customer → AI Agent answers as "caller"
```
- **App User Perspective**: OUTBOUND call (I'm calling someone)
- **AI Agent Perspective**: "Incoming call" (I'm receiving the call to handle)
- **Current Script Name**: "INCOMING CALL SCRIPT" ❌ (CONFUSING!)
- **Better Script Name**: "OUTBOUND CALL SCRIPT" ✅ or "CALLING SCRIPT" ✅

#### Flow 2: External Person Calls Twilio Number
```
External Person → Calls Twilio Number → AI Agent answers as "receiver"
```
- **App User Perspective**: INBOUND call (someone is calling my number)
- **AI Agent Perspective**: "Incoming call" (I'm receiving the call to handle)
- **Current Script Name**: "INCOMING CALL SCRIPT" ✅ (CORRECT!)

### 🔧 Current System Behavior

#### What "INCOMING CALL SCRIPT" Actually Controls:

1. **When App User makes outbound calls** (Flow 1):
   - AI agent uses "INCOMING CALL SCRIPT" 
   - Agent acts as the **caller** (sales, support, etc.)
   - Agent follows the script to call customers

2. **When external person calls Twilio number** (Flow 2):
   - AI agent uses "INCOMING CALL SCRIPT"
   - Agent acts as the **receiver** (receptionist, support, etc.)
   - Agent answers and helps the caller

### 🎭 Script Types by Use Case

#### For App User Making Outbound Calls (Flow 1):
- **Sales Scripts**: Agent calls prospects to sell
- **Support Scripts**: Agent calls customers for follow-up
- **Survey Scripts**: Agent calls for feedback
- **Appointment Scripts**: Agent calls to schedule

#### For External People Calling In (Flow 2):
- **Reception Scripts**: Agent answers as receptionist
- **Support Scripts**: Agent provides customer service
- **Information Scripts**: Agent provides company info
- **Emergency Scripts**: Agent handles urgent calls

### 📝 Recommended Terminology Changes

#### Current (Confusing):
- "INCOMING CALL SCRIPT" → Used for both flows

#### Better (Clear):
- **"OUTBOUND CALL SCRIPT"** → When app user calls someone
- **"INBOUND CALL SCRIPT"** → When someone calls the Twilio number
- **"AGENT CALLING SCRIPT"** → When agent acts as caller
- **"AGENT ANSWERING SCRIPT"** → When agent acts as receiver

### 🔍 How to Identify Which Flow You're Configuring

#### Questions to Ask:
1. **Who is initiating the call?**
   - App user → Outbound call script needed
   - External person → Inbound call script needed

2. **What is the AI agent's role?**
   - Agent is calling someone → Outbound/calling script
   - Agent is answering a call → Inbound/answering script

3. **What's the business purpose?**
   - Proactive outreach (sales, surveys) → Outbound script
   - Reactive support (answering questions) → Inbound script

### 🎯 Practical Examples

#### Outbound Call Scenarios (App User Calls Someone):
```
"Hi, this is Sarah calling from ABC Company. I'm reaching out because..."
```
- Agent is the **caller**
- Agent has a specific purpose/script to follow
- Agent is proactive

#### Inbound Call Scenarios (Someone Calls Twilio Number):
```
"Hello, thank you for calling ABC Company. This is Sarah, how can I help you?"
```
- Agent is the **receiver**
- Agent responds to caller's needs
- Agent is reactive

### 🔧 Configuration Best Practices

#### When Setting Up Outbound Campaigns:
- Use scripts where agent introduces themselves as calling FROM your company
- Agent should have specific goals (sales, surveys, appointments)
- Agent should be proactive and drive the conversation

#### When Setting Up Inbound Support:
- Use scripts where agent welcomes callers TO your company
- Agent should be helpful and responsive to caller needs
- Agent should ask "How can I help you?" type questions

### 📋 Summary

The key is to always specify **from whose perspective** you're describing the call:

- **App User Perspective**: Outbound = I'm calling someone, Inbound = Someone is calling me
- **AI Agent Perspective**: Always "incoming" because the agent receives all calls

**Recommendation**: Use **App User Perspective** for all documentation and naming to avoid confusion.
