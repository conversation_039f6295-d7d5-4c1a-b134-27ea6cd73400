#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting application in debug mode...${NC}"

# Check if .env file exists
if [ ! -f .env ]; then
  echo -e "${RED}.env file not found. Creating from template...${NC}"
  cp .env.example .env
  echo -e "${YELLOW}Please edit .env file with your credentials before continuing.${NC}"
  exit 1
fi

# Kill any processes running on ports 3000 and 3001
echo -e "${YELLOW}Checking for processes on ports 3000 and 3001...${NC}"

PORT_3000_PID=$(lsof -t -i:3000)
if [ -n "$PORT_3000_PID" ]; then
  echo -e "${YELLOW}Killing process on port 3000 (PID: $PORT_3000_PID)${NC}"
  kill -9 $PORT_3000_PID
else
  echo -e "${GREEN}No process running on port 3000${NC}"
fi

PORT_3001_PID=$(lsof -t -i:3001)
if [ -n "$PORT_3001_PID" ]; then
  echo -e "${YELLOW}Killing process on port 3001 (PID: $PORT_3001_PID)${NC}"
  kill -9 $PORT_3001_PID
else
  echo -e "${GREEN}No process running on port 3001${NC}"
fi

# Start backend with Node.js inspector
echo -e "${YELLOW}Starting backend in debug mode on port 3001...${NC}"
NODE_OPTIONS="--inspect=0.0.0.0:9229" node index.js > backend-debug.log 2>&1 &
BACKEND_PID=$!
echo -e "${GREEN}Backend started with PID: $BACKEND_PID${NC}"
echo -e "${GREEN}Backend debugger available at: chrome://inspect${NC}"

# Wait for backend to start
echo -e "${YELLOW}Waiting for backend to start...${NC}"
MAX_RETRIES=10
RETRY_COUNT=0
BACKEND_READY=false

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
  if curl -s http://localhost:3001 > /dev/null; then
    BACKEND_READY=true
    echo -e "${GREEN}Backend is running!${NC}"
    break
  fi
  echo -e "${YELLOW}Waiting for backend to start (attempt $((RETRY_COUNT+1))/$MAX_RETRIES)...${NC}"
  RETRY_COUNT=$((RETRY_COUNT+1))
  sleep 2
done

if [ "$BACKEND_READY" = false ]; then
  echo -e "${RED}Failed to start backend after $MAX_RETRIES attempts${NC}"
  echo -e "${YELLOW}Check backend-debug.log for errors${NC}"
  exit 1
fi

# Start frontend with debugging enabled
echo -e "${YELLOW}Starting frontend in debug mode on port 3000...${NC}"
cd call-center-frontend && NODE_OPTIONS="--inspect=0.0.0.0:9230" npm run dev > ../frontend-debug.log 2>&1 &
FRONTEND_PID=$!
echo -e "${GREEN}Frontend started with PID: $FRONTEND_PID${NC}"
echo -e "${GREEN}Frontend debugger available at: chrome://inspect${NC}"

# Wait for frontend to start
echo -e "${YELLOW}Waiting for frontend to start...${NC}"
MAX_RETRIES=10
RETRY_COUNT=0
FRONTEND_READY=false

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
  if curl -s http://localhost:3000 > /dev/null; then
    FRONTEND_READY=true
    echo -e "${GREEN}Frontend is running!${NC}"
    break
  fi
  echo -e "${YELLOW}Waiting for frontend to start (attempt $((RETRY_COUNT+1))/$MAX_RETRIES)...${NC}"
  RETRY_COUNT=$((RETRY_COUNT+1))
  sleep 2
done

if [ "$FRONTEND_READY" = false ]; then
  echo -e "${RED}Failed to start frontend after $MAX_RETRIES attempts${NC}"
  echo -e "${YELLOW}Check frontend-debug.log for errors${NC}"
  exit 1
fi

echo -e "${GREEN}Both services are running in debug mode:${NC}"
echo -e "${GREEN}Backend: http://localhost:3001 (Debugger on port 9229)${NC}"
echo -e "${GREEN}Frontend: http://localhost:3000 (Debugger on port 9230)${NC}"
echo -e "${YELLOW}Debug logs are available in backend-debug.log and frontend-debug.log${NC}"
echo -e "${YELLOW}To debug in Chrome, open chrome://inspect in your browser${NC}"
echo -e "${YELLOW}To debug in VS Code, use the 'Debug Full Stack' launch configuration${NC}"
echo -e "${YELLOW}Press Ctrl+C to stop both services${NC}"

# Wait for user to press Ctrl+C
wait