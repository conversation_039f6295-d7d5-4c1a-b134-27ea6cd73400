# Error Handling Gaps Report

## Summary
After reviewing the codebase, I've identified several areas where error handling could be improved. While the code has decent error handling in many places, there are specific gaps that should be addressed for better robustness.

## Identified Error Handling Gaps

### 1. Missing Try-Catch Blocks Around Async Operations

#### Location: `/src/websocket/twilio-flow-handler.js`
- **Line 191-193**: `setTimeout` callback for recovery doesn't have error handling
  ```javascript
  setTimeout(() => {
      recoveryManager.recoverSession(callSid, 'websocket_error', activeConnections);
  }, 1000);
  ```
  **Issue**: If `recoverSession` throws, it will be an unhandled promise rejection.

#### Location: `/src/session/session-manager.js`
- **Lines 88-111**: `setTimeout` callback for sending AI instructions lacks comprehensive error handling
  ```javascript
  setTimeout(() => {
      if (config.aiInstructions && geminiSession) {
          // ... code ...
          try {
              geminiSession.sendClientContent(instructionsPayload);
          } catch (sendError) {
              console.error(`❌ [${callSid}] Error sending AI instructions:`, sendError);
          }
      }
  }, 100);
  ```
  **Issue**: Only synchronous errors are caught; async errors in `sendClientContent` may not be handled.

### 2. Unhandled Promise Rejections

#### Location: `/src/session/lifecycle-manager.js`
- **Line 174**: `lifecycleManager.requestSessionEnd` is called without await or catch
  ```javascript
  lifecycleManager.requestSessionEnd(callSid, connectionData, 'user_close_connection');
  ```
  **Issue**: If this async method rejects, it won't be caught.

#### Location: `/src/websocket/twilio-flow-handler.js`
- **Line 147**: `lifecycleManager.requestSessionEnd` called without error handling
  ```javascript
  await lifecycleManager.requestSessionEnd(callSid, stopConnectionData, 'user_stop_stream');
  ```
  **Issue**: No try-catch around this await.

### 3. Missing Error Handlers in Event Emitters

#### Location: `/src/audio/transcription-manager.js`
- **Lines 84-87**: Error handler doesn't attempt recovery or provide fallback
  ```javascript
  dgConnection.on('error', (error) => {
      console.error(`❌ [${callSid}] Deepgram transcription error:`, error);
      this.activeConnections.delete(callSid);
  });
  ```
  **Issue**: Connection is simply deleted without attempting recovery or notifying dependent systems.

### 4. WebSocket Error Handling Gaps

#### Location: `/src/websocket/handlers.js`
- **Lines 91-101, 104-114, etc.**: Handler functions don't have try-catch wrappers
  ```javascript
  function handleOutboundCall(connection, deps) {
      const { isTestMode } = deps;
      console.log(`📞 [OUTBOUND] Client connected for outbound call (test: ${isTestMode})`);
      
      return handleTwilioFlow(connection, {
          ...deps,
          flowType: 'outbound_call',
          getSessionConfig: () => getOutboundCallConfig(deps),
          isIncomingCall: false
      });
  }
  ```
  **Issue**: If `handleTwilioFlow` throws synchronously, it won't be caught.

### 5. Database Operations Without Proper Error Handling

#### Location: `/src/api/routes.js`
- **Lines 73-74**: Transcription health check without error handling
  ```javascript
  const transcriptionManager = new TranscriptionManager();
  const transcriptionHealth = await transcriptionManager.healthCheck();
  ```
  **Issue**: If `healthCheck` fails, the entire health endpoint fails.

### 6. Missing Cleanup in Error Paths

#### Location: `/src/session/recovery-manager.js`
- **Lines 99-101, 116-119**: Scheduled retries don't check if session still exists
  ```javascript
  setTimeout(() => {
      this.recoverSession(callSid, `retry_after_${reason}`, activeConnections);
  }, 5000);
  ```
  **Issue**: No verification that the session/connection still exists before retry.

### 7. Resource Cleanup Issues

#### Location: `/src/session/lifecycle-manager.js`
- **Line 113**: Error in `checkGeminiSessionHealth` is caught but resources aren't cleaned
  ```javascript
  } catch (error) {
      console.warn(`⚠️ [${sessionId}] Error checking Gemini session health:`, error);
  }
  ```
  **Issue**: If health check fails, the keep-alive continues without addressing the underlying issue.

## Recommendations

### 1. Wrap All Async Operations in Try-Catch
```javascript
// Example fix for setTimeout callbacks
setTimeout(async () => {
    try {
        await recoveryManager.recoverSession(callSid, 'websocket_error', activeConnections);
    } catch (error) {
        console.error(`❌ [${callSid}] Recovery failed:`, error);
        // Implement fallback or cleanup
    }
}, 1000);
```

### 2. Add Error Boundaries for WebSocket Handlers
```javascript
function wrapHandler(handler) {
    return async (connection, deps) => {
        try {
            return await handler(connection, deps);
        } catch (error) {
            console.error('❌ WebSocket handler error:', error);
            // Clean up connection
            if (connection?.socket?.close) {
                connection.socket.close(1011, 'Internal server error');
            }
        }
    };
}
```

### 3. Implement Circuit Breaker Pattern for External Services
```javascript
class CircuitBreaker {
    constructor(threshold = 5, timeout = 60000) {
        this.failureCount = 0;
        this.threshold = threshold;
        this.timeout = timeout;
        this.state = 'closed';
        this.nextAttempt = Date.now();
    }
    
    async execute(fn) {
        if (this.state === 'open' && Date.now() < this.nextAttempt) {
            throw new Error('Circuit breaker is open');
        }
        
        try {
            const result = await fn();
            this.onSuccess();
            return result;
        } catch (error) {
            this.onFailure();
            throw error;
        }
    }
    
    onSuccess() {
        this.failureCount = 0;
        this.state = 'closed';
    }
    
    onFailure() {
        this.failureCount++;
        if (this.failureCount >= this.threshold) {
            this.state = 'open';
            this.nextAttempt = Date.now() + this.timeout;
        }
    }
}
```

### 4. Add Global Error Recovery Middleware
```javascript
fastify.addHook('onError', async (request, reply, error) => {
    console.error('❌ Request error:', {
        url: request.url,
        method: request.method,
        error: error.message,
        stack: error.stack
    });
    
    // Attempt to clean up any resources
    const sessionId = request.params?.callSid || request.body?.callSid;
    if (sessionId && activeConnections.has(sessionId)) {
        // Trigger cleanup
    }
});
```

### 5. Implement Graceful Degradation
- Add fallback mechanisms when external services fail
- Implement retry logic with exponential backoff
- Add timeout handling for all async operations
- Implement health checks before attempting operations

### 6. Add Structured Error Types
```javascript
class RecoverableError extends Error {
    constructor(message, code, canRetry = true) {
        super(message);
        this.code = code;
        this.canRetry = canRetry;
    }
}

class SessionError extends RecoverableError {
    constructor(message, sessionId, code) {
        super(message, code);
        this.sessionId = sessionId;
    }
}
```

## Priority Fixes

1. **High Priority**: Add error handling to WebSocket message handlers and recovery timeouts
2. **High Priority**: Wrap all lifecycle manager async calls in try-catch
3. **Medium Priority**: Add circuit breakers for Gemini and Deepgram connections
4. **Medium Priority**: Implement proper cleanup in all error paths
5. **Low Priority**: Add structured error types and improve error logging

## Affected Files Summary

- `/src/websocket/twilio-flow-handler.js` - 3 issues
- `/src/websocket/handlers.js` - 4 issues  
- `/src/session/session-manager.js` - 2 issues
- `/src/session/lifecycle-manager.js` - 2 issues
- `/src/session/recovery-manager.js` - 2 issues
- `/src/audio/transcription-manager.js` - 1 issue
- `/src/api/routes.js` - 1 issue
- `/index.js` - Already has good error handling

Total: ~15 specific error handling gaps identified