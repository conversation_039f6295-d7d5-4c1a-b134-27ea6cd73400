#!/usr/bin/env node

// Import exactly like the backend does
import { config } from './src/config/config.js';
import { initializeGeminiClient } from './src/gemini/client.js';

console.log('🧪 Testing backend Gemini configuration...\n');

// Test the config loading
console.log('📋 Config loaded:');
console.log(`   - API Key: ${config.auth.gemini.apiKey ? config.auth.gemini.apiKey.substring(0, 20) + '...' : 'NOT SET'}`);
console.log(`   - Default Model: ${config.ai.gemini.defaultModel}`);
console.log(`   - Default Voice: ${config.ai.gemini.defaultVoice}\n`);

// Test the client initialization exactly like the backend
console.log('🤖 Initializing Gemini client...');
const geminiClient = initializeGeminiClient(config.auth.gemini.apiKey);

if (!geminiClient) {
    console.error('❌ Failed to initialize Gemini client');
    process.exit(1);
}

console.log('✅ Gemini client initialized successfully\n');

// Test the Live API connection
console.log('🔍 Testing Live API connection...');

try {
    const session = await geminiClient.live.connect({
        model: config.ai.gemini.defaultModel,
        callbacks: {
            onopen: () => {
                console.log('✅ Session opened successfully!');
                console.log('🎉 Backend configuration is working correctly!');
                session.close();
                process.exit(0);
            },
            onclose: (event) => {
                console.log(`🔌 Session closed: ${event.code} - ${event.reason}`);
                if (event.code === 1007) {
                    console.error('❌ API key validation failed in Live API');
                    console.error('❌ This means the API key is invalid or expired');
                } else if (event.code !== 1000) {
                    console.error('❌ Session closed with unexpected error');
                }
                process.exit(1);
            },
            onerror: (error) => {
                console.error('❌ Session error:', error);
                process.exit(1);
            }
        }
    });
} catch (error) {
    console.error('❌ Error connecting to Live API:', error);
    process.exit(1);
}
