import { describe, test, beforeEach, afterEach } from 'node:test';
import assert from 'node:assert/strict';
import { validateSupabaseAuth, requireAuth } from '../src/middleware/auth-simple.js';
import sinon from 'sinon';

describe('Authentication Middleware Tests', () => {
    let sandbox;
    let mockRequest;
    let mockReply;
    let originalEnv;

    beforeEach(() => {
        sandbox = sinon.createSandbox();
        originalEnv = process.env.NODE_ENV;
        
        // Create mock request and reply objects
        mockRequest = {
            headers: {},
            url: '/api/test',
            method: 'GET',
            ip: '127.0.0.1'
        };
        
        mockReply = {
            code: sandbox.stub().returnsThis(),
            send: sandbox.stub()
        };
    });

    afterEach(() => {
        sandbox.restore();
        process.env.NODE_ENV = originalEnv;
    });

    describe('validateSupabaseAuth', () => {
        test('should skip auth for WebSocket connections', async () => {
            mockRequest.headers.upgrade = 'websocket';
            
            await validateSupabaseAuth(mockRequest, mockReply);
            
            assert.equal(mockReply.code.called, false);
            assert.equal(mockReply.send.called, false);
        });

        test('should skip auth for health check endpoint', async () => {
            mockRequest.url = '/health';
            
            await validateSupabaseAuth(mockRequest, mockReply);
            
            assert.equal(mockReply.code.called, false);
            assert.equal(mockReply.send.called, false);
        });

        test('should reject requests without auth header in production', async () => {
            process.env.NODE_ENV = 'production';
            
            await validateSupabaseAuth(mockRequest, mockReply);
            
            assert.equal(mockReply.code.calledWith(401), true);
            assert.deepEqual(mockReply.send.calledWith({ error: 'Authorization required' }), true);
        });

        test('should allow requests without auth header in development', async () => {
            process.env.NODE_ENV = 'development';
            
            await validateSupabaseAuth(mockRequest, mockReply);
            
            assert.equal(mockReply.code.called, false);
            assert.equal(mockReply.send.called, false);
        });

        test('should reject invalid token formats', async () => {
            mockRequest.headers.authorization = 'Bearer undefined';
            
            await validateSupabaseAuth(mockRequest, mockReply);
            
            assert.equal(mockReply.code.calledWith(401), true);
            assert.deepEqual(mockReply.send.calledWith({ error: 'Unauthorized' }), true);
        });

        test('should reject null tokens', async () => {
            mockRequest.headers.authorization = 'Bearer null';
            
            await validateSupabaseAuth(mockRequest, mockReply);
            
            assert.equal(mockReply.code.calledWith(401), true);
            assert.deepEqual(mockReply.send.calledWith({ error: 'Unauthorized' }), true);
        });

        test('should reject short tokens in production', async () => {
            process.env.NODE_ENV = 'production';
            mockRequest.headers.authorization = 'Bearer shorttoken';
            
            await validateSupabaseAuth(mockRequest, mockReply);
            
            assert.equal(mockReply.code.calledWith(401), true);
            assert.deepEqual(mockReply.send.calledWith({ error: 'Unauthorized' }), true);
        });

        test('should validate against API_KEY if set', async () => {
            process.env.NODE_ENV = 'production';
            process.env.API_KEY = 'test-api-key-that-is-long-enough-12345';
            mockRequest.headers.authorization = 'Bearer wrong-api-key-that-is-long-enough';
            
            await validateSupabaseAuth(mockRequest, mockReply);
            
            assert.equal(mockReply.code.calledWith(401), true);
            assert.deepEqual(mockReply.send.calledWith({ error: 'Unauthorized' }), true);
            
            delete process.env.API_KEY;
        });

        test('should accept valid API_KEY in production', async () => {
            process.env.NODE_ENV = 'production';
            process.env.API_KEY = 'test-api-key-that-is-long-enough-12345';
            mockRequest.headers.authorization = 'Bearer test-api-key-that-is-long-enough-12345';
            
            await validateSupabaseAuth(mockRequest, mockReply);
            
            assert.equal(mockReply.code.called, false);
            assert.equal(mockReply.send.called, false);
            assert.equal(mockRequest.auth.authenticated, true);
            assert.equal(mockRequest.auth.token, 'test-api-key-that-is-long-enough-12345');
            
            delete process.env.API_KEY;
        });

        test('should validate against SUPABASE_SERVICE_KEY as fallback', async () => {
            process.env.NODE_ENV = 'production';
            process.env.SUPABASE_SERVICE_KEY = 'supabase-key-that-is-long-enough-12345';
            mockRequest.headers.authorization = 'Bearer supabase-key-that-is-long-enough-12345';
            
            await validateSupabaseAuth(mockRequest, mockReply);
            
            assert.equal(mockReply.code.called, false);
            assert.equal(mockReply.send.called, false);
            assert.equal(mockRequest.auth.authenticated, true);
            
            delete process.env.SUPABASE_SERVICE_KEY;
        });

        test('should attach auth info to request on success', async () => {
            process.env.NODE_ENV = 'development';
            const token = 'valid-development-token-1234567890';
            mockRequest.headers.authorization = `Bearer ${token}`;
            
            await validateSupabaseAuth(mockRequest, mockReply);
            
            assert.equal(mockRequest.auth.token, token);
            assert.equal(mockRequest.auth.authenticated, true);
        });

        test('should extract token without Bearer prefix correctly', async () => {
            const token = 'test-token-without-bearer-prefix-1234567890';
            mockRequest.headers.authorization = `Bearer ${token}`;
            
            await validateSupabaseAuth(mockRequest, mockReply);
            
            assert.equal(mockRequest.auth.token, token);
        });
    });

    describe('requireAuth', () => {
        test('should call validateSupabaseAuth and proceed on success', (t, done) => {
            process.env.NODE_ENV = 'development';
            mockRequest.headers.authorization = 'Bearer valid-token-1234567890';
            
            requireAuth(mockRequest, mockReply, () => {
                assert.equal(mockReply.code.called, false);
                assert.equal(mockReply.send.called, false);
                done();
            });
        });

        test('should respond with 401 if authentication fails', (t, done) => {
            process.env.NODE_ENV = 'production';
            // No auth header
            
            requireAuth(mockRequest, mockReply, () => {
                assert.equal(mockReply.code.calledWith(401), true);
                assert.deepEqual(mockReply.send.calledWith({ error: 'Authentication required' }), true);
                done();
            });
        });

        test('should handle async errors gracefully', (t, done) => {
            // This will trigger auth failure due to no header in production
            process.env.NODE_ENV = 'production';
            
            requireAuth(mockRequest, mockReply, () => {
                assert.equal(mockReply.code.calledWith(401), true);
                done();
            });
        });
    });

    describe('Edge Cases and Security', () => {
        test('should handle malformed authorization headers', async () => {
            mockRequest.headers.authorization = 'Basic dGVzdDp0ZXN0'; // Basic auth instead of Bearer
            
            await validateSupabaseAuth(mockRequest, mockReply);
            
            // In dev mode it would pass, in prod it would fail
            if (process.env.NODE_ENV === 'production') {
                assert.equal(mockReply.code.calledWith(401), true);
            }
        });

        test('should handle very long tokens gracefully', async () => {
            const longToken = 'a'.repeat(1000);
            mockRequest.headers.authorization = `Bearer ${longToken}`;
            
            await validateSupabaseAuth(mockRequest, mockReply);
            
            // Should pass validation (length check only requires >= 32)
            assert.equal(mockRequest.auth?.token, longToken);
        });

        test('should handle special characters in tokens', async () => {
            const specialToken = 'token-with-special-chars-!@#$%^&*()_+-=[]{}|;:,.<>?';
            mockRequest.headers.authorization = `Bearer ${specialToken}`;
            process.env.NODE_ENV = 'development';
            
            await validateSupabaseAuth(mockRequest, mockReply);
            
            assert.equal(mockRequest.auth?.token, specialToken);
        });

        test('should not leak sensitive information in error messages', async () => {
            process.env.NODE_ENV = 'production';
            process.env.API_KEY = 'super-secret-api-key-12345678901234567890';
            mockRequest.headers.authorization = 'Bearer wrong-key-12345678901234567890';
            
            await validateSupabaseAuth(mockRequest, mockReply);
            
            // Error message should not contain the actual API key
            const errorCall = mockReply.send.getCall(0);
            assert.equal(errorCall.args[0].error, 'Unauthorized');
            assert.equal(errorCall.args[0].error.includes('super-secret'), false);
            
            delete process.env.API_KEY;
        });

        test('should handle missing Bearer prefix', async () => {
            mockRequest.headers.authorization = 'just-a-token-without-prefix';
            
            await validateSupabaseAuth(mockRequest, mockReply);
            
            // Should still try to validate the token
            if (process.env.NODE_ENV === 'production') {
                assert.equal(mockReply.code.calledWith(401), true);
            }
        });
    });

    describe('Integration with API Routes', () => {
        test('should protect API endpoints except health and websocket', async () => {
            const protectedEndpoints = [
                '/api/sessions/active',
                '/configure-call',
                '/make-call',
                '/api/analytics/system',
                '/get-campaign-script/1'
            ];

            for (const endpoint of protectedEndpoints) {
                mockRequest.url = endpoint;
                mockRequest.headers = {}; // No auth
                process.env.NODE_ENV = 'production';
                
                // Reset stubs
                mockReply.code.resetHistory();
                mockReply.send.resetHistory();
                
                await validateSupabaseAuth(mockRequest, mockReply);
                
                assert.equal(mockReply.code.calledWith(401), true, `Endpoint ${endpoint} should be protected`);
            }
        });

        test('should allow public endpoints without auth', async () => {
            const publicEndpoints = [
                '/health'
            ];

            for (const endpoint of publicEndpoints) {
                mockRequest.url = endpoint;
                mockRequest.headers = {}; // No auth
                process.env.NODE_ENV = 'production';
                
                // Reset stubs
                mockReply.code.resetHistory();
                mockReply.send.resetHistory();
                
                await validateSupabaseAuth(mockRequest, mockReply);
                
                assert.equal(mockReply.code.called, false, `Endpoint ${endpoint} should be public`);
            }
        });
    });
});

describe('Frontend Auth Integration', () => {
    describe('AuthGuard Component', () => {
        test('should validate token presence in URL params', () => {
            // This would typically be tested with a frontend testing framework
            // Here we just document the expected behavior
            
            // Expected behavior:
            // 1. Check for 'token' query parameter
            // 2. If no token, redirect to https://verduona.com/demos
            // 3. If token exists, assume nginx has validated it
            // 4. Set isAuthenticated to true
            
            assert.ok(true, 'Frontend auth behavior documented');
        });
    });

    describe('JWT Token Validation', () => {
        test('should decode JWT tokens client-side', () => {
            // The public/auth.js file handles JWT decoding
            // Expected behavior:
            // 1. Extract token from URL
            // 2. Decode JWT payload (no signature verification)
            // 3. Check expiration
            // 4. Extract user info (id, email, role)
            
            assert.ok(true, 'JWT validation behavior documented');
        });
    });
});

describe('Missing Test Coverage', () => {
    test('API endpoints should include auth headers in requests', () => {
        // Currently missing: Frontend API calls don't include Authorization headers
        // This is a gap in the implementation
        
        const missingAuthHeaders = [
            'fetch(`${BACKEND_URL}/available-voices`)',
            'fetch(`${BACKEND_URL}/make-call`)',
            'fetch(`${BACKEND_URL}/update-session-config`)',
            'fetch(`${BACKEND_URL}/call-results/${callSid}`)'
        ];
        
        assert.ok(true, `Missing auth headers in ${missingAuthHeaders.length} API calls`);
    });

    test('Twilio webhook validation should be tested', () => {
        // The validateTwilioWebhook function is used but not tested
        // This is critical for security
        
        assert.ok(true, 'Twilio webhook validation needs test coverage');
    });

    test('Session management auth should be tested', () => {
        // WebSocket connections bypass auth but should have session validation
        
        assert.ok(true, 'WebSocket session auth needs test coverage');
    });
});