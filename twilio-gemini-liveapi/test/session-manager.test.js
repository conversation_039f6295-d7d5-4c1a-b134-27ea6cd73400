import { describe, it, beforeEach } from 'node:test';
import assert from 'node:assert';
import { SessionManager } from '../src/session/session-manager.js';

describe('SessionManager', () => {
    let sessionManager;
    let mockGeminiClient;
    let mockContextManager;
    let mockActiveConnections;

    beforeEach(() => {
        // Mock dependencies
        mockActiveConnections = new Map();
        mockGeminiClient = {
            live: {
                connect: async () => ({
                    sendRealtimeInput: () => Promise.resolve(),
                    close: () => Promise.resolve()
                })
            }
        };
        mockContextManager = {
            createContext: () => ({
                sessionId: 'test-session',
                startTime: Date.now(),
                conversationLog: [],
                fullTranscript: []
            }),
            getContext: () => ({}),
            updateContext: () => {},
            deleteContext: () => {}
        };

        sessionManager = new SessionManager(
            mockContextManager,
            mockGeminiClient,
            mockActiveConnections
        );
    });

    describe('createGeminiSession', () => {
        it('should create a new Gemini session with proper initialization', async () => {
            const callSid = 'test-call-123';
            const sessionConfig = {
                model: 'gemini-2.0-flash-exp',
                voice: 'Aoede',
                scriptId: 1
            };
            const connectionData = {
                callSid,
                sessionId: 'test-session',
                conversationLog: [],
                fullTranscript: []
            };

            const geminiSession = await sessionManager.createGeminiSession(
                callSid,
                sessionConfig,
                connectionData
            );

            assert.ok(geminiSession);
            assert.ok(geminiSession.sendRealtimeInput);
            assert.ok(geminiSession.close);
        });

        it('should handle session creation failure gracefully', async () => {
            const callSid = 'test-call-456';
            const sessionConfig = { model: 'invalid-model' };
            const connectionData = {
                callSid,
                sessionId: 'test-session',
                conversationLog: [],
                fullTranscript: []
            };

            // Mock a failing Gemini client
            mockGeminiClient.live.connect = async () => {
                throw new Error('Connection failed');
            };

            try {
                await sessionManager.createGeminiSession(
                    callSid,
                    sessionConfig,
                    connectionData
                );
                assert.fail('Should have thrown an error');
            } catch (error) {
                // The error might be wrapped or have a different message
                assert.ok(error.message.includes('Connection failed') || 
                         error.message.includes('Failed to create Gemini session') ||
                         error.message.includes('Error'));
            }
        });
    });

    describe('sendAudioToGemini', () => {
        it('should process audio input correctly', async () => {
            const callSid = 'test-call-789';
            const audioData = Buffer.from([1, 2, 3, 4]);
            
            // Create a mock Gemini session
            const mockGeminiSession = {
                sendRealtimeInput: async (input) => {
                    assert.ok(input.media);
                    assert.ok(input.media.data);
                    return Promise.resolve();
                }
            };

            // Test sending audio
            await sessionManager.sendAudioToGemini(callSid, mockGeminiSession, audioData);
        });

        it('should handle invalid session gracefully', async () => {
            const callSid = 'invalid-call';
            const audioData = Buffer.from([1, 2, 3, 4]);

            // Should not throw an error for null session
            await sessionManager.sendAudioToGemini(callSid, null, audioData);
        });
    });

    describe('sendBrowserAudioToGemini', () => {
        it('should handle browser audio correctly', async () => {
            const callSid = 'test-browser-audio';
            const base64Audio = 'dGVzdGF1ZGlv'; // base64 encoded 'testaudio'
            
            // Create a mock Gemini session
            const mockGeminiSession = {
                sendRealtimeInput: async (input) => {
                    assert.ok(input.media);
                    assert.ok(input.media.data);
                    assert.strictEqual(input.media.mimeType, 'audio/pcm;rate=16000');
                    return Promise.resolve();
                }
            };

            // Test sending browser audio
            await sessionManager.sendBrowserAudioToGemini(callSid, mockGeminiSession, base64Audio);
        });
    });

    describe('sendTextToGemini', () => {
        it('should send text messages correctly', async () => {
            const callSid = 'test-text';
            const text = 'Hello, this is a test message';
            
            // Create a mock Gemini session
            const mockGeminiSession = {
                sendRealtimeInput: async (input) => {
                    assert.ok(input.clientContent);
                    assert.ok(input.clientContent.parts);
                    assert.strictEqual(input.clientContent.parts[0].text, text);
                    return Promise.resolve();
                }
            };

            // Test sending text
            await sessionManager.sendTextToGemini(callSid, mockGeminiSession, text);
        });
    });

    describe('error handling', () => {
        it('should handle WebSocket errors gracefully', async () => {
            const callSid = 'test-call-error';
            const audioData = Buffer.from([1, 2, 3, 4]);

            // Mock a session that throws errors
            const mockGeminiSession = {
                sendRealtimeInput: async () => {
                    throw new Error('WebSocket error');
                }
            };

            // Should not throw, should handle gracefully
            await sessionManager.sendAudioToGemini(callSid, mockGeminiSession, audioData);
        });
    });

    describe('memory management', () => {
        it('should limit session metrics storage', () => {
            // SessionManager uses BoundedMap with maxSize 1000
            // Add many sessions to test the limit
            for (let i = 0; i < 1500; i++) {
                sessionManager.sessionMetrics.set(`call-${i}`, {
                    messagesSent: i,
                    lastActivity: Date.now()
                });
            }

            // Should not exceed maxSize
            assert.ok(sessionManager.sessionMetrics.size <= 1000);
        });
    });
});