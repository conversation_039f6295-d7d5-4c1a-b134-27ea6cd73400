version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile.debug
    container_name: twilio-backend-debug
    restart: unless-stopped
    ports:
      - "3001:3001"
      - "9229:9229"  # Node.js inspector port
    environment:
      - PORT=3001
      - NODE_ENV=development
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - TWILIO_PHONE_NUMBER=${TWILIO_PHONE_NUMBER}
      - DEFAULT_PHONE_NUMBER=${DEFAULT_PHONE_NUMBER}
    volumes:
      - .:/app  # Mount the entire project for live reloading
      - /app/node_modules  # Don't override node_modules
    command: node --inspect=0.0.0.0:9229 index.js

  frontend:
    build:
      context: ./call-center-frontend
      dockerfile: Dockerfile.debug
    container_name: twilio-frontend-debug
    restart: unless-stopped
    ports:
      - "3000:3000"
      - "9230:9229"  # Node.js inspector port (mapped to 9230 on host)
    environment:
      - NEXT_PUBLIC_BACKEND_URL=http://localhost:3001
      - NODE_ENV=development
    volumes:
      - ./call-center-frontend:/app  # Mount the frontend directory for live reloading
      - /app/node_modules  # Don't override node_modules
    depends_on:
      - backend
    command: npm run dev