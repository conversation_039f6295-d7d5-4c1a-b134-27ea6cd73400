#!/usr/bin/env node

import twilio from 'twilio';
import dotenv from './src/utils/dotenv-stub.js';

dotenv.config();

const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const client = twilio(accountSid, authToken);

const callSid = process.argv[2] || 'CA8f51cb45be53e635465788d9a4f7e6bd';

async function checkCall() {
    try {
        const call = await client.calls(callSid).fetch();
        
        console.log('Call Details:');
        console.log('-------------');
        console.log(`Status: ${call.status}`);
        console.log(`Duration: ${call.duration} seconds`);
        console.log(`From: ${call.from}`);
        console.log(`To: ${call.to}`);
        console.log(`Direction: ${call.direction}`);
        console.log(`Start Time: ${call.startTime}`);
        console.log(`End Time: ${call.endTime}`);
        console.log(`Answered By: ${call.answeredBy}`);
        console.log(`Price: ${call.price}`);
        
        if (call.status === 'failed' || call.status === 'no-answer' || call.status === 'busy') {
            console.log(`\nCall Failed Reason: ${call.status}`);
        }
        
    } catch (error) {
        console.error('Error fetching call:', error.message);
    }
}

checkCall();