#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Creating .env file for Docker if it doesn't exist...${NC}"
if [ ! -f .env ]; then
  echo -e "${RED}.env file not found. Creating from template...${NC}"
  cp .env.example .env
  echo -e "${YELLOW}Please edit .env file with your credentials before continuing.${NC}"
  exit 1
fi

# Check if proxy network exists, create if not
if ! docker network inspect proxy >/dev/null 2>&1; then
  echo -e "${YELLOW}Creating proxy network...${NC}"
  docker network create proxy
fi

echo -e "${YELLOW}Building and starting Docker containers...${NC}"
docker-compose up --build -d

echo -e "${YELLOW}Waiting for services to start...${NC}"
sleep 5

echo -e "${YELLOW}Checking container status...${NC}"
if [ "$(docker ps -q -f name=twilio-backend)" ]; then
  echo -e "${GREEN}Backend container is running!${NC}"
else
  echo -e "${RED}Backend container failed to start. Check docker logs with: docker logs twilio-backend${NC}"
fi

if [ "$(docker ps -q -f name=twilio-frontend)" ]; then
  echo -e "${GREEN}Frontend container is running!${NC}"
else
  echo -e "${RED}Frontend container failed to start. Check docker logs with: docker logs twilio-frontend${NC}"
fi

echo -e "${GREEN}Services are deployed with Traefik and available at:${NC}"
echo -e "${GREEN}Backend API: https://api.verduona.site${NC}"
echo -e "${GREEN}Frontend: https://www.verduona.site${NC}"
echo -e "${YELLOW}Note: It may take a few minutes for DNS to propagate and SSL certificates to be issued.${NC}"