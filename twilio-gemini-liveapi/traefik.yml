# traefik.yml (Static Configuration)

# Enable API and Dashboard (optional, useful for debugging)
# api:
#   insecure: true # Set to false in production and configure authentication
#   dashboard: true

# Define Entrypoints
entryPoints:
  web:
    address: ":80"
    # Redirect HTTP to HTTPS
    http:
      redirections:
        entryPoint:
          to: websecure
          scheme: https

  websecure:
    address: ":443"
    # Enable TLS
    http:
      tls:
        certResolver: letsencrypt

# Define Certificate Resolver (Let's Encrypt)
certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL> # Replace with your email
      storage: acme.json # Path to store ACME certificates
      # Use HTTP challenge for certificate validation
      httpChallenge:
        entryPoint: web

# Define Providers (File provider for dynamic configuration)
providers:
  file:
    directory: /home/<USER>/github/twilio-openai-realapi/config # Path to the directory containing dynamic configuration
    watch: true # Watch for changes in the dynamic configuration files