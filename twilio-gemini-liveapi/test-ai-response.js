#!/usr/bin/env node

import WebSocket from 'ws';

console.log('🧪 Testing AI Response Generation...');

const ws = new WebSocket('ws://localhost:3000/local-audio-session');
let sessionStarted = false;
let aiResponded = false;

ws.on('open', () => {
    console.log('✅ WebSocket connected');
    
    // Send session start
    console.log('📤 Starting session...');
    ws.send(JSON.stringify({
        type: 'start-session',
        voice: 'Kore',
        model: 'gemini-2.5-flash-preview-native-audio-dialog',
        scriptType: 'incoming',
        scriptId: 'incoming-1',
        targetName: 'Test User',
        targetPhoneNumber: '+420733154483',
        isTestMode: true
    }));
});

ws.on('message', (data) => {
    try {
        const message = JSON.parse(data);
        console.log(`📨 Received: ${message.type}`);
        
        if (message.type === 'session-started') {
            sessionStarted = true;
            console.log('✅ Session started! Now sending voice prompt...');
            
            // Wait a moment then send a clear voice prompt
            setTimeout(() => {
                console.log('🎤 Sending voice prompt: "Hello, can you hear me?"');
                
                // Create a simple audio buffer (simulating voice)
                const audioData = Buffer.alloc(1600, 0); // 100ms of silence as placeholder
                // Add some variation to simulate voice
                for (let i = 0; i < audioData.length; i += 2) {
                    audioData.writeInt16LE(Math.sin(i * 0.01) * 1000, i);
                }
                
                ws.send(JSON.stringify({
                    type: 'audio-data',
                    audioData: audioData.toString('base64')
                }));
                
                // Send multiple audio chunks to simulate continuous speech
                let chunkCount = 0;
                const sendChunks = setInterval(() => {
                    chunkCount++;
                    if (chunkCount > 10) {
                        clearInterval(sendChunks);
                        console.log('🔚 Finished sending voice prompt');
                        return;
                    }
                    
                    // Vary the audio to simulate speech
                    for (let i = 0; i < audioData.length; i += 2) {
                        audioData.writeInt16LE(Math.sin(i * 0.01 * chunkCount) * 500, i);
                    }
                    
                    ws.send(JSON.stringify({
                        type: 'audio-data',
                        audioData: audioData.toString('base64')
                    }));
                }, 100);
                
            }, 1000);
        } else if (message.type === 'audio-response' || message.type === 'ai-audio') {
            aiResponded = true;
            console.log('🎉 AI RESPONDED WITH AUDIO!');
            console.log(`   Audio size: ${message.audioData ? message.audioData.length : 'N/A'} bytes`);
        } else if (message.type === 'ai-text' || message.text) {
            aiResponded = true;
            console.log('💬 AI RESPONDED WITH TEXT!');
            console.log(`   Text: ${message.text || message.content || 'N/A'}`);
        } else {
            console.log(`   Full message:`, message);
        }
    } catch (error) {
        console.log(`⚠️ Error parsing message: ${error.message}`);
        console.log(`   Raw data: ${data.toString().substring(0, 200)}...`);
    }
});

ws.on('close', (code, reason) => {
    console.log(`🔌 WebSocket closed - Code: ${code}, Reason: ${reason}`);
    
    console.log('\n📊 Test Results:');
    console.log(`✅ Session Started: ${sessionStarted ? 'YES' : 'NO'}`);
    console.log(`🤖 AI Responded: ${aiResponded ? 'YES' : 'NO'}`);
    
    if (sessionStarted && !aiResponded) {
        console.log('\n❌ ISSUE IDENTIFIED: Session starts but AI does not respond');
        console.log('   This suggests the audio is being sent to Gemini but no response is generated');
        console.log('   Possible causes:');
        console.log('   1. Gemini session configuration issue');
        console.log('   2. Audio format/encoding problem');
        console.log('   3. AI response handling issue');
        console.log('   4. Missing response modality configuration');
    } else if (!sessionStarted) {
        console.log('\n❌ ISSUE: Session failed to start');
    } else {
        console.log('\n🎉 SUCCESS: AI is responding!');
    }
});

ws.on('error', (error) => {
    console.log(`❌ WebSocket error: ${error.message}`);
});

// Auto-close after 15 seconds
setTimeout(() => {
    if (ws.readyState === WebSocket.OPEN) {
        console.log('\n⏰ Test timeout - closing connection');
        ws.close();
    }
}, 15000);
