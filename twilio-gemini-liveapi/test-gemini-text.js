#!/usr/bin/env node

import pkg from '@google/genai';
const { GoogleGenAI } = pkg;
import dotenv from 'dotenv';

dotenv.config();

const API_KEY = process.env.GEMINI_API_KEY;
console.log(`📝 Testing with API Key: ${API_KEY}\n`);

async function test() {
    try {
        const client = new GoogleGenAI({ apiKey: API_KEY });
        console.log('✅ Client initialized');
        
        const session = await client.live.connect({
            model: 'gemini-2.5-flash-preview-native-audio-dialog',
            callbacks: {
                onopen: () => console.log('✅ Session opened'),
                onmessage: (msg) => {
                    if (msg.serverContent?.modelTurn?.parts?.[0]?.text) {
                        console.log('📨 Response:', msg.serverContent.modelTurn.parts[0].text);
                        console.log('\n✅ API KEY WORKS!');
                        process.exit(0);
                    }
                },
                onerror: (e) => {
                    console.error('❌ Error:', e);
                    process.exit(1);
                },
                onclose: (e) => {
                    console.log('Session closed:', e.code, e.reason);
                    if (e.code === 1007) {
                        console.error('❌ API KEY INVALID!');
                        process.exit(1);
                    }
                }
            }
        });
        
        // Send message after session is created
        console.log('📤 Sending text...');
        await session.sendClientContent({
            turns: [{
                role: 'user',
                parts: [{ text: 'Say "Hello, I am working!"' }]
            }],
            turnComplete: true
        });
        
        // Wait for response
        setTimeout(() => {
            console.log('⏰ Timeout - no response');
            process.exit(1);
        }, 5000);
        
    } catch (error) {
        console.error('❌ Error:', error);
        process.exit(1);
    }
}

test();